<?php
declare(strict_types=1);

namespace app\common\model;

use think\Model;

/**
 * 上传文件模型
 */
class Upload extends Model
{
    protected $table = 'fs_upload';
    protected $pk = 'id';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 字段类型转换
    protected $type = [
        'file_size' => 'integer',
        'upload_user_id' => 'integer',
    ];

    /**
     * 文件大小获取器
     * @param $value
     * @return string
     */
    public function getFileSizeTextAttr($value): string
    {
        $size = $this->file_size;
        if ($size < 1024) {
            return $size . 'B';
        } elseif ($size < 1024 * 1024) {
            return round($size / 1024, 2) . 'KB';
        } elseif ($size < 1024 * 1024 * 1024) {
            return round($size / 1024 / 1024, 2) . 'MB';
        } else {
            return round($size / 1024 / 1024 / 1024, 2) . 'GB';
        }
    }

    /**
     * 文件URL获取器
     * @param $value
     * @return string
     */
    public function getUrlAttr($value): string
    {
        return '/storage/' . $this->file_path;
    }
}
