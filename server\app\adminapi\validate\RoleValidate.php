<?php
declare(strict_types=1);

namespace app\adminapi\validate;

use think\Validate;

/**
 * 角色验证器
 */
class RoleValidate extends Validate
{
    protected $rule = [
        'roleName' => 'require|length:2,50',
        'roleCode' => 'require|length:2,50',
        'des' => 'length:0,255',
        'enable' => 'boolean',
    ];

    protected $message = [
        'roleName.require' => '角色名称不能为空',
        'roleName.length' => '角色名称长度必须在2-50个字符之间',
        'roleCode.require' => '角色编码不能为空',
        'roleCode.length' => '角色编码长度必须在2-50个字符之间',
        'roleCode.unique' => '角色编码已存在',
        'des.length' => '角色描述长度不能超过255个字符',
        'enable.boolean' => '启用状态必须是布尔值',
    ];

    protected $scene = [
        'create' => ['roleName', 'roleCode', 'des', 'enable'],
        'update' => ['roleName', 'roleCode', 'des', 'enable'],
    ];
}
