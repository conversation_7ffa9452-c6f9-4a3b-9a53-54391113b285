# 富文本编辑器组件 (RichTextEditor)

基于 WangEditor 封装的通用富文本编辑器组件，支持多种模式和自定义配置。

## 功能特性

- 🎨 **多种模式**: 支持文章模式、公告模式、简单模式
- 🛠️ **自定义工具栏**: 根据不同模式自动配置工具栏
- 📷 **图片上传**: 内置图片上传功能
- 📱 **响应式设计**: 适配移动端和桌面端
- 🎯 **易于集成**: 支持 v-model 双向绑定
- 🔧 **高度可配置**: 支持自定义高度、占位符等

## 基础用法

### 在表单中使用

```vue
<template>
  <el-form>
    <el-form-item label="文章内容">
      <RichTextEditor
        v-model="content"
        mode="article"
        height="500px"
        placeholder="请输入文章内容..."
      />
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref } from 'vue'
import RichTextEditor from '@/components/common/rich-text-editor/index.vue'

const content = ref('')
</script>
```

### 在 CrudListPage 中使用

```javascript
// 表单配置
form: {
  items: [
    {
      prop: 'content',
      label: '内容',
      type: 'editor',
      required: true,
      placeholder: '请输入内容',
      config: {
        height: '400px',
        mode: 'article'
      },
      rules: [
        { required: true, message: '请输入内容', trigger: 'blur' }
      ]
    }
  ]
}
```

## 模式说明

### 1. 文章模式 (article)
- **适用场景**: 文章编辑、博客内容、详细描述
- **工具栏**: 完整的编辑工具，包括标题、格式、列表、表格、代码块等
- **高度**: 默认 500px

```vue
<RichTextEditor
  v-model="content"
  mode="article"
  height="500px"
/>
```

### 2. 公告模式 (notice)
- **适用场景**: 公告、通知、简单富文本内容
- **工具栏**: 精简的编辑工具，去除复杂功能
- **高度**: 默认 400px

```vue
<RichTextEditor
  v-model="content"
  mode="notice"
  height="400px"
/>
```

### 3. 简单模式 (simple)
- **适用场景**: 评论、简短内容、基础格式化
- **工具栏**: 最基础的编辑工具
- **高度**: 默认 200px

```vue
<RichTextEditor
  v-model="content"
  mode="simple"
  height="200px"
/>
```

## Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | string | '' | 编辑器内容，支持 v-model |
| height | string | '400px' | 编辑器高度 |
| placeholder | string | '请输入内容...' | 占位符文本 |
| mode | string | 'article' | 编辑器模式：article/notice/simple |
| disabled | boolean | false | 是否禁用 |
| uploadConfig | object | - | 上传配置 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| blur | (value: string) | 失去焦点时触发 |
| focus | (value: string) | 获得焦点时触发 |

## 上传配置

```vue
<RichTextEditor
  v-model="content"
  :upload-config="{
    maxFileSize: 5 * 1024 * 1024, // 5MB
    maxNumberOfFiles: 10,
    server: '/api/upload/image'
  }"
/>
```

## 高级用法

### 自定义工具栏

```vue
<RichTextEditor
  v-model="content"
  :toolbar-keys="[
    'bold', 'italic', 'underline', '|',
    'bulletedList', 'numberedList', '|',
    'insertLink', 'insertImage'
  ]"
/>
```

### 监听编辑器事件

```vue
<template>
  <RichTextEditor
    v-model="content"
    @focus="handleFocus"
    @blur="handleBlur"
  />
</template>

<script setup>
const handleFocus = (value) => {
  console.log('编辑器获得焦点:', value)
}

const handleBlur = (value) => {
  console.log('编辑器失去焦点:', value)
}
</script>
```

## 样式定制

组件支持通过 CSS 变量进行样式定制：

```css
.rich-text-editor {
  --editor-border-color: #dcdfe6;
  --editor-border-hover-color: #c0c4cc;
  --editor-border-focus-color: #409eff;
}
```

## 注意事项

1. **内容格式**: 编辑器输出 HTML 格式内容
2. **图片上传**: 需要配置后端上传接口
3. **响应式**: 在小屏幕设备上工具栏会自动换行
4. **性能**: 大量文本时建议使用懒加载
5. **安全**: 输出内容需要进行 XSS 过滤

## 更新日志

- v1.0.0: 初始版本，支持三种模式
- v1.1.0: 添加自定义工具栏支持
- v1.2.0: 优化响应式设计和样式
