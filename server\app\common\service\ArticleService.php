<?php
declare(strict_types=1);

namespace app\common\service;

use app\common\model\Article;
use think\facade\Db;
use think\exception\ValidateException;

/**
 * 文章管理服务类
 */
class ArticleService
{
    /**
     * 获取列表
     * @param array $params 查询参数
     * @return array
     */
    public static function getList(array $params = []): array
    {
        $page = max(1, (int)($params['current'] ?? 1));
        $limit = max(1, min(100, (int)($params['size'] ?? 20))); // 限制每页最大100条
        
        $query = Article::order('sort desc, id desc');
        
        // 搜索条件
        if (!empty($params['title'])) {
            $query->whereLike('title', '%' . $params['title'] . '%');
        }
        
        if (isset($params['status']) && $params['status'] !== '') {
            $query->where('status', $params['status']);
        }
        
        if (isset($params['is_published']) && $params['is_published'] !== '') {
            $query->where('is_published', $params['is_published']);
        }
        
        if (isset($params['is_featured']) && $params['is_featured'] !== '') {
            $query->where('is_featured', $params['is_featured']);
        }
        
        if (!empty($params['author_name'])) {
            $query->whereLike('author_name', '%' . $params['author_name'] . '%');
        }
        
        $result = $query->paginate([
            'list_rows' => $limit,
            'page' => $page
        ]);
        
        return [
            'list' => $result->items(),
            'total' => $result->total(),
            'current' => $page,
            'size' => $limit
        ];
    }

    /**
     * 获取详情
     * @param int $id ID
     * @return Article
     */
    public static function getDetail(int $id): Article
    {
        // 先尝试查找包含软删除的记录
        $modelWithTrashed = Article::withTrashed()->find($id);

        $model = Article::find($id);

        if (!$model) {
            // 如果正常查询找不到，但软删除查询能找到，说明记录已被软删除
            if ($modelWithTrashed) {
                throw new \Exception('文章已被删除，无法再次删除');
            }
            throw new \Exception('文章不存在');
        }
        return $model;
    }

    /**
     * 创建
     * @param array $data 数据
     * @return Article
     */
    public static function create(array $data): Article
    {
        Db::startTrans();
        try {
            // 设置默认值
            $data['status'] = $data['status'] ?? 1; // 默认启用

            $model = new Article();
            $model->save($data);

            Db::commit();
            return $model;
        } catch (\Exception $e) {
            Db::rollback();
            throw new \Exception('创建失败：' . $e->getMessage());
        }
    }

    /**
     * 更新
     * @param int $id ID
     * @param array $data 更新数据
     * @return Article
     */
    public static function update(int $id, array $data): Article
    {
        $model = self::getDetail($id);

        Db::startTrans();
        try {
            $model->save($data);

            Db::commit();
            return $model;
        } catch (\Exception $e) {
            Db::rollback();
            throw new \Exception('更新失败：' . $e->getMessage());
        }
    }

    /**
     * 删除
     * @param int $id ID
     * @return bool
     */
    public static function delete(int $id): bool
    {
        $model = self::getDetail($id);
        
        Db::startTrans();
        try {
            $model->delete();
            
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            throw new \Exception('删除失败：' . $e->getMessage());
        }
    }

    /**
     * 批量删除
     * @param array $ids ID数组
     * @return bool
     */
    public static function batchDelete(array $ids): bool
    {
        if (empty($ids)) {
            throw new ValidateException('请选择要删除的数据');
        }
        
        Db::startTrans();
        try {
            Article::destroy($ids);
            
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            throw new \Exception('批量删除失败：' . $e->getMessage());
        }
    }

    /**
     * 更新状态
     * @param int $id ID
     * @param int $status 状态值
     * @return bool
     */
    public static function updateStatus(int $id, int $status): bool
    {
        $model = self::getDetail($id);
        
        if (!in_array($status, [1, 2])) {
            throw new ValidateException('状态值无效');
        }
        
        return $model->save(['status' => $status]);
    }

    /**
     * 发布/取消发布文章
     * @param int $id ID
     * @param int $isPublished 发布状态
     * @return bool
     */
    public static function updatePublishStatus(int $id, int $isPublished): bool
    {
        $model = self::getDetail($id);
        
        if (!in_array($isPublished, [0, 1])) {
            throw new ValidateException('发布状态值无效');
        }
        
        if ($isPublished == 1) {
            return $model->publish();
        } else {
            return $model->unpublish();
        }
    }
}
