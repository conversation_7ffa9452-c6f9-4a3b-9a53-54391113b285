<?php
declare(strict_types=1);

namespace app\adminapi\controller;

use app\common\controller\BaseController;
use app\common\model\Role as RoleModel;
use app\common\model\Menu;
use app\common\model\Permission;
use app\adminapi\validate\RoleValidate;
use think\facade\Db;

/**
 * 角色管理控制器
 */
class Role extends BaseController
{
    /**
     * 获取角色列表
     */
    public function list()
    {
        try {
            $roles = RoleModel::order('id asc')->select();
            
            $result = $roles->map(function($role) {
                return [
                    'id' => $role->id,
                    'roleName' => $role->role_name,
                    'roleCode' => $role->role_code,
                    'des' => $role->description,
                    'date' => $role->created_at,
                    'enable' => (bool)$role->status
                ];
            })->toArray();
            
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 创建角色
     */
    public function create()
    {
        $data = $this->request->post();

        $validate = new RoleValidate();
        if (!$validate->scene('create')->check($data)) {
            return $this->error($validate->getError());
        }

        try {
            // 检查角色编码是否已存在
            $existingRole = RoleModel::where('role_code', $data['roleCode'])->find();
            if ($existingRole) {
                return $this->error('角色编码已存在');
            }

            $role = new RoleModel();
            $role->save([
                'role_name' => $data['roleName'],
                'role_code' => $data['roleCode'],
                'description' => $data['des'] ?? '',
                'status' => $data['enable'] ? 1 : 0
            ]);

            return $this->success($role, '创建成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 更新角色
     */
    public function update()
    {
        $id = $this->request->param('id');
        $data = $this->request->post();

        $validate = new RoleValidate();
        if (!$validate->scene('update')->check($data)) {
            return $this->error($validate->getError());
        }

        try {
            $role = RoleModel::find($id);
            if (!$role) {
                return $this->error('角色不存在');
            }

            // 检查角色编码是否已被其他角色使用
            $existingRole = RoleModel::where('role_code', $data['roleCode'])
                ->where('id', '<>', $id)
                ->find();
            if ($existingRole) {
                return $this->error('角色编码已存在');
            }

            $role->save([
                'role_name' => $data['roleName'],
                'role_code' => $data['roleCode'],
                'description' => $data['des'] ?? '',
                'status' => $data['enable'] ? 1 : 0
            ]);

            return $this->success($role, '更新成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除角色
     */
    public function delete()
    {
        $id = $this->request->param('id');
        
        try {
            $role = RoleModel::find($id);
            if (!$role) {
                return $this->error('角色不存在');
            }
            
            // 检查是否有管理员使用此角色
            $adminCount = Db::table('fs_admin_role')->where('role_id', $id)->count();
            if ($adminCount > 0) {
                return $this->error('该角色下还有管理员，无法删除');
            }
            
            // 删除角色相关的权限关联
            Db::table('fs_role_menu')->where('role_id', $id)->delete();
            Db::table('fs_role_permission')->where('role_id', $id)->delete();
            
            $role->delete();
            return $this->success([], '删除成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 设置角色权限
     */
    public function setPermissions()
    {
        $id = $this->request->param('id');
        $data = $this->request->post();
        
        try {
            $role = RoleModel::find($id);
            if (!$role) {
                return $this->error('角色不存在');
            }
            
            $menuIds = $data['menuIds'] ?? [];
            $permissionIds = $data['permissionIds'] ?? [];
            
            Db::startTrans();
            
            // 删除原有权限
            Db::table('fs_role_menu')->where('role_id', $id)->delete();
            Db::table('fs_role_permission')->where('role_id', $id)->delete();
            
            // 添加菜单权限
            if (!empty($menuIds)) {
                $menuData = [];
                foreach ($menuIds as $menuId) {
                    $menuData[] = ['role_id' => $id, 'menu_id' => $menuId];
                }
                Db::table('fs_role_menu')->insertAll($menuData);
            }
            
            // 添加按钮权限
            if (!empty($permissionIds)) {
                $permissionData = [];
                foreach ($permissionIds as $permissionId) {
                    $permissionData[] = ['role_id' => $id, 'permission_id' => $permissionId];
                }
                Db::table('fs_role_permission')->insertAll($permissionData);
            }
            
            Db::commit();
            return $this->success([], '权限设置成功');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取角色权限
     */
    public function getPermissions()
    {
        $id = $this->request->param('id');
        
        try {
            $role = RoleModel::with(['menu', 'permission'])->find($id);
            if (!$role) {
                return $this->error('角色不存在');
            }
            
            $result = [
                'menuIds' => $role->menu->column('id'),
                'permissionIds' => $role->permission->column('id')
            ];
            
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
