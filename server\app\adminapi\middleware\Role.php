<?php
declare(strict_types=1);

namespace app\adminapi\middleware;

use app\common\service\AuthService;
use think\Response;

/**
 * 角色控制中间件
 */
class Role
{
    /**
     * 处理请求
     *
     * @param \think\Request $request
     * @param \Closure       $next
     * @param string         $role 需要的角色
     * @return Response
     */
    public function handle($request, \Closure $next, string $role = '')
    {
        // 如果没有指定角色，直接通过
        if (empty($role)) {
            return $next($request);
        }
        
        // 获取用户信息（由Auth中间件注入）
        $user = $request->user ?? null;
        
        if (!$user) {
            return json([
                'code' => 401,
                'msg' => '用户未登录',
                'data' => []
            ]);
        }
        
        // 获取用户角色
        $userRoles = $user['roles'] ?? [];
        
        // 检查角色
        if (!AuthService::hasRole($userRoles, $role)) {
            return json([
                'code' => 403,
                'msg' => '角色权限不足，需要角色: ' . $role,
                'data' => []
            ]);
        }
        
        return $next($request);
    }
}
