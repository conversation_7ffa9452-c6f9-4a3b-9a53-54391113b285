<?php
declare(strict_types=1);

namespace app\adminapi\validate;

use think\Validate;

/**
 * 公告验证器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class NoticeValidate extends Validate
{
    /** @var array 验证规则 */
    protected $rule = [
        'title' => 'require|max:200',
        'content' => 'require',
        'cover_image' => 'max:255',
        'type' => 'integer|in:1,2,3',
        'is_published' => 'integer|in:0,1',
        'is_top' => 'integer|in:0,1',
        'status' => 'integer|in:0,1',
        'sort' => 'integer|egt:0',
        'view_count' => 'integer|egt:0',
    ];

    /** @var array 错误消息 */
    protected $message = [
        'title.require' => '公告标题不能为空',
        'title.max' => '公告标题不能超过200个字符',
        'title.chsAlphaNum' => '公告标题只能包含汉字、字母和数字',
        'content.require' => '公告内容不能为空',
        'content.min' => '公告内容不能为空',
        'cover_image.max' => '封面图片路径不能超过255个字符',
        'type.integer' => '公告类型必须是整数',
        'type.in' => '公告类型值错误',
        'is_published.integer' => '发布状态必须是整数',
        'is_published.in' => '发布状态值错误',
        'is_top.integer' => '置顶状态必须是整数',
        'is_top.in' => '置顶状态值错误',
        'status.integer' => '状态必须是整数',
        'status.in' => '状态值错误',
        'sort.integer' => '排序必须是整数',
        'sort.between' => '排序值必须在0-9999之间',
        'view_count.integer' => '浏览量必须是整数',
        'view_count.egt' => '浏览量不能小于0',
    ];

    /** @var array 验证场景 */
    protected $scene = [
        'create' => [
            'title',
            'content',
            'cover_image',
            'type',
            'is_published',
            'is_top',
            'status',
            'sort'
        ],
        'update' => [
            'title',
            'content',
            'cover_image',
            'type',
            'is_published',
            'is_top',
            'status',
            'sort'
        ],
    ];

    /**
     * 创建场景验证
     *
     * @return NoticeValidate
     */
    public function sceneCreate(): NoticeValidate
    {
        return $this->only(['title', 'content', 'cover_image', 'type', 'is_published', 'is_top', 'status', 'sort']);
    }

    /**
     * 更新场景验证
     *
     * @return NoticeValidate
     */
    public function sceneUpdate(): NoticeValidate
    {
        return $this->only(['title', 'content', 'cover_image', 'type', 'is_published', 'is_top', 'status', 'sort']);
    }
}
