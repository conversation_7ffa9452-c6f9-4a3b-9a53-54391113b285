-- 文章管理菜单结构更新

-- 首先删除现有的文章相关菜单（如果存在）
DELETE FROM `fs_menu` WHERE `name` IN ('文章管理', '文章列表', '文章分类', '公告管理');

-- 获取系统管理菜单的ID（假设系统管理菜单已存在）
SET @system_menu_id = (SELECT id FROM `fs_menu` WHERE `name` = '系统管理' AND `parent_id` = 0 LIMIT 1);

-- 如果系统管理菜单不存在，先创建
INSERT IGNORE INTO `fs_menu` (`parent_id`, `name`, `title`, `path`, `component`, `icon`, `sort`, `status`, `created_at`, `updated_at`)
VALUES (0, '系统管理', '系统管理', '/system', 'Layout', 'Setting', 100, 1, NOW(), NOW());

-- 重新获取系统管理菜单ID
SET @system_menu_id = (SELECT id FROM `fs_menu` WHERE `name` = '系统管理' AND `parent_id` = 0 LIMIT 1);

-- 插入文章管理主菜单（二级菜单）
INSERT INTO `fs_menu` (`parent_id`, `name`, `title`, `path`, `component`, `icon`, `sort`, `status`, `created_at`, `updated_at`)
VALUES (@system_menu_id, '文章管理', '文章管理', '/system/article', 'system/article/index', 'Document', 50, 1, NOW(), NOW());

-- 获取文章管理菜单ID
SET @article_menu_id = (SELECT id FROM `fs_menu` WHERE `name` = '文章管理' AND `parent_id` = @system_menu_id LIMIT 1);

-- 插入文章管理子菜单（三级菜单）
INSERT INTO `fs_menu` (`parent_id`, `name`, `title`, `path`, `component`, `icon`, `sort`, `status`, `created_at`, `updated_at`) VALUES
(@article_menu_id, '文章列表', '文章列表', '/system/article/list', 'system/article/list/index', 'Document', 1, 1, NOW(), NOW()),
(@article_menu_id, '文章分类', '文章分类', '/system/article/category', 'system/article/category/index', 'Folder', 2, 1, NOW(), NOW()),
(@article_menu_id, '公告管理', '公告管理', '/system/article/notice', 'system/article/notice/index', 'Bell', 3, 1, NOW(), NOW());

-- 查看插入结果
SELECT 
    m1.id as menu_id,
    m1.name as menu_name,
    m1.path,
    m1.component,
    m1.icon,
    m1.sort,
    m2.name as parent_name,
    CASE 
        WHEN m1.parent_id = 0 THEN '一级菜单'
        WHEN m2.parent_id = 0 THEN '二级菜单'
        ELSE '三级菜单'
    END as menu_level
FROM `fs_menu` m1
LEFT JOIN `fs_menu` m2 ON m1.parent_id = m2.id
WHERE m1.name IN ('系统管理', '文章管理', '文章列表', '文章分类', '公告管理')
   OR m1.id IN (
       SELECT parent_id FROM `fs_menu` 
       WHERE `name` IN ('文章管理', '文章列表', '文章分类', '公告管理')
   )
ORDER BY 
    CASE WHEN m1.parent_id = 0 THEN 1
         WHEN m2.parent_id = 0 THEN 2
         ELSE 3 END,
    m1.sort;
