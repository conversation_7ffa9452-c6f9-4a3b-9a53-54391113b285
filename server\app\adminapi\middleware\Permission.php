<?php
declare(strict_types=1);

namespace app\adminapi\middleware;

use app\common\model\Admin;
use app\common\service\AuthService;
use think\Response;

/**
 * 权限验证中间件
 */
class Permission
{
    /**
     * 处理请求
     *
     * @param \think\Request $request
     * @param \Closure       $next
     * @param string|null    $permission 权限标识
     * @return Response
     */
    public function handle($request, \Closure $next, $permission = null)
    {
        // 获取当前用户信息
        $user = $request->user ?? null;
        
        if (!$user) {
            return json([
                'code' => 401,
                'msg' => '用户未认证',
                'data' => []
            ]);
        }

        // 如果没有指定权限，直接通过
        if (!$permission) {
            return $next($request);
        }

        // 获取用户权限（优先使用已缓存的权限信息）
        $userPermissions = $user['permissions'] ?? [];

        // 如果用户信息中没有权限信息，从数据库获取
        if (empty($userPermissions)) {
            $admin = Admin::with(['role.permission'])->find($user['id']);
            if (!$admin) {
                return json([
                    'code' => 403,
                    'msg' => '用户不存在',
                    'data' => []
                ]);
            }
            $userPermissions = $admin->getPermissions();
        }

        // 使用AuthService检查权限
        if (!AuthService::hasPermission($userPermissions, $permission)) {
            return json([
                'code' => 403,
                'msg' => '权限不足，需要权限: ' . $permission,
                'data' => []
            ]);
        }

        return $next($request);
    }
}
