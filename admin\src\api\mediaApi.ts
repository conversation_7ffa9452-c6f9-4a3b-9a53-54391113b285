import request from '@/utils/http'

// 媒体文件相关接口类型定义
export interface MediaFile {
  id: number
  original_name: string
  file_name: string
  file_path: string
  file_url: string
  file_size: number
  file_size_formatted: string
  file_type: string
  mime_type: string
  category_id: number
  category_name?: string
  is_image: boolean
  upload_user_id: number
  upload_user_type: string
  created_at: string
  updated_at: string
}

export interface MediaCategory {
  id: number
  name: string
  parent_id: number
  sort: number
  file_count: number
  created_at: string
  updated_at: string
}

export interface MediaListParams {
  page?: number
  limit?: number
  category_id?: number | null
  keyword?: string
  file_type?: string
}

export interface MediaListResponse {
  list: MediaFile[]
  total: number
  page: number
  limit: number
  pages: number
}

// 媒体文件服务类
export class MediaService {
  // 获取媒体文件列表
  static getMediaList(params: MediaListParams = {}) {
    return request.get<MediaListResponse>({
      url: '/media',
      params
    })
  }

  // 上传媒体文件
  static uploadMedia(file: File, categoryId: number = 0) {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('category_id', categoryId.toString())

    return request.post<MediaFile>({
      url: '/media/upload',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  // 删除媒体文件
  static deleteMedia(id: number) {
    return request.del({
      url: `/media/${id}`
    })
  }

  // 批量删除媒体文件
  static batchDeleteMedia(ids: number[]) {
    return request.post({
      url: '/media/batch-delete',
      data: { ids }
    })
  }

  // 移动文件到分类
  static moveToCategory(ids: number[], categoryId: number) {
    return request.post({
      url: '/media/move-to-category',
      data: { ids, category_id: categoryId }
    })
  }

  // 获取分类列表
  static getCategories() {
    return request.get<MediaCategory[]>({
      url: '/media/categories'
    })
  }

  // 创建分类
  static createCategory(name: string, parentId: number = 0) {
    return request.post<MediaCategory>({
      url: '/media/categories',
      data: { name, parent_id: parentId }
    })
  }

  // 更新分类
  static updateCategory(id: number, name: string) {
    return request.put({
      url: `/media/categories/${id}`,
      data: { name }
    })
  }

  // 删除分类
  static deleteCategory(id: number) {
    return request.del({
      url: `/media/categories/${id}`
    })
  }
}

// 导出默认服务
export default MediaService
