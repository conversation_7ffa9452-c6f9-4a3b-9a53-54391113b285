<?php
declare(strict_types=1);

namespace app\common\model;

use think\Model;

/**
 * 分类模型
 */
class Category extends Model
{
    // 表名
    protected $table = 'fs_categories';
    
    // 主键
    protected $pk = 'id';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    // 字段类型转换
    protected $type = [
        'parent_id' => 'integer',
        'status' => 'integer',
        'sort' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // 获取器 - 状态文本
    public function getStatusTextAttr($value, $data)
    {
        $status = [0 => '禁用', 1 => '启用'];
        return $status[$data['status']] ?? '未知';
    }

    // 关联文章
    public function articles()
    {
        return $this->hasMany(Article::class, 'category_id');
    }

    // 获取文章数量
    public function getArticleCountAttr()
    {
        return $this->articles()->where('status', 1)->count();
    }
}
