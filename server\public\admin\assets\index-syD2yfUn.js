var e=Object.defineProperty,n=Object.defineProperties,t=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,u=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,a=(n,t,l)=>t in n?e(n,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):n[t]=l,i=(e,n)=>{for(var t in n||(n={}))u.call(n,t)&&a(e,t,n[t]);if(l)for(var t of l(n))r.call(n,t)&&a(e,t,n[t]);return e},o=(e,n,t)=>new Promise(((l,u)=>{var r=e=>{try{i(t.next(e))}catch(n){u(n)}},a=e=>{try{i(t.throw(e))}catch(n){u(n)}},i=e=>e.done?l(e.value):Promise.resolve(e.value).then(r,a);i((t=t.apply(e,n)).next())}));import{G as s,J as c,K as v,L as d,N as f,O as m,P as p,Q as y}from"./index-DG9-1w7X.js";import{r as b,w as h,c as w,y as O,d as I,e as E}from"./vendor-84Inc-Pt.js";const F=m?window:void 0,S=m?window.document:void 0;function g(e){var n;const t=c(e);return null!=(n=null==t?void 0:t.$el)?n:t}function k(...e){let n,t,l,u;if("string"==typeof e[0]||Array.isArray(e[0])?([t,l,u]=e,n=F):[n,t,l,u]=e,!n)return p;Array.isArray(t)||(t=[t]),Array.isArray(l)||(l=[l]);const r=[],a=()=>{r.forEach((e=>e())),r.length=0},o=h((()=>[g(n),c(u)]),(([e,n])=>{if(a(),!e)return;const u=y(n)?i({},n):n;r.push(...t.flatMap((n=>l.map((t=>((e,n,t,l)=>(e.addEventListener(n,t,l),()=>e.removeEventListener(n,t,l)))(e,n,t,u))))))}),{immediate:!0,flush:"post"}),s=()=>{o(),a()};return d(s),s}function x(e){const n=function(){const e=b(!1),n=E();return n&&I((()=>{e.value=!0}),n),e}();return w((()=>(n.value,Boolean(e()))))}function A(e,n={}){const{delayEnter:t=0,delayLeave:l=0,window:u=F}=n,r=b(!1);let a;const i=e=>{const n=e?t:l;a&&(clearTimeout(a),a=void 0),n?a=setTimeout((()=>r.value=e),n):r.value=e};return u?(k(e,"mouseenter",(()=>i(!0)),{passive:!0}),k(e,"mouseleave",(()=>i(!1)),{passive:!0}),r):r}const P=["fullscreenchange","webkitfullscreenchange","webkitendfullscreen","mozfullscreenchange","MSFullscreenChange"];function L(e,n={}){const{document:t=S,autoExit:l=!1}=n,u=w((()=>{var n;return null!=(n=g(e))?n:null==t?void 0:t.querySelector("html")})),r=b(!1),a=w((()=>["requestFullscreen","webkitRequestFullscreen","webkitEnterFullscreen","webkitEnterFullScreen","webkitRequestFullScreen","mozRequestFullScreen","msRequestFullscreen"].find((e=>t&&e in t||u.value&&e in u.value)))),i=w((()=>["exitFullscreen","webkitExitFullscreen","webkitExitFullScreen","webkitCancelFullScreen","mozCancelFullScreen","msExitFullscreen"].find((e=>t&&e in t||u.value&&e in u.value)))),s=w((()=>["fullScreen","webkitIsFullScreen","webkitDisplayingFullscreen","mozFullScreen","msFullscreenElement"].find((e=>t&&e in t||u.value&&e in u.value)))),c=["fullscreenElement","webkitFullscreenElement","mozFullScreenElement","msFullscreenElement"].find((e=>t&&e in t)),v=x((()=>u.value&&t&&void 0!==a.value&&void 0!==i.value&&void 0!==s.value)),f=()=>{if(s.value){if(t&&null!=t[s.value])return t[s.value];{const e=u.value;if(null!=(null==e?void 0:e[s.value]))return Boolean(e[s.value])}}return!1};function m(){return o(this,null,(function*(){if(v.value&&r.value){if(i.value)if(null!=(null==t?void 0:t[i.value]))yield t[i.value]();else{const e=u.value;null!=(null==e?void 0:e[i.value])&&(yield e[i.value]())}r.value=!1}}))}function p(){return o(this,null,(function*(){if(!v.value||r.value)return;f()&&(yield m());const e=u.value;a.value&&null!=(null==e?void 0:e[a.value])&&(yield e[a.value](),r.value=!0)}))}const y=()=>{const e=f();(!e||e&&c&&(null==t?void 0:t[c])===u.value)&&(r.value=e)};return k(t,P,y,!1),k((()=>g(u)),P,y,!1),l&&d(m),{isSupported:v,isFullscreen:r,enter:p,exit:m,toggle:function(){return o(this,null,(function*(){yield r.value?m():p()}))}}}const Q={easeInSine:[.12,0,.39,0],easeOutSine:[.61,1,.88,1],easeInOutSine:[.37,0,.63,1],easeInQuad:[.11,0,.5,0],easeOutQuad:[.5,1,.89,1],easeInOutQuad:[.45,0,.55,1],easeInCubic:[.32,0,.67,0],easeOutCubic:[.33,1,.68,1],easeInOutCubic:[.65,0,.35,1],easeInQuart:[.5,0,.75,0],easeOutQuart:[.25,1,.5,1],easeInOutQuart:[.76,0,.24,1],easeInQuint:[.64,0,.78,0],easeOutQuint:[.22,1,.36,1],easeInOutQuint:[.83,0,.17,1],easeInExpo:[.7,0,.84,0],easeOutExpo:[.16,1,.3,1],easeInOutExpo:[.87,0,.13,1],easeInCirc:[.55,0,1,.45],easeOutCirc:[0,.55,.45,1],easeInOutCirc:[.85,0,.15,1],easeInBack:[.36,0,.66,-.56],easeOutBack:[.34,1.56,.64,1],easeInOutBack:[.68,-.6,.32,1.6]},j=Object.assign({},{linear:s},Q);function C([e,n,t,l]){const u=(e,n)=>1-3*n+3*e,r=(e,n)=>3*n-6*e,a=e=>3*e,i=(e,n,t)=>((u(n,t)*e+r(n,t))*e+a(n))*e;return o=>e===n&&t===l?o:i((n=>{let l=n;for(let v=0;v<4;++v){const v=(o=l,3*u(s=e,c=t)*o*o+2*r(s,c)*o+a(s));if(0===v)return l;l-=(i(l,e,t)-n)/v}var o,s,c;return l})(o),n,l)}function q(e,n,t){return e+t*(n-e)}function N(e){return("number"==typeof e?[e]:e)||[]}function T(e,l={}){let u=0;const r=()=>{const n=c(e);return"number"==typeof n?n:n.map(c)},a=b(r());return h(r,(e=>o(this,null,(function*(){var r,o;if(c(l.disabled))return;const d=++u;if(l.delay&&(yield v(c(l.delay))),d!==u)return;const f=Array.isArray(e)?e.map(c):c(e);var m,p;null==(r=l.onStarted)||r.call(l),yield function(e,n,t,l={}){var u,r;const a=c(n),i=c(t),o=N(a),v=N(i),d=null!=(u=c(l.duration))?u:1e3,f=Date.now(),m=Date.now()+d,p="function"==typeof l.transition?l.transition:null!=(r=c(l.transition))?r:s,y="function"==typeof p?p:C(p);return new Promise((n=>{e.value=a;const t=()=>{var u;if(null==(u=l.abort)?void 0:u.call(l))return void n();const r=Date.now(),a=y((r-f)/d),s=N(e.value).map(((e,n)=>q(o[n],v[n],a)));Array.isArray(e.value)?e.value=s.map(((e,n)=>{var t,l;return q(null!=(t=o[n])?t:0,null!=(l=v[n])?l:0,a)})):"number"==typeof e.value&&(e.value=s[0]),r<m?requestAnimationFrame(t):(e.value=i,n())};t()}))}(a,a.value,f,(m=i({},l),p={abort:()=>{var e;return d!==u||(null==(e=l.abort)?void 0:e.call(l))}},n(m,t(p)))),null==(o=l.onFinished)||o.call(l)}))),{deep:!0}),h((()=>c(l.disabled)),(e=>{e&&(u++,a.value=r())})),d((()=>{u++})),w((()=>c(l.disabled)?r():a.value))}function z(e={}){const{window:n=F,initialWidth:t=Number.POSITIVE_INFINITY,initialHeight:l=Number.POSITIVE_INFINITY,listenOrientation:u=!0,includeScrollbar:r=!0,type:a="inner"}=e,i=b(t),o=b(l),s=()=>{n&&("outer"===a?(i.value=n.outerWidth,o.value=n.outerHeight):r?(i.value=n.innerWidth,o.value=n.innerHeight):(i.value=n.document.documentElement.clientWidth,o.value=n.document.documentElement.clientHeight))};if(s(),f(s),k("resize",s,{passive:!0}),u){const e=function(e,n={}){const{window:t=F}=n,l=x((()=>t&&"matchMedia"in t&&"function"==typeof t.matchMedia));let u;const r=b(!1),a=e=>{r.value=e.matches},i=()=>{u&&("removeEventListener"in u?u.removeEventListener("change",a):u.removeListener(a))},o=O((()=>{l.value&&(i(),u=t.matchMedia(c(e)),"addEventListener"in u?u.addEventListener("change",a):u.addListener(a),r.value=u.matches)}));return d((()=>{o(),i(),u=void 0})),r}("(orientation: portrait)");h(e,(()=>s()))}return{width:i,height:o}}export{j as T,z as a,A as b,L as c,T as d,k as u};
