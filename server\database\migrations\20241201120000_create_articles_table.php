<?php
use think\migration\Migrator;
use think\migration\db\Column;

/**
 * 文章管理表迁移文件
 * 创建时间：2024-12-01
 * 说明：文章管理功能的数据表结构
 */
class CreateArticlesTable extends Migrator
{
    public function change()
    {
        $table = $this->table('fs_articles', [
            'id' => false,
            'primary_key' => ['id'],
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci',
            'comment' => '文章表'
        ]);

        $table
            // 主键
            ->addColumn('id', 'integer', [
                'identity' => true,
                'signed' => false,
                'comment' => '文章ID'
            ])
            // 基本字段
            ->addColumn('title', 'string', [
                'limit' => 200,
                'null' => false,
                'comment' => '文章标题'
            ])
            ->addColumn('slug', 'string', [
                'limit' => 100,
                'null' => false,
                'comment' => '文章别名'
            ])
            ->addColumn('content', 'text', [
                'null' => true,
                'comment' => '文章内容'
            ])
            ->addColumn('summary', 'text', [
                'null' => true,
                'comment' => '文章摘要'
            ])
            // 分类和标签
            ->addColumn('category_id', 'integer', [
                'signed' => false,
                'null' => true,
                'comment' => '分类ID'
            ])
            ->addColumn('tags', 'string', [
                'limit' => 500,
                'null' => true,
                'comment' => '标签（逗号分隔）'
            ])
            // 图片
            ->addColumn('cover_image', 'string', [
                'limit' => 255,
                'null' => true,
                'comment' => '封面图片'
            ])
            // 作者信息
            ->addColumn('author_id', 'integer', [
                'signed' => false,
                'null' => true,
                'comment' => '作者ID'
            ])
            ->addColumn('author_name', 'string', [
                'limit' => 50,
                'null' => true,
                'comment' => '作者姓名'
            ])
            // 统计信息
            ->addColumn('view_count', 'integer', [
                'signed' => false,
                'null' => false,
                'default' => 0,
                'comment' => '浏览次数'
            ])
            ->addColumn('like_count', 'integer', [
                'signed' => false,
                'null' => false,
                'default' => 0,
                'comment' => '点赞次数'
            ])
            // 发布信息
            ->addColumn('published_at', 'datetime', [
                'null' => true,
                'comment' => '发布时间'
            ])
            ->addColumn('is_published', 'integer', [
                'limit' => 1,
                'null' => false,
                'default' => 0,
                'comment' => '是否发布：0=草稿，1=已发布'
            ])
            ->addColumn('is_featured', 'integer', [
                'limit' => 1,
                'null' => false,
                'default' => 0,
                'comment' => '是否推荐：0=否，1=是'
            ])
            // 状态字段
            ->addColumn('status', 'integer', [
                'limit' => 1,
                'null' => false,
                'default' => 1,
                'comment' => '状态：1=启用，2=禁用'
            ])
            // 排序字段
            ->addColumn('sort', 'integer', [
                'signed' => false,
                'null' => false,
                'default' => 0,
                'comment' => '排序'
            ])
            // 时间戳
            ->addColumn('created_at', 'datetime', [
                'null' => true,
                'comment' => '创建时间'
            ])
            ->addColumn('updated_at', 'datetime', [
                'null' => true,
                'comment' => '更新时间'
            ])
            ->addColumn('deleted_at', 'datetime', [
                'null' => true,
                'comment' => '删除时间'
            ])
            // 索引
            ->addIndex(['slug'], ['unique' => true, 'name' => 'idx_articles_slug'])
            ->addIndex(['category_id'], ['name' => 'idx_category_id'])
            ->addIndex(['author_id'], ['name' => 'idx_author_id'])
            ->addIndex(['is_published'], ['name' => 'idx_is_published'])
            ->addIndex(['status'], ['name' => 'idx_status'])
            ->addIndex(['published_at'], ['name' => 'idx_published_at'])
            ->addIndex(['created_at'], ['name' => 'idx_created_at'])
            ->create();
    }
}
