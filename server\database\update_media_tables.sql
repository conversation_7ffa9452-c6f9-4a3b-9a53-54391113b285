-- 更新媒体管理相关表结构

-- 创建图片分类表（如果不存在）
CREATE TABLE IF NOT EXISTS `fs_media_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `parent_id` int(11) DEFAULT 0 COMMENT '父分类ID',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='媒体文件分类表';

-- 检查并添加新字段到 fs_upload 表
ALTER TABLE `fs_upload` 
ADD COLUMN IF NOT EXISTS `file_url` varchar(500) DEFAULT NULL COMMENT '文件访问URL' AFTER `file_path`,
ADD COLUMN IF NOT EXISTS `category_id` int(11) DEFAULT 0 COMMENT '分类ID' AFTER `mime_type`,
ADD COLUMN IF NOT EXISTS `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `created_at`;

-- 添加索引
ALTER TABLE `fs_upload` 
ADD INDEX IF NOT EXISTS `idx_category_id` (`category_id`),
ADD INDEX IF NOT EXISTS `idx_file_type` (`file_type`),
ADD INDEX IF NOT EXISTS `idx_upload_user` (`upload_user_id`, `upload_user_type`);

-- 插入默认分类数据（如果不存在）
INSERT IGNORE INTO `fs_media_category` (`id`, `name`, `parent_id`, `sort`) VALUES
(1, 'logo', 0, 1),
(2, '商品图片', 0, 2),
(3, '清报', 0, 3),
(4, '系统图片', 0, 4);

-- 更新现有文件的 file_url 字段
UPDATE `fs_upload` SET `file_url` = CONCAT('http://fanshop.gg/', `file_path`) WHERE `file_url` IS NULL OR `file_url` = '';
