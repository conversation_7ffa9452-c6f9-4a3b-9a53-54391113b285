<template>
  <div class="notice-edit-page">
    <!-- 页面头部 -->
    <ElCard class="page-header" shadow="never">
      <div class="header-content">
        <div class="title-section">
          <h2>{{ isEdit ? '编辑公告' : '新增公告' }}</h2>
        </div>
        <div class="action-section">
          <ElButton @click="handleCancel">取消</ElButton>
          <ElButton type="primary" @click="handleSave" :loading="saving">保存</ElButton>
        </div>
      </div>
    </ElCard>

    <!-- 表单内容 -->
    <ElCard class="form-content" shadow="never">
      <div class="edit-layout">
        <!-- 左侧：基本信息表单 -->
        <div class="left-panel">
          <ElForm
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="80px"
            label-position="left"
          >
            <!-- 公告标题 -->
            <ElFormItem label="公告标题" prop="title">
              <ElInput
                v-model="formData.title"
                placeholder="请输入公告标题"
                maxlength="200"
                show-word-limit
              />
            </ElFormItem>

            <!-- 封面图片 -->
            <ElFormItem label="封面图片" prop="cover_image">
              <div class="cover-upload-area">
                <div
                  v-if="!formData.cover_image"
                  class="upload-placeholder"
                  @click="showCoverPicker = true"
                >
                  <div class="upload-icon">+</div>
                </div>
                <div v-else class="uploaded-image">
                  <img :src="resolveFileUrl(formData.cover_image)" alt="封面图片" class="cover-preview" />
                  <div class="image-overlay">
                    <ElButton size="small" @click="showCoverPicker = true">更换</ElButton>
                    <ElButton size="small" type="danger" @click="formData.cover_image = ''">删除</ElButton>
                  </div>
                </div>
                <div class="upload-hint">建议尺寸：240*240像素</div>
                <MediaPicker
                  v-model="showCoverPicker"
                  :multiple="false"
                  accept="image/*"
                  title="选择封面图片"
                  @confirm="handleCoverConfirm"
                />
              </div>
            </ElFormItem>

            <!-- 置顶状态 -->
            <ElFormItem label="置顶状态" prop="is_top">
              <ElRadioGroup v-model="formData.is_top">
                <ElRadio :value="0">不置顶</ElRadio>
                <ElRadio :value="1">置顶</ElRadio>
              </ElRadioGroup>
            </ElFormItem>

            <!-- 显示状态 -->
            <ElFormItem label="显示状态" prop="status">
              <ElRadioGroup v-model="formData.status">
                <ElRadio :value="1">显示</ElRadio>
                <ElRadio :value="0">隐藏</ElRadio>
              </ElRadioGroup>
            </ElFormItem>

            <!-- 排序 -->
            <ElFormItem label="排序" prop="sort">
              <ElInputNumber
                v-model="formData.sort"
                :min="0"
                :max="9999"
                placeholder="数值越大越靠前"
                style="width: 100%"
              />
            </ElFormItem>
          </ElForm>
        </div>

        <!-- 右侧：富文本编辑器 -->
        <div class="right-panel">
          <div class="panel-title">公告内容</div>
          <ElForm
            :model="formData"
            label-width="0"
          >
            <ElFormItem prop="content">
              <div class="editor-container">
                <RichTextEditor
                  v-model="formData.content"
                  height="600px"
                  mode="article"
                  placeholder="请输入公告内容..."
                />
              </div>
            </ElFormItem>
          </ElForm>
        </div>
      </div>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import RichTextEditor from '@/components/common/rich-text-editor/index.vue'
import MediaPicker from '@/components/MediaPicker/index.vue'
import { NoticeService, type NoticeFormData, type NoticeInfo } from '@/api/noticeApi'
import type { MediaFile } from '@/api/mediaApi'
import { resolveFileUrl } from '@/utils/url'

/**
 * 公告编辑页面组件
 *
 * <AUTHOR>
 * @since 1.0.0
 */
defineOptions({ name: 'NoticeEdit' })

// 路由和表单引用
const router = useRouter()
const route = useRoute()
const formRef = ref<FormInstance>()

// 状态管理
const saving = ref<boolean>(false)
const loading = ref<boolean>(false)
const showCoverPicker = ref<boolean>(false)

// 计算属性
const isEdit = computed<boolean>(() => !!route.params.id)
const noticeId = computed<string>(() => route.params.id as string)

// 表单数据
const formData = reactive<NoticeFormData>({
  title: '',
  content: '',
  cover_image: '',
  is_top: 0,
  status: 1,
  sort: 0
})



// 表单验证规则
const formRules: FormRules<NoticeFormData> = {
  title: [
    { required: true, message: '请输入公告标题', trigger: 'blur' },
    { max: 200, message: '标题不能超过200个字符', trigger: 'blur' },
    { min: 1, message: '公告标题不能为空', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入公告内容', trigger: 'blur' },
    { min: 1, message: '公告内容不能为空', trigger: 'blur' }
  ],
  is_top: [
    { type: 'number', message: '置顶状态必须是数字', trigger: 'change' }
  ],
  status: [
    { type: 'number', message: '状态必须是数字', trigger: 'change' }
  ],
  sort: [
    { type: 'number', min: 0, max: 9999, message: '排序值必须在0-9999之间', trigger: 'blur' }
  ]
}

/**
 * 加载公告数据（编辑模式）
 */
const loadNoticeData = async (): Promise<void> => {
  if (!isEdit.value) return

  try {
    loading.value = true
    const id = Number(noticeId.value)

    if (!id || isNaN(id)) {
      throw new Error('无效的公告ID')
    }

    const notice = await NoticeService.getNoticeDetail(id)

    // 填充表单数据
    Object.assign(formData, {
      title: notice.title || '',
      content: notice.content || '',
      cover_image: notice.cover_image || '',
      is_top: notice.is_top ?? 0,
      status: notice.status ?? 1,
      sort: notice.sort ?? 0
    })
  } catch (error: any) {
    const message = error?.message || '加载公告数据失败'
    ElMessage.error(message)
    await router.push('/system/article/notice')
  } finally {
    loading.value = false
  }
}

/**
 * 保存公告
 */
const handleSave = async (): Promise<void> => {
  if (!formRef.value) return

  try {
    // 表单验证
    await formRef.value.validate()
    saving.value = true

    const data = { ...formData }

    if (isEdit.value) {
      const id = Number(noticeId.value)
      if (!id || isNaN(id)) {
        throw new Error('无效的公告ID')
      }
      await NoticeService.updateNotice(id, data)
      ElMessage.success('公告更新成功')
    } else {
      await NoticeService.createNotice(data)
      ElMessage.success('公告保存成功')
    }

    await router.push('/system/article/notice')
  } catch (error: any) {
    if (error !== false) { // 不是表单验证错误
      const message = error?.message || (isEdit.value ? '更新失败' : '保存失败')
      ElMessage.error(message)
    }
  } finally {
    saving.value = false
  }
}

/**
 * 封面图片选择确认处理
 */
const handleCoverConfirm = (files: MediaFile[]): void => {
  if (files.length > 0) {
    // 保存文件路径，而不是完整URL
    formData.cover_image = files[0].file_path
  }
}

/**
 * 取消操作
 */
const handleCancel = async (): Promise<void> => {
  try {
    await ElMessageBox.confirm(
      '确定要取消吗？未保存的内容将丢失。',
      '提示',
      { type: 'warning' }
    )
    await router.push('/system/article/notice')
  } catch {
    // 用户取消操作，不做任何处理
  }
}

// 页面初始化
onMounted((): void => {
  loadNoticeData()
})
</script>

<style scoped lang="scss">
// 封面上传区域样式
.cover-upload-area {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  .upload-placeholder {
    width: 100px !important;
    height: 100px !important;
    border: 2px dashed #ddd !important;
    border-radius: 6px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    background-color: #fff !important;
    box-sizing: border-box !important;
    margin-bottom: 8px !important;

    &:hover {
      border-color: #409eff !important;
    }

    .upload-icon {
      font-size: 32px !important;
      color: #ccc !important;
      font-weight: 300 !important;
      line-height: 1 !important;
    }
  }

  .upload-hint {
    font-size: 12px !important;
    color: #999 !important;
    text-align: left !important;
    margin-top: 4px !important;
    line-height: 1.4;
  }

  .uploaded-image {
    position: relative;
    display: inline-block;

    .cover-preview {
      width: 100px;
      height: 100px;
      object-fit: cover;
      border-radius: 6px;
      border: 1px solid #ddd;
    }

    .image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      opacity: 0;
      transition: opacity 0.3s;
      border-radius: 8px;
    }

    &:hover .image-overlay {
      opacity: 1;
    }
  }
}

.notice-edit-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);

  .page-header {
    margin-bottom: 20px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-section {
        h2 {
          margin: 0;
          font-size: 20px;
          font-weight: 600;
          color: #303133;
        }
      }

      .action-section {
        display: flex;
        gap: 12px;
      }
    }
  }

  .form-content {
    :deep(.el-form-item__label) {
      font-weight: 500;
    }

    :deep(.el-input__count) {
      color: #909399;
    }
  }

  /* 左右分栏布局 */
  .edit-layout {
    display: flex;
    min-height: 600px;

    .left-panel {
      flex: 0 0 400px; /* 左侧固定宽度400px，增加宽度 */
      padding: 0 20px 0 24px; /* 左边距24px，右边距20px */
      border-right: 1px solid #ebeef5; /* 添加分割线 */

      :deep(.el-form-item) {
        margin-bottom: 20px;
        display: flex;
        align-items: flex-start;
      }

      :deep(.el-form-item__label) {
        font-weight: 500;
        color: #606266;
        font-size: 14px;
        padding-right: 12px !important;
        text-align: right !important;
        line-height: 32px; /* 与输入框高度对齐 */
        min-height: 32px;
      }

      // 隐藏必填字段的红色星号
      :deep(.el-form-item__label::before) {
        display: none !important;
      }

      :deep(.el-form-item__content) {
        flex: 1;
        line-height: 32px;
      }

      :deep(.el-input) {
        width: 100%;
      }

      :deep(.el-select) {
        width: 100%;
      }

      :deep(.el-input-number) {
        width: 100%;
      }

      :deep(.el-textarea) {
        width: 100%;
      }

      :deep(.el-input__count) {
        color: #909399;
      }

      :deep(.el-radio-group) {
        display: flex;
        gap: 16px;
      }
    }

    .right-panel {
      flex: 1; /* 右侧占剩余空间 */
      padding-left: 20px;

      .panel-title {
        font-weight: 500;
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
        margin-left: 100px; /* 与左侧label对齐 */
      }

      .editor-container {
        margin-left: 100px; /* 与左侧输入框对齐 */

        :deep(.rich-text-editor) {
          border: 1px solid #dcdfe6;
          border-radius: 4px;
        }

        :deep(.ql-toolbar) {
          border-bottom: 1px solid #dcdfe6;
          background-color: #fafafa;
        }

        :deep(.ql-container) {
          border: none;
        }

        :deep(.ql-editor) {
          min-height: 600px;
          font-size: 14px;
          line-height: 1.6;
          padding: 12px 15px;
        }

        :deep(.ql-editor.ql-blank::before) {
          color: #c0c4cc;
          font-style: normal;
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .notice-edit-page .edit-layout {
    flex-direction: column;

    .left-panel {
      flex: none;
      width: 100%;
      padding-right: 0;
      border-right: none;
      border-bottom: 1px solid #ebeef5;
      padding-bottom: 20px;
      margin-bottom: 20px;
    }

    .right-panel {
      flex: none;
      width: 100%;
      padding-left: 0;

      .panel-title {
        margin-left: 80px;
      }

      .editor-container {
        margin-left: 80px;

        :deep(.ql-editor) {
          min-height: 500px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .notice-edit-page .edit-layout .right-panel {
    .panel-title {
      margin-left: 0;
    }

    .editor-container {
      margin-left: 0;
    }
  }
}
</style>
