var e=Object.defineProperty,a=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,t=(a,l,r)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,o=(e,o)=>{for(var n in o||(o={}))l.call(o,n)&&t(e,n,o[n]);if(a)for(var n of a(o))r.call(o,n)&&t(e,n,o[n]);return e};import{_ as n}from"./index-Dom_waQv.js";import{k as p,r as s,C as i,D as c,i as b,u}from"./vendor-84Inc-Pt.js";const d=p({__name:"user-search",emits:["update:modelValue","search","reset"],setup(e,{emit:a}){const l=a,r={name:"",phone:"",address:"",level:"normal",email:"",date:"2025-01-05",daterange:["2025-01-01","2025-02-10"],status:"1"},t=s(o({},r)),p=()=>{l("update:modelValue",o({},r)),l("reset")},d=()=>{l("search")},g=e=>{},m=[{label:"用户名",prop:"name",type:"input",config:{clearable:!0},onChange:g},{label:"电话",prop:"phone",type:"input",config:{clearable:!0},onChange:g},{label:"用户等级",prop:"level",type:"select",config:{clearable:!0},options:()=>[{label:"普通用户",value:"normal"},{label:"VIP用户",value:"vip"},{label:"高级VIP",value:"svip"},{label:"企业用户",value:"enterprise",disabled:!0}],onChange:g},{label:"地址",prop:"address",type:"input",config:{clearable:!0},onChange:g},{label:"邮箱",prop:"email",type:"input",config:{clearable:!0},onChange:g},{prop:"date",label:"日期",type:"date",config:{type:"date",placeholder:"请选择日期"}},{prop:"daterange",label:"日期范围",type:"daterange",config:{type:"daterange",startPlaceholder:"开始时间",endPlaceholder:"结束时间"}},{label:"状态",prop:"status",type:"radio",options:[{label:"在线",value:"1"},{label:"离线",value:"2"}],onChange:g}];return(e,a)=>{const l=n;return c(),i(l,{filter:u(t),"onUpdate:filter":a[0]||(a[0]=e=>b(t)?t.value=e:null),items:m,onReset:p,onSearch:d},null,8,["filter"])}}});export{d as _};
