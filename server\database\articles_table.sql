-- 创建文章管理表
CREATE TABLE `fs_articles` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '文章ID',
  `title` varchar(200) NOT NULL COMMENT '文章标题',
  `slug` varchar(100) NOT NULL COMMENT '文章别名',
  `content` longtext COMMENT '文章内容',
  `summary` text COMMENT '文章摘要',
  `category_id` int(11) unsigned DEFAULT NULL COMMENT '分类ID',
  `tags` varchar(500) DEFAULT NULL COMMENT '标签（逗号分隔）',
  `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图片',
  `author_id` int(11) unsigned DEFAULT NULL COMMENT '作者ID',
  `author_name` varchar(50) DEFAULT NULL COMMENT '作者姓名',
  `view_count` int(11) unsigned NOT NULL DEFAULT 0 COMMENT '浏览次数',
  `like_count` int(11) unsigned NOT NULL DEFAULT 0 COMMENT '点赞次数',
  `published_at` datetime DEFAULT NULL COMMENT '发布时间',
  `is_published` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否发布：0=草稿，1=已发布',
  `is_featured` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否推荐：0=否，1=是',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1=启用，2=禁用',
  `sort` int(11) unsigned NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_articles_slug` (`slug`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_author_id` (`author_id`),
  KEY `idx_is_published` (`is_published`),
  KEY `idx_status` (`status`),
  KEY `idx_published_at` (`published_at`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='文章表';

-- 插入文章管理菜单到系统管理下
INSERT INTO `fs_menu` (
    `parent_id`,
    `name`,
    `path`,
    `component`,
    `title`,
    `icon`,
    `sort`,
    `type`,
    `status`,
    `keep_alive`,
    `external_link`,
    `is_iframe`,
    `created_at`,
    `updated_at`
) VALUES (
    2,
    '文章管理',
    '/system/article',
    'system/article/index',
    '文章管理',
    'Document',
    50,
    1,
    1,
    0,
    '',
    0,
    NOW(),
    NOW()
);
