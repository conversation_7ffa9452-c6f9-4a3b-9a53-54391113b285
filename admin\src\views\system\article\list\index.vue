<template>
  <CrudListPage
    ref="crudListPageRef"
    :config="articleListConfig"
    @create="handleArticleCreate"
    @update="handleArticleUpdate"
    @delete="handleArticleDelete"
    @selection-change="handleSelectionChange"
  >
    <!-- 自定义头部操作 -->
    <template #header-actions="{ selectedRows }">
      <ElButton
        v-if="selectedRows.length > 0"
        type="danger"
        @click="handleBatchDelete"
      >
        批量删除 ({{ selectedRows.length }})
      </ElButton>
    </template>
  </CrudListPage>
</template>

<script setup lang="ts">
import { reactive, ref, h } from 'vue'
import { useRouter } from 'vue-router'
import { ElButton, ElMessage, ElMessageBox, ElTag } from 'element-plus'
import CrudListPage from '@/components/common/crud-list-page/index.vue'
import { ArticleService, type ArticleInfo, type ArticleFormData } from '@/api/articleApi'
import type { CrudListConfig, CrudFormData } from '@/components/common/crud-list-page/types'
import { resolveFileUrl } from '@/utils/url'

const router = useRouter()

// 选中的行数据
const selectedRows = ref<ArticleInfo[]>([])

// CrudListPage 组件引用
const crudListPageRef = ref()

// 推荐状态配置
const getFeaturedConfig = (status: number) => {
  const configs = {
    0: { type: 'info' as const, text: '否' },
    1: { type: 'warning' as const, text: '是' }
  }
  return configs[status as keyof typeof configs] || { type: 'info' as const, text: '否' }
}

// 状态配置
const getStatusConfig = (status: number) => {
  const configs = {
    1: { type: 'success' as const, text: '启用' },
    2: { type: 'danger' as const, text: '禁用' }
  }
  return configs[status as keyof typeof configs] || { type: 'info' as const, text: '未知' }
}

// 状态切换处理
const handleStatusToggle = async (row: ArticleInfo) => {
  try {
    const newStatus = row.status === 1 ? 2 : 1
    const statusText = newStatus === 1 ? '启用' : '禁用'

    await ElMessageBox.confirm(
      `确定要${statusText}这篇文章吗？`,
      '状态切换',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await ArticleService.updateArticleStatus(row.id, newStatus)
    ElMessage.success(`文章已${statusText}`)

    // 刷新列表
    if (crudListPageRef.value) {
      crudListPageRef.value.refresh()
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '状态切换失败')
    }
  }
}

// 配置对象
const articleListConfig: CrudListConfig<ArticleInfo> = reactive({
  // API配置
  api: {
    list: ArticleService.getArticleList,
    update: (id: string | number, data: CrudFormData) => ArticleService.updateArticle(Number(id), data as Partial<ArticleFormData>),
    delete: (id: string | number) => ArticleService.deleteArticle(Number(id))
  },

  // 表格列配置
  columns: [
    { type: 'selection' },
    { type: 'index', label: '序号' },
    {
      prop: 'cover_image',
      label: '封面',
      formatter: (row: any) => {
        return h('div', { class: 'article-cover-cell' }, [
          row.cover_image
            ? h('img', {
                src: resolveFileUrl(row.cover_image),
                alt: '封面',
                class: 'cover-image',
                style: {
                  width: '50px',
                  height: '50px',
                  objectFit: 'cover',
                  borderRadius: '6px',
                  border: '1px solid #e5e7eb'
                }
              })
            : h('div', {
                class: 'no-cover',
                style: {
                  width: '50px',
                  height: '50px',
                  backgroundColor: '#f3f4f6',
                  borderRadius: '6px',
                  border: '1px solid #e5e7eb',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '12px',
                  color: '#9ca3af'
                }
              }, '无图')
        ])
      }
    },
    { prop: 'title', label: '文章标题' },
    { prop: 'author_name', label: '作者' },
    {
      prop: 'is_featured',
      label: '推荐',
      formatter: (row: any) => {
        const config = getFeaturedConfig(row.is_featured)
        return h(ElTag, { type: config.type, size: 'small' }, () => config.text)
      }
    },
    {
      prop: 'status',
      label: '状态',
      formatter: (row: any) => {
        const config = getStatusConfig(row.status)
        return h(ElTag, { type: config.type, size: 'small' }, () => config.text)
      }
    },
    { prop: 'sort', label: '排序' },
    { prop: 'view_count', label: '浏览量' },
    { prop: 'created_at', label: '创建时间' }
  ],

  // 搜索配置
  search: {
    enabled: false
  },



  // 操作按钮配置
  actions: {
    enabled: true,
    width: 200, // 增加宽度以容纳3个按钮
    create: {
      enabled: true,
      text: '新增文章',
      type: 'primary'
    },
    edit: {
      enabled: true
    },
    delete: {
      enabled: true,
      confirmText: '确定要删除这篇文章吗？'
    },
    custom: [
      {
        type: 'more',
        text: '状态切换',
        handler: handleStatusToggle
      }
    ]
  }
})

// 事件处理
const handleArticleCreate = () => {
  // 跳转到编辑页面（新增模式）
  router.push('/system/article/list/edit')
}

const handleArticleUpdate = (id: string | number) => {
  // 跳转到编辑页面（编辑模式）
  router.push(`/system/article/list/edit/${id}`)
}



const handleArticleDelete = (id: string | number) => {
  console.log('删除文章:', id)
}

const handleSelectionChange = (rows: any[]) => {
  selectedRows.value = rows
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 篇文章吗？`,
      '批量删除',
      { type: 'warning' }
    )

    const ids = selectedRows.value.map(row => row.id)
    await ArticleService.batchDeleteArticles(ids)
    ElMessage.success('批量删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}
</script>

<style scoped>
.article-cover-cell {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 0;
}

.article-cover-cell .cover-image {
  transition: transform 0.2s ease;
  cursor: pointer;
}

.article-cover-cell .cover-image:hover {
  transform: scale(1.05);
}

.no-cover {
  user-select: none;
  font-weight: 500;
}
</style>
