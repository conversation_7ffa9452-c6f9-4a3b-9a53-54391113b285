<?php
declare(strict_types=1);

namespace app\common\middleware;

use think\Response;

/**
 * 跨域中间件
 */
class Cors
{
    /**
     * 处理请求
     *
     * @param \think\Request $request
     * @param \Closure       $next
     * @return Response
     */
    public function handle($request, \Closure $next)
    {
        // 处理预检请求
        if ($request->method() == 'OPTIONS') {
            $response = response();
        } else {
            $response = $next($request);
        }

        // 设置跨域头
        $response->header([
            'Access-Control-Allow-Origin' => env('CORS_ALLOW_ORIGIN', '*'),
            'Access-Control-Allow-Methods' => env('CORS_ALLOW_METHODS', 'GET,POST,PUT,DELETE,OPTIONS'),
            'Access-Control-Allow-Headers' => env('CORS_ALLOW_HEADERS', 'Authorization,Content-Type,Accept,X-Requested-With'),
            'Access-Control-Allow-Credentials' => 'true',
            'Access-Control-Max-Age' => '86400',
        ]);

        return $response;
    }
}
