# Vue 3 + TypeScript 编码规范

> 避免TypeScript错误的核心规则

## 🎯 响应式数据规范

### 规则1：对象数据使用 ref，不用 reactive
```typescript
// ❌ 错误 - reactive对象不能重新赋值
const formData = reactive({ username: '', email: '' })

// ✅ 正确 - 使用ref可以重新赋值
const formData = ref({ username: '', email: '' })
formData.value = newData // 正确
```

### 规则2：始终提供类型注解
```typescript
// ❌ 错误 - 缺少类型
const data = ref()
const list = ref([])

// ✅ 正确 - 明确类型
const data = ref<User | null>(null)
const list = ref<User[]>([])
```

## 🔧 组件定义规范

### 规则3：Props和Emits必须定义类型
```typescript
// ✅ 正确的Props定义
interface Props {
  title: string
  count?: number
  user: User
}
const props = defineProps<Props>()

// ✅ 正确的Emits定义
interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'search', params: any): void
}
const emit = defineEmits<Emits>()
```

### 规则4：组件引用必须指定类型
```typescript
import type { FormInstance } from 'element-plus'

// ✅ 正确
const formRef = ref<FormInstance>()

// 使用时检查
if (formRef.value) {
  await formRef.value.validate()
}
```

## 🌐 API调用规范

### 规则5：API响应必须定义接口
```typescript
// ✅ 定义响应类型
interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

interface User {
  id: number
  username: string
  email: string
}

// ✅ API函数类型化
const getUsers = (): Promise<ApiResponse<User[]>> => {
  return request.get('/users')
}
```

## 📝 事件处理规范

### 规则6：事件参数必须指定类型
```typescript
// ✅ 正确的事件处理
const handleClick = (event: MouseEvent) => {
  event.preventDefault()
}

const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  console.log(target.value)
}

const handleChange = (value: string | number) => {
  console.log(value)
}
```

## 🔍 安全访问规范

### 规则7：使用可选链避免undefined错误
```typescript
// ❌ 错误 - 可能报错
const userName = user.value.name

// ✅ 正确 - 安全访问
const userName = user.value?.name || ''
const userAge = user.value?.profile?.age ?? 0
```

### 规则8：数组操作前检查类型
```typescript
// ❌ 错误 - 可能报错
const ids = users.map(user => user.id)

// ✅ 正确 - 确保类型
const ids = users.value?.map((user: User) => user.id) || []
```

## 📋 表单规范

### 规则9：表单数据结构化定义
```typescript
// ✅ 定义表单接口
interface UserForm {
  username: string
  email: string
  status: 1 | 2 | 3 // 限制具体值
}

const formData = ref<UserForm>({
  username: '',
  email: '',
  status: 1
})
```

### 规则10：验证规则类型化
```typescript
interface FormRules {
  [key: string]: Array<{
    required?: boolean
    message: string
    trigger?: string
  }>
}

const rules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ]
}
```

## 🎨 计算属性和监听器规范

### 规则11：computed自动推断，复杂类型手动指定
```typescript
// ✅ 自动推断
const fullName = computed(() => {
  return `${user.value.firstName} ${user.value.lastName}`
})

// ✅ 复杂类型手动指定
const userStatus = computed<string>(() => {
  return user.value.status === 1 ? '正常' : '禁用'
})
```

### 规则12：watch参数类型化
```typescript
// ✅ 正确的watch
watch(searchKeyword, (newVal: string, oldVal: string) => {
  console.log(`从 ${oldVal} 变为 ${newVal}`)
})

watch(
  [keyword, page],
  ([newKeyword, newPage]: [string, number]) => {
    fetchData(newKeyword, newPage)
  }
)
```

## 🛠️ 常见错误修复

### 错误1：Cannot assign to constant
```typescript
// ❌ 问题代码
const data = reactive({})
data = newData // 错误

// ✅ 修复方案
const data = ref({})
data.value = newData // 正确
```

### 错误2：Property does not exist
```typescript
// ❌ 问题代码
const name = user.name // user可能为null

// ✅ 修复方案
const name = user?.name || ''
```

### 错误3：Argument of type 'unknown'
```typescript
// ❌ 问题代码
const handleChange = (value) => {} // 参数类型未知

// ✅ 修复方案
const handleChange = (value: string | number) => {}
```

## 📦 类型声明文件

### 全局类型声明
```typescript
// types/global.d.ts
declare global {
  interface Window {
    __APP_VERSION__: string
  }
}

// types/api.d.ts
export interface User {
  id: number
  username: string
  email: string
}

export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}
```

## 🎯 核心原则

1. **明确类型**：所有变量、参数、返回值都要有明确类型
2. **安全访问**：使用可选链(?.)和空值合并(??)
3. **接口优先**：复杂对象用interface定义
4. **ref优于reactive**：对象数据优先使用ref
5. **类型检查**：使用前检查类型和存在性

遵循这些规范可以避免90%的TypeScript错误！
