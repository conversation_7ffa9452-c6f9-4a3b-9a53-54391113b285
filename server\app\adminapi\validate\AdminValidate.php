<?php
declare(strict_types=1);

namespace app\adminapi\validate;

use think\Validate;

/**
 * 管理员验证器
 */
class AdminValidate extends Validate
{
    protected $rule = [
        'userName' => 'require|length:3,50',
        'username' => 'require|length:3,50',
        'password' => 'require|length:6,32',
        'nickname' => 'length:2,50',
        'email' => 'email',
        'phone' => 'mobile',
        'status' => 'in:1,2',
        'roleIds' => 'array',
    ];

    protected $message = [
        'userName.require' => '用户名不能为空',
        'userName.length' => '用户名长度必须在3-50个字符之间',
        'username.require' => '用户名不能为空',
        'username.length' => '用户名长度必须在3-50个字符之间',
        'password.require' => '密码不能为空',
        'password.length' => '密码长度必须在6-32个字符之间',
        'nickname.length' => '昵称长度必须在2-50个字符之间',
        'email.email' => '邮箱格式不正确',
        'phone.mobile' => '手机号格式不正确',
        'status.in' => '状态值不正确',
        'roleIds.array' => '角色ID必须是数组',
    ];

    protected $scene = [
        'login' => ['userName', 'password'],
        'create' => ['username', 'password', 'nickname', 'email', 'phone', 'status', 'roleIds'],
        'update' => ['username', 'nickname', 'email', 'phone', 'status', 'roleIds'],
    ];
}
