var e=Object.defineProperty,t=Object.defineProperties,r=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable,s=(t,r,a)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[r]=a,i=(e,t,r)=>new Promise(((a,o)=>{var p=e=>{try{i(r.next(e))}catch(t){o(t)}},s=e=>{try{i(r.throw(e))}catch(t){o(t)}},i=e=>e.done?a(e.value):Promise.resolve(e.value).then(p,s);i((r=r.apply(e,t)).next())}));import{C as l}from"./index-C8nK2eTm.js";import{A as n}from"./adminApi-BaeDSpcJ.js";import{m,r as c,C as d,D as u,G as y,V as b,u as f,a6 as j,W as g,X as x,aI as h,E as w,p as v,aQ as O}from"./vendor-8T3zXQLl.js";import{_}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-DyBuW4xE.js";import"./index-Bcx6y5fH.js";import"./index-D1l9fF2S.js";import"./index-Drb3fmHv.js";/* empty css                   *//* empty css                      *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./index-BPyeCy-r.js";/* empty css                *//* empty css               *//* empty css                  *//* empty css               */import"./el-form-item-l0sNRNKZ.js";/* empty css                       *//* empty css                 */import"./index-BgthDRX_.js";import"./index-7x-iptGQ.js";/* empty css                  *//* empty css                    */const A=m((P=((e,t)=>{for(var r in t||(t={}))o.call(t,r)&&s(e,r,t[r]);if(a)for(var r of a(t))p.call(t,r)&&s(e,r,t[r]);return e})({},{name:"Admin"}),t(P,r({__name:"index",setup(e){const t=c([]),r={api:{list:n.getAdminList,create:e=>n.createAdmin(e),update:(e,t)=>n.updateAdmin(Number(e),t),delete:e=>n.deleteAdmin(Number(e))},columns:[{type:"selection"},{type:"index",label:"序号"},{prop:"username",label:"用户名"},{prop:"nickname",label:"昵称"},{prop:"email",label:"邮箱"},{prop:"phone",label:"手机号"},{prop:"roles",label:"角色",formatter:e=>e.roles&&0!==e.roles.length?v("div",{class:"flex flex-wrap gap-1"},e.roles.map((e=>v(O,{key:e.id,type:"primary",size:"small",style:"margin-right: 4px; margin-bottom: 2px;"},(()=>e.name))))):v(O,{type:"info",size:"small"},(()=>"无角色"))},{prop:"status",label:"状态",formatter:e=>{const t={1:{type:"success",text:"正常"},2:{type:"danger",text:"禁用"}}[e.status]||{type:"info",text:"未知"};return v(O,{type:t.type,size:"small"},(()=>t.text))}},{prop:"lastLoginTime",label:"最后登录",formatter:e=>e.lastLoginTime||"从未登录"},{prop:"createdAt",label:"创建时间",sortable:!0}],search:{enabled:!1},actions:{enabled:!0,width:240,create:{enabled:!0,text:"新增管理员",type:"primary"},edit:{enabled:!0},delete:{enabled:!0,confirmText:"确定要删除这个管理员吗？"},custom:[{type:"view",text:"重置密码",handler:e=>_(e)},{type:"edit",text:"状态切换",handler:e=>A(e)}]},dialog:{enabled:!0,width:"600px",titles:{create:"新增管理员",edit:"编辑管理员"},formConfig:[{prop:"username",label:"用户名",type:"input",required:!0,span:12},{prop:"password",label:"密码",type:"input",required:!0,span:12,config:{type:"password"}},{prop:"nickname",label:"昵称",type:"input",span:12},{prop:"email",label:"邮箱",type:"input",span:12},{prop:"phone",label:"手机号",type:"input",span:12},{prop:"status",label:"状态",type:"switch",span:12}]},table:{rowKey:"id",stripe:!0,border:!1}},a=e=>{},o=(e,t)=>{},p=e=>{},s=e=>{t.value=e},m=()=>i(this,null,(function*(){try{yield h.confirm(`确定要删除选中的 ${t.value.length} 个管理员吗？`,"批量删除",{type:"warning"}),w.success("批量删除成功")}catch(e){"cancel"!==e&&w.error("批量删除失败")}})),_=e=>i(this,null,(function*(){try{const{value:t}=yield h.prompt(`请输入 ${e.username} 的新密码`,"重置密码",{confirmButtonText:"确定",cancelButtonText:"取消",inputType:"password",inputValidator:e=>e?!(e.length<6)||"密码长度不能少于6位":"密码不能为空"});yield n.resetPassword(e.id,t),w.success("密码重置成功")}catch(t){"cancel"!==t&&w.error("重置密码失败")}})),A=e=>i(this,null,(function*(){try{const t=1===e.status?"禁用":"启用";yield h.confirm(`确定要${t}管理员 ${e.username} 吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield n.toggleStatus(e.id),w.success(`${t}成功`)}catch(t){"cancel"!==t&&w.error("状态切换失败")}}));return(e,t)=>(u(),d(l,{config:r,onCreate:a,onUpdate:o,onDelete:p,onSelectionChange:s},{"header-actions":y((({selectedRows:e})=>[e.length>0?(u(),d(f(j),{key:0,type:"danger",onClick:m},{default:y((()=>[g(" 批量删除 ("+x(e.length)+") ",1)])),_:2},1024)):b("",!0)])),_:1}))}}))));var P;const T=_(A,[["__scopeId","data-v-43c449c5"]]);export{T as default};
