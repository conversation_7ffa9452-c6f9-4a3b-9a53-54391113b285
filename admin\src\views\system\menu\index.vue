<template>
  <div class="menu-page art-full-height">
    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader :showZebra="false" v-model:columns="columnChecks" @refresh="handleRefresh">
        <template #left>
          <!-- 添加菜单按钮 -->
          <ElButton @click="showModel('menu', null, true)" type="primary" v-ripple>
            添加菜单
          </ElButton>
          <ElButton @click="toggleExpand" v-ripple>
            {{ isExpanded ? '收起' : '展开' }}
          </ElButton>
        </template>
      </ArtTableHeader>
      <!-- 表格 -->
      <ArtTable
        ref="tableRef"
        :loading="loading"
        :data="filteredTableData"
        :tableConfig="{
          rowKey: 'path',
          stripe: false
        }"
        :layout="{
          marginTop: 10
        }"
      >
        <template #default>
          <ElTableColumn v-for="col in columns" :key="col.prop || col.type" v-bind="col" />
        </template>
      </ArtTable>

      <ElDialog :title="dialogTitle" v-model="dialogVisible" width="700px" align-center>
        <ElForm ref="formRef" :model="form" :rules="rules" label-width="85px">
          <ElFormItem label="菜单类型">
            <ElRadioGroup v-model="labelPosition" :disabled="disableMenuType">
              <ElRadioButton value="menu">菜单</ElRadioButton>
              <ElRadioButton value="button">权限</ElRadioButton>
            </ElRadioGroup>
          </ElFormItem>

          <template v-if="labelPosition === 'menu'">
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="菜单名称" prop="name">
                  <ElInput v-model="form.name" placeholder="菜单名称"></ElInput>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="路由地址" prop="path">
                  <ElInput v-model="form.path" placeholder="路由地址"></ElInput>
                </ElFormItem>
              </ElCol>
            </ElRow>
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="权限标识" prop="label">
                  <ElInput v-model="form.label" placeholder="权限标识"></ElInput>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="组件路径" prop="component">
                  <ElInput v-model="form.component" placeholder="组件路径，如：/index/index">
                    <template #prepend>
                      <ElButton @click="setDefaultComponent" size="small" type="primary">
                        默认
                      </ElButton>
                    </template>
                  </ElInput>
                </ElFormItem>
              </ElCol>
            </ElRow>

            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="图标" prop="icon">
                  <ArtIconSelector v-model="form.icon" :iconType="iconType" width="100%" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <!-- 空列，保持布局平衡 -->
              </ElCol>
            </ElRow>

            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="父级菜单" prop="parentId">
                  <ElSelect v-model="form.parentId" placeholder="请选择父级菜单" clearable style="width: 100%">
                    <ElOption label="顶级菜单" :value="0" />
                    <ElOption
                      v-for="menu in parentMenuOptions"
                      :key="menu.id"
                      :label="menu.title"
                      :value="menu.id"
                    />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="菜单排序" prop="sort" style="width: 100%">
                  <ElInputNumber
                    v-model="form.sort"
                    style="width: 100%"
                    @change="handleChange"
                    :min="1"
                    controls-position="right"
                  />
                </ElFormItem>
              </ElCol>
            </ElRow>

            <ElRow :gutter="20">
              <ElCol :span="24">
                <ElFormItem label="外部链接" prop="link">
                  <ElInput
                    v-model="form.link"
                    placeholder="外部链接/内嵌地址(https://www.baidu.com)"
                  ></ElInput>
                </ElFormItem>
              </ElCol>
            </ElRow>

            <ElRow :gutter="20">
              <ElCol :span="5">
                <ElFormItem label="是否启用" prop="isEnable">
                  <ElSwitch v-model="form.isEnable"></ElSwitch>
                </ElFormItem>
              </ElCol>
              <ElCol :span="5">
                <ElFormItem label="页面缓存" prop="keepAlive">
                  <ElSwitch v-model="form.keepAlive"></ElSwitch>
                </ElFormItem>
              </ElCol>
              <ElCol :span="5">
                <ElFormItem label="是否显示" prop="isVisible">
                  <ElSwitch v-model="form.isVisible"></ElSwitch>
                </ElFormItem>
              </ElCol>
              <ElCol :span="5">
                <ElFormItem label="是否内嵌" prop="isMenu">
                  <ElSwitch v-model="form.isIframe"></ElSwitch>
                </ElFormItem>
              </ElCol>
            </ElRow>
          </template>

          <template v-if="labelPosition === 'button'">
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="权限名称" prop="authName">
                  <ElInput v-model="form.authName" placeholder="权限名称"></ElInput>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="权限标识" prop="authLabel">
                  <ElInput v-model="form.authLabel" placeholder="权限标识"></ElInput>
                </ElFormItem>
              </ElCol>
            </ElRow>
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="权限排序" prop="authSort" style="width: 100%">
                  <ElInputNumber
                    v-model="form.authSort"
                    style="width: 100%"
                    @change="handleChange"
                    :min="1"
                    controls-position="right"
                  />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </template>
        </ElForm>

        <template #footer>
          <span class="dialog-footer">
            <ElButton @click="dialogVisible = false">取 消</ElButton>
            <ElButton type="primary" @click="submitForm()">确 定</ElButton>
          </span>
        </template>
      </ElDialog>
    </ElCard>
  </div>
</template>

<script setup lang="ts">

  import type { FormInstance, FormRules } from 'element-plus'
  import { ElMessage, ElMessageBox, ElTag, ElTooltip } from 'element-plus'
  import { IconTypeEnum } from '@/enums/appEnum'
  import { formatMenuTitle } from '@/router/utils/utils'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import { useTableColumns } from '@/composables/useTableColumns'
  import { ElButton } from 'element-plus'
  import { menuService } from '@/api/menuApi'
  import { AppRouteRecord } from '@/types/router'


  defineOptions({ name: 'Menus' })

  const loading = ref(false)
  const parentMenuOptions = ref<Array<{id: number, title: string}>>([])  // 父级菜单选项



  // 构建菜单类型标签
  const buildMenuTypeTag = (row: AppRouteRecord) => {
    if (row.children && row.children.length > 0) {
      return 'info'
    } else if (row.meta?.link && row.meta?.isIframe) {
      return 'success'
    } else if (row.path) {
      return 'primary'
    } else if (row.meta?.link) {
      return 'warning'
    }
  }

  // 构建菜单类型文本
  const buildMenuTypeText = (row: AppRouteRecord) => {
    if (row.children && row.children.length > 0) {
      return '目录'
    } else if (row.meta?.link && row.meta?.isIframe) {
      return '内嵌'
    } else if (row.path) {
      return '菜单'
    } else if (row.meta?.link) {
      return '外链'
    }
  }

  // 动态列配置
  const { columnChecks, columns } = useTableColumns(() => [
    {
      prop: 'meta.title',
      label: '菜单名称',
      minWidth: 120,
      formatter: (row: AppRouteRecord) => {
        return formatMenuTitle(row.meta?.title)
      }
    },
    {
      prop: 'type',
      label: '菜单类型',
      formatter: (row: AppRouteRecord) => {
        return h(ElTag, { type: buildMenuTypeTag(row) }, () => buildMenuTypeText(row))
      }
    },
    {
      prop: 'path',
      label: '路由',
      formatter: (row: AppRouteRecord) => {
        return row.meta?.link || row.path || ''
      }
    },
    {
      prop: 'component',
      label: '组件路径',
      formatter: (row: AppRouteRecord) => {
        return row.component || '/index/index'
      }
    },
    {
      prop: 'date',
      label: '编辑时间',
      formatter: () => '2022-3-12 12:00:00'
    },
    {
      prop: 'status',
      label: '隐藏菜单',
      formatter: (row) => {
        return h(ElTag, { type: row.meta.isHide ? 'danger' : 'info' }, () =>
          row.meta.isHide ? '是' : '否'
        )
      }
    },
    {
      prop: 'operation',
      label: '操作',
      width: 180,
      formatter: (row: AppRouteRecord) => {
        const buttons = []

        // 增加按钮
        buttons.push(
          h(ElTooltip, {
            content: '添加子菜单',
            placement: 'top'
          }, () =>
            h(ArtButtonTable, {
              type: 'add',
              onClick: () => showModel('menu', null, true)
            })
          )
        )

        // 编辑按钮
        buttons.push(
          h(ElTooltip, {
            content: '编辑',
            placement: 'top'
          }, () =>
            h(ArtButtonTable, {
              type: 'edit',
              onClick: () => showDialog('edit', row)
            })
          )
        )

        // 删除按钮
        buttons.push(
          h(ElTooltip, {
            content: '删除',
            placement: 'top'
          }, () =>
            h(ArtButtonTable, {
              type: 'delete',
              onClick: () => deleteMenu(row)
            })
          )
        )

        return h('div', { class: 'flex gap-2' }, buttons)
      }
    }
  ])

  const handleRefresh = () => {
    getTableData()
  }

  const dialogVisible = ref(false)
  const form = reactive({
    // 菜单
    name: '',
    path: '',
    label: '',
    component: '',  // 组件路径
    icon: '',
    isEnable: true,
    sort: 1,
    isMenu: true,
    keepAlive: true,
    isVisible: true,  // 改为 isVisible，默认显示
    link: '',
    isIframe: false,
    parentId: 0,  // 父级菜单ID，0表示顶级菜单
    // 权限 (修改这部分)
    authName: '',
    authLabel: '',
    authIcon: '',
    authSort: 1
  })
  const iconType = ref(IconTypeEnum.UNICODE)

  const labelPosition = ref('menu')
  const rules = reactive<FormRules>({
    name: [
      { required: true, message: '请输入菜单名称', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    path: [{ required: true, message: '请输入路由地址', trigger: 'blur' }],
    label: [{ required: true, message: '输入权限标识', trigger: 'blur' }],
    // 修改这部分
    authName: [{ required: true, message: '请输入权限名称', trigger: 'blur' }],
    authLabel: [{ required: true, message: '请输入权限权限标识', trigger: 'blur' }]
  })

  const tableData = ref<AppRouteRecord[]>([])

  onMounted(() => {
    getTableData()
  })

  const getTableData = async () => {
    loading.value = true
    try {
      // 调用API获取最新的菜单数据
      const response = await menuService.getMenuList()
      tableData.value = response.menuList
      // 同时更新父级菜单选项
      updateParentMenuOptions(response.menuList)
    } catch (error) {
      console.error('获取菜单数据失败:', error)
      ElMessage.error('获取菜单数据失败')
    } finally {
      loading.value = false
    }
  }

  // 更新父级菜单选项
  const updateParentMenuOptions = (menus: AppRouteRecord[]) => {
    const options: Array<{id: number, title: string}> = []
    const extractMenus = (menuList: AppRouteRecord[]) => {
      menuList.forEach(menu => {
        // 只添加菜单类型的项目作为父级选项
        options.push({
          id: menu.id || 0,
          title: menu.meta.title
        })
        // 递归处理子菜单
        if (menu.children && menu.children.length > 0) {
          extractMenus(menu.children)
        }
      })
    }
    extractMenus(menus)
    parentMenuOptions.value = options
  }

  // 过滤后的表格数据
  const filteredTableData = computed(() => {
    return tableData.value || []
  })

  const isEdit = ref(false)
  const currentEditId = ref<number | null>(null)
  const formRef = ref<FormInstance>()
  const dialogTitle = computed(() => {
    const type = labelPosition.value === 'menu' ? '菜单' : '权限'
    return isEdit.value ? `编辑${type}` : `新建${type}`
  })

  const showDialog = (type: string, row: AppRouteRecord) => {
    showModel('menu', row, true)
  }

  const handleChange = () => {}

  // 设置默认组件路径
  const setDefaultComponent = () => {
    form.component = '/index/index'
  }

  const submitForm = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          // 准备提交数据 - 映射到数据库字段
          const submitData = {
            name: form.label,           // 路由名称 -> name
            title: form.name,           // 菜单名称 -> title
            path: form.path,            // 路由路径
            component: form.component || '/index/index',  // 组件路径，默认使用 /index/index
            icon: form.icon,            // 图标
            sort: form.sort || 0,       // 排序
            type: 1,                    // 类型：1菜单，2按钮
            status: form.isVisible ? 1 : 0,  // 状态：1显示，0隐藏
            keep_alive: form.keepAlive ? 1 : 0,  // 是否缓存
            external_link: form.link,   // 外部链接
            is_iframe: form.isIframe ? 1 : 0,    // 是否iframe
            parent_id: form.parentId || 0        // 父级菜单ID
          }

          if (isEdit.value && currentEditId.value) {
            // 编辑菜单
            await menuService.updateMenu(currentEditId.value, submitData)
            ElMessage.success('编辑成功')
          } else {
            // 新增菜单
            await menuService.createMenu(submitData)
            ElMessage.success('新增成功')
          }

          dialogVisible.value = false
          // 刷新表格数据
          await getTableData()
        } catch (error) {
          console.error('提交失败:', error)
          ElMessage.error(`${isEdit.value ? '编辑' : '新增'}失败`)
        }
      }
    })
  }

  const showModel = (type: string, row?: any, lock: boolean = false) => {
    dialogVisible.value = true
    labelPosition.value = type
    isEdit.value = false
    currentEditId.value = null
    lockMenuType.value = lock
    resetForm()

    if (row) {
      isEdit.value = true
      currentEditId.value = row.id || null
      nextTick(() => {
        // 回显数据
        if (type === 'menu') {
          // 菜单数据回显
          form.name = formatMenuTitle(row.meta.title)
          form.path = row.path
          form.label = row.name
          form.component = row.component || '/index/index'  // 组件路径回显
          form.icon = row.meta.icon
          form.sort = row.meta.sort || 1
          form.isMenu = row.meta.isMenu
          form.keepAlive = row.meta.keepAlive
          form.isVisible = !row.meta.isHide  // 从后端的 isHide 转换为 isVisible
          form.isEnable = row.meta.isEnable || true
          form.link = row.meta.link
          form.isIframe = row.meta.isIframe || false
          form.parentId = row.parent_id || 0  // 父级菜单ID
        } else {
          // 权限按钮数据回显
          form.authName = row.title
          form.authLabel = row.authMark
          form.authIcon = row.icon || ''
          form.authSort = row.sort || 1
        }
      })
    }
  }

  const resetForm = () => {
    formRef.value?.resetFields()
    Object.assign(form, {
      // 菜单
      name: '',
      path: '',
      label: '',
      component: '',  // 组件路径
      icon: '',
      sort: 1,
      isMenu: true,
      keepAlive: true,
      isVisible: true,
      link: '',
      isIframe: false,
      parentId: 0,
      // 权限
      authName: '',
      authLabel: '',
      authIcon: '',
      authSort: 1
    })
  }

  const deleteMenu = async (row?: AppRouteRecord) => {
    if (!row || !row.id) {
      ElMessage.error('无法获取菜单信息')
      return
    }

    try {
      await ElMessageBox.confirm('确定要删除该菜单吗？删除后无法恢复', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await menuService.deleteMenu(row.id)
      ElMessage.success('删除成功')
      // 刷新表格数据
      await getTableData()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      } else {
        ElMessage.info('已取消删除')
      }
    }
  }



  // 修改计算属性，增加锁定控制参数
  const disableMenuType = computed(() => {
    // 编辑权限时锁定为权限类型
    if (isEdit.value && labelPosition.value === 'button') return true
    // 编辑菜单时锁定为菜单类型
    if (isEdit.value && labelPosition.value === 'menu') return true
    // 顶部添加菜单按钮时锁定为菜单类型
    if (!isEdit.value && labelPosition.value === 'menu' && lockMenuType.value) return true
    return false
  })

  // 添加一个控制变量
  const lockMenuType = ref(false)

  const isExpanded = ref(false)
  const tableRef = ref()

  const toggleExpand = () => {
    isExpanded.value = !isExpanded.value
    nextTick(() => {
      if (tableRef.value) {
        tableRef.value[isExpanded.value ? 'expandAll' : 'collapseAll']()
      }
    })
  }
</script>

<style lang="scss" scoped>
  .menu-page {
    .svg-icon {
      width: 1.8em;
      height: 1.8em;
      overflow: hidden;
      vertical-align: -8px;
      fill: currentcolor;
    }

    :deep(.small-btn) {
      height: 30px !important;
      padding: 0 10px !important;
      font-size: 12px !important;
    }

    /* 权限标签样式优化 */
    :deep(.el-tag) {
      margin: 2px !important;
      cursor: pointer !important;
    }

    /* 权限标签容器样式 */
    .auth-tags-container {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      align-items: center;
      max-width: 200px;
    }

    /* 操作按钮容器样式 */
    .operation-buttons {
      display: flex;
      gap: 4px;
      align-items: center;
    }

    /* 通用flex布局 */
    .flex {
      display: flex;
    }

    .gap-2 {
      gap: 8px;
    }
  }
</style>
