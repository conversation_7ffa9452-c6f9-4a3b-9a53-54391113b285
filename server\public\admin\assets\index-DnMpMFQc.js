var r=Object.defineProperty,o=Object.defineProperties,e=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,m=(o,e,t)=>e in o?r(o,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[e]=t;import{o as p}from"./index-TSQrMSQp.js";/* empty css               *//* empty css               */import a from"./CardList-Bx6wPt5p.js";import l from"./ActiveUser-WIQbV4Xw.js";import j from"./SalesOverview-BPjIHf7o.js";import n from"./NewUser-CTf73Wxz.js";import d from"./Dynamic-B_QrQ6oY.js";import c from"./TodoList-DxaYgyxW.js";import f from"./AboutProject-BVHIZS6-.js";import{k as u,P as b,D as g,x as _,G as v,aK as O,aL as y}from"./vendor-84Inc-Pt.js";import{_ as x}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-XUvv1IdG.js";/* empty css                   */import"./useChart-CzFsSt4f.js";import"./index-DacxPkpy.js";/* empty css                      *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                    *//* empty css                       *//* empty css                        */import"./avatar6-BkZJe8A3.js";const P={class:"console"},w=u((h=((r,o)=>{for(var e in o||(o={}))s.call(o,e)&&m(r,e,o[e]);if(t)for(var e of t(o))i.call(o,e)&&m(r,e,o[e]);return r})({},{name:"Console"}),o(h,e({__name:"index",setup:r=>(p().scrollToTop(),(r,o)=>{const e=O,t=y;return g(),b("div",P,[_(a),_(t,{gutter:20},{default:v((()=>[_(e,{sm:24,md:12,lg:10},{default:v((()=>[_(l)])),_:1}),_(e,{sm:24,md:12,lg:14},{default:v((()=>[_(j)])),_:1})])),_:1}),_(t,{gutter:20},{default:v((()=>[_(e,{sm:24,md:24,lg:12},{default:v((()=>[_(n)])),_:1}),_(e,{sm:24,md:12,lg:6},{default:v((()=>[_(d)])),_:1}),_(e,{sm:24,md:12,lg:6},{default:v((()=>[_(c)])),_:1})])),_:1}),_(f)])})}))));var h;const D=x(w,[["__scopeId","data-v-699cd4c1"]]);export{D as default};
