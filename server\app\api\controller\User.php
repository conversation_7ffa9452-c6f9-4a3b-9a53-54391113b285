<?php
declare(strict_types=1);

namespace app\api\controller;

use app\common\controller\BaseController;
use app\common\model\User as UserModel;
use app\api\validate\UserValidate;

/**
 * 移动端用户控制器
 */
class User extends BaseController
{
    /**
     * 获取用户资料
     */
    public function profile()
    {
        $userId = $this->request->user['id'];
        $user = UserModel::find($userId);
        
        if (!$user) {
            return $this->error('用户不存在', 401);
        }

        $userInfo = [
            'userId' => $user->id,  // 统一使用 userId
            'id' => $user->id,      // 保持兼容性
            'userName' => $user->username,  // 前端期望的字段名
            'username' => $user->username,  // 保持兼容性
            'nickname' => $user->nickname,
            'avatar' => $user->avatar ?? '',
            'email' => $user->email ?? '',
            'phone' => $user->phone ?? '',
            'gender' => $user->gender,
            'genderText' => $user->gender_text,
            'birthday' => $user->birthday,
            'status' => $user->status,
            'createdAt' => $user->created_at
        ];

        return $this->success($userInfo);
    }

    /**
     * 更新用户资料
     */
    public function update()
    {
        $userId = $this->request->user['id'];
        $data = $this->request->post();
        
        $validate = new UserValidate();
        if (!$validate->scene('update')->check($data)) {
            return $this->error($validate->getError());
        }

        try {
            $user = UserModel::find($userId);
            if (!$user) {
                return $this->error('用户不存在');
            }
            
            // 如果没有传密码，则不更新密码
            if (empty($data['password'])) {
                unset($data['password']);
            }
            
            $user->save($data);
            return $this->success($user, '更新成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 上传头像
     */
    public function avatar()
    {
        $file = $this->request->file('avatar');
        
        if (!$file) {
            return $this->error('请选择头像文件');
        }

        // 验证文件类型
        $allowTypes = ['jpg', 'jpeg', 'png', 'gif'];
        if (!in_array(strtolower($file->extension()), $allowTypes)) {
            return $this->error('不支持的图片格式');
        }

        // 验证文件大小 (2MB)
        if ($file->getSize() > 2 * 1024 * 1024) {
            return $this->error('头像大小不能超过2MB');
        }

        try {
            $userId = $this->request->user['id'];
            $result = \app\common\service\UploadService::uploadImage($file, ['id' => $userId, 'type' => 'user']);
            
            // 更新用户头像
            $user = UserModel::find($userId);
            $user->avatar = $result['url'];
            $user->save();
            
            return $this->success(['avatar' => $result['url']], '头像上传成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
