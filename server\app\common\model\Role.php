<?php
declare(strict_types=1);

namespace app\common\model;

use think\Model;

/**
 * 角色模型
 */
class Role extends Model
{
    protected $table = 'fs_role';
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    // 字段类型转换
    protected $type = [
        'status' => 'integer',
    ];

    // 获取器 - 将数据库字段映射到模型属性
    public function getNameAttr($value, $data)
    {
        return $data['role_name'] ?? '';
    }

    public function getCodeAttr($value, $data)
    {
        return $data['role_code'] ?? '';
    }

    /**
     * 关联菜单
     */
    public function menu()
    {
        return $this->belongsToMany(Menu::class, 'role_menu', 'menu_id', 'role_id');
    }

    /**
     * 关联权限
     */
    public function permission()
    {
        return $this->belongsToMany(Permission::class, 'role_permission', 'permission_id', 'role_id');
    }

    /**
     * 关联管理员
     */
    public function admin()
    {
        return $this->belongsToMany(Admin::class, 'admin_role', 'admin_id', 'role_id');
    }

    /**
     * 状态获取器
     * @param $value
     * @return string
     */
    public function getStatusTextAttr($value): string
    {
        $status = [0 => '禁用', 1 => '启用'];
        return $status[$this->status] ?? '未知';
    }
}
