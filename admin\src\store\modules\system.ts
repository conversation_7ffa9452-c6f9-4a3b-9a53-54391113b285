import { defineStore } from 'pinia'
import { ref } from 'vue'
import { SystemService, type SystemConfigFormData } from '@/api/systemApi'
import { resolveFileUrl } from '@/utils/url'

export const useSystemStore = defineStore('system', () => {
  // 系统配置数据
  const systemConfig = ref<SystemConfigFormData>({
    site_name: 'Art Design Pro',
    site_logo: '',
    site_favicon: '',
    site_description: '',
    site_keywords: '',
    site_copyright: '',
    site_icp: '',
    upload_max_size: 10,
    upload_allowed_ext: 'jpg,jpeg,png,gif,webp,pdf,doc,docx,xls,xlsx',
    logo_size: 48
  })

  // 加载状态
  const loading = ref(false)

  // 获取系统配置
  const loadSystemConfig = async () => {
    if (loading.value) return
    
    loading.value = true
    try {
      const data = await SystemService.getConfig()
      if (data) {
        systemConfig.value = { ...systemConfig.value, ...data }
        
        // 更新页面标题
        if (data.site_name) {
          document.title = data.site_name
        }
        
        // 更新favicon
        if (data.site_favicon) {
          updateFavicon(data.site_favicon)
        }
      }
    } catch (error) {
      console.error('加载系统配置失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 更新系统配置
  const updateSystemConfig = (config: Partial<SystemConfigFormData>) => {
    systemConfig.value = { ...systemConfig.value, ...config }
    
    // 更新页面标题
    if (config.site_name) {
      document.title = config.site_name
    }
    
    // 更新favicon
    if (config.site_favicon) {
      updateFavicon(config.site_favicon)
    }
  }

  // 更新favicon
  const updateFavicon = (faviconPath: string) => {
    const link = document.querySelector("link[rel*='icon']") as HTMLLinkElement || document.createElement('link')
    link.type = 'image/x-icon'
    link.rel = 'shortcut icon'
    link.href = resolveFileUrl(faviconPath) // 自动处理URL
    document.getElementsByTagName('head')[0].appendChild(link)
  }

  // 获取网站名称
  const getSiteName = () => {
    return systemConfig.value.site_name || 'Art Design Pro'
  }

  // 获取网站Logo
  const getSiteLogo = () => {
    return systemConfig.value.site_logo || ''
  }

  // 获取网站图标
  const getSiteFavicon = () => {
    return systemConfig.value.site_favicon || ''
  }

  // 获取Logo尺寸
  const getLogoSize = () => {
    return systemConfig.value.logo_size || 48
  }

  return {
    systemConfig,
    loading,
    loadSystemConfig,
    updateSystemConfig,
    getSiteName,
    getSiteLogo,
    getSiteFavicon,
    getLogoSize
  }
}, {
  persist: {
    key: 'system',
    storage: localStorage
  }
})
