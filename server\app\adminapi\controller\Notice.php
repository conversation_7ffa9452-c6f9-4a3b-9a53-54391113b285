<?php
declare(strict_types=1);

namespace app\adminapi\controller;

use app\common\controller\BaseController;
use app\adminapi\validate\NoticeValidate;
use app\common\service\NoticeService;
use app\common\model\Notice as NoticeModel;
use think\response\Json;
use think\exception\ValidateException;

/**
 * 公告管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class Notice extends BaseController
{
    /**
     * 获取公告列表
     *
     * @return Json
     */
    public function list(): Json
    {
        try {
            $params = $this->request->param();
            $result = NoticeService::getList($params);

            return $this->success($result, '获取列表成功');
        } catch (\Exception $e) {
            return $this->error('获取列表失败：' . $e->getMessage());
        }
    }

    /**
     * 获取公告详情
     *
     * @return Json
     */
    public function detail(): Json
    {
        try {
            $id = (int)$this->request->param('id');

            if ($id <= 0) {
                return $this->error('无效的公告ID');
            }

            $model = NoticeService::getDetail($id);
            return $this->success($model->toArray(), '获取详情成功');
        } catch (\Exception $e) {
            return $this->error('获取详情失败：' . $e->getMessage());
        }
    }

    /**
     * 创建公告
     *
     * @return Json
     */
    public function create(): Json
    {
        try {
            $data = $this->request->post();

            if (empty($data)) {
                return $this->error('请提供有效的数据');
            }

            // 数据验证
            $validate = new NoticeValidate();
            if (!$validate->scene('create')->check($data)) {
                return $this->error($validate->getError());
            }

            $model = NoticeService::create($data);
            return $this->success($model->toArray(), '创建成功');
        } catch (ValidateException $e) {
            return $this->error('数据验证失败：' . $e->getMessage());
        } catch (\Exception $e) {
            return $this->error('创建失败：' . $e->getMessage());
        }
    }

    /**
     * 更新公告
     *
     * @return Json
     */
    public function update(): Json
    {
        try {
            $id = (int)$this->request->param('id');
            $data = $this->request->put();

            if ($id <= 0) {
                return $this->error('无效的公告ID');
            }

            if (empty($data)) {
                return $this->error('请提供有效的更新数据');
            }

            $data['id'] = $id; // 用于验证器检查唯一性

            // 数据验证
            $validate = new NoticeValidate();
            if (!$validate->scene('update')->check($data)) {
                return $this->error($validate->getError());
            }

            unset($data['id']); // 移除ID，避免更新主键
            $model = NoticeService::update($id, $data);

            return $this->success($model->toArray(), '更新成功');
        } catch (ValidateException $e) {
            return $this->error('数据验证失败：' . $e->getMessage());
        } catch (\Exception $e) {
            return $this->error('更新失败：' . $e->getMessage());
        }
    }

    /**
     * 删除公告
     *
     * @return Json
     */
    public function delete(): Json
    {
        try {
            $id = (int)$this->request->param('id');

            if ($id <= 0) {
                return $this->error('无效的公告ID');
            }

            $notice = NoticeModel::find($id);
            if (!$notice) {
                return $this->error('公告不存在');
            }

            $notice->delete();
            return $this->success([], '删除成功');
        } catch (\Exception $e) {
            return $this->error('删除失败：' . $e->getMessage());
        }
    }
}
