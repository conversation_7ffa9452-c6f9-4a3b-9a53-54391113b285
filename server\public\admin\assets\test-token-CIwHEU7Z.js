var e=Object.defineProperty,t=Object.defineProperties,s=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,n=(t,s,a)=>s in t?e(t,s,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[s]=a,o=(e,t,s)=>new Promise(((a,r)=>{var l=e=>{try{o(s.next(e))}catch(t){r(t)}},n=e=>{try{o(s.throw(e))}catch(t){r(t)}},o=e=>e.done?a(e.value):Promise.resolve(e.value).then(l,n);o((s=s.apply(e,t)).next())}));import{b as i,E as u}from"./index-Bcx6y5fH.js";import{m as c,r as p,P as m,D as d,x as f,G as y,R as _,u as v,a6 as g,W as k,F as h,$ as b,aT as T,X as j}from"./vendor-8T3zXQLl.js";import{_ as O}from"./_plugin-vue_export-helper-BCo6x5W8.js";const P={class:"test-token-page"},w={class:"test-buttons"},x={class:"test-results"},C={class:"timestamp"},D={class:"message"},S=c(($=((e,t)=>{for(var s in t||(t={}))r.call(t,s)&&n(e,s,t[s]);if(a)for(var s of a(t))l.call(t,s)&&n(e,s,t[s]);return e})({},{name:"TestToken"}),t($,s({__name:"test-token",setup(e){const t=i(),s=p([]),a=e=>{s.value.unshift({timestamp:(new Date).toLocaleTimeString(),message:e})},r=()=>o(this,null,(function*(){a("开始测试单个请求...");try{yield u.get({url:"/user/list"}),a("✅ 单个请求成功")}catch(e){a(`❌ 单个请求失败: ${e}`)}})),l=()=>o(this,null,(function*(){a("开始测试多个并发请求...");const e=[u.get({url:"/user/list"}),u.get({url:"/role/list"}),u.get({url:"/menu/list"}),u.get({url:"/system/config"}),u.get({url:"/admin/list"})];try{yield Promise.allSettled(e),a("✅ 并发请求完成")}catch(t){a(`❌ 并发请求失败: ${t}`)}})),n=()=>{a("模拟Token过期..."),t.setToken("invalid_token_12345"),a("⚠️ Token已设置为无效值，下次请求将触发401错误")},c=()=>{a("清空Token..."),t.setToken(""),a("🗑️ Token已清空")};return(e,t)=>(d(),m("div",P,[f(v(T),null,{header:y((()=>t[0]||(t[0]=[_("h3",null,"Token过期测试页面",-1)]))),default:y((()=>[_("div",w,[f(v(g),{onClick:r,type:"primary"},{default:y((()=>t[1]||(t[1]=[k(" 测试单个请求 ")]))),_:1,__:[1]}),f(v(g),{onClick:l,type:"warning"},{default:y((()=>t[2]||(t[2]=[k(" 测试多个并发请求 ")]))),_:1,__:[2]}),f(v(g),{onClick:n,type:"danger"},{default:y((()=>t[3]||(t[3]=[k(" 模拟Token过期 ")]))),_:1,__:[3]}),f(v(g),{onClick:c,type:"info"},{default:y((()=>t[4]||(t[4]=[k(" 清空Token ")]))),_:1,__:[4]})]),_("div",x,[t[5]||(t[5]=_("h4",null,"测试结果：",-1)),(d(!0),m(h,null,b(s.value,((e,t)=>(d(),m("div",{class:"result-item",key:t},[_("span",C,j(e.timestamp),1),_("span",D,j(e.message),1)])))),128))])])),_:1})]))}}))));var $;const E=O(S,[["__scopeId","data-v-5df782f0"]]);export{E as default};
