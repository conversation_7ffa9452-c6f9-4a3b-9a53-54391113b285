import type { RouteRecordRaw } from 'vue-router'
import { autoRouteConfig, getRouteParams, getPageMeta } from '../config/autoRouteConfig'

/**
 * 自动路由生成器
 * 基于文件系统自动生成路由配置
 */

/**
 * 动态导入所有页面组件
 */
const pageModules: Record<string, () => Promise<any>> = import.meta.glob('../../views/**/*.vue')

/**
 * 生成自动路由
 * 扫描 views 目录下的所有 .vue 文件，自动生成路由配置
 */
export function generateAutoRoutes(): RouteRecordRaw[] {
  // 如果禁用自动路由，返回空数组
  if (!autoRouteConfig.enabled) {
    return []
  }

  const autoRoutes: RouteRecordRaw[] = []

  // 系统管理页面的自动路由容器
  const systemPagesRoute: RouteRecordRaw = {
    path: '/system',
    component: () => import('@/views/index/index.vue'),
    name: 'SystemPages',
    meta: { title: '系统页面', isHideTab: true },
    children: []
  }

  // 遍历所有页面模块
  Object.keys(pageModules).forEach(modulePath => {
    const route = createRouteFromPath(modulePath)
    if (route && shouldIncludeInAutoRoutes(modulePath)) {
      // 根据路径判断应该添加到哪个路由组
      if (modulePath.includes('/system/')) {
        systemPagesRoute.children!.push(route)
      } else {
        autoRoutes.push(route)
      }
    }
  })

  // 只有当有子路由时才添加系统页面路由
  if (systemPagesRoute.children!.length > 0) {
    autoRoutes.push(systemPagesRoute)
  }

  return autoRoutes
}

/**
 * 根据文件路径创建路由配置
 */
function createRouteFromPath(modulePath: string): RouteRecordRaw | null {
  // 移除 '../../views' 前缀和 '.vue' 后缀
  const cleanPath = modulePath.replace('../../views', '').replace('.vue', '')
  
  // 跳过 index.vue 文件，这些通常是列表页面，由菜单系统处理
  if (cleanPath.endsWith('/index')) {
    return null
  }

  // 生成路由路径
  const routePath = generateRoutePath(cleanPath)
  
  // 生成路由名称
  const routeName = generateRouteName(cleanPath)
  
  // 生成路由元数据
  const meta = generateRouteMeta(cleanPath)

  return {
    path: routePath,
    name: routeName,
    component: pageModules[modulePath],
    meta
  }
}

/**
 * 生成路由路径
 */
function generateRoutePath(cleanPath: string): string {
  const segments = cleanPath.split('/').filter(Boolean)
  const fileName = segments[segments.length - 1]

  // 使用配置文件中的参数配置
  const params = getRouteParams(fileName)

  return cleanPath + params
}

/**
 * 生成路由名称
 */
function generateRouteName(cleanPath: string): string {
  // 将路径转换为驼峰命名
  return cleanPath
    .split('/')
    .filter(Boolean)
    .map(segment => segment.charAt(0).toUpperCase() + segment.slice(1))
    .join('')
}

/**
 * 生成路由元数据
 */
function generateRouteMeta(cleanPath: string) {
  const segments = cleanPath.split('/').filter(Boolean)
  const fileName = segments[segments.length - 1]

  // 使用配置文件中的页面元数据配置
  return getPageMeta(fileName)
}

/**
 * 判断是否应该包含在自动路由中
 */
function shouldIncludeInAutoRoutes(modulePath: string): boolean {
  // 使用配置文件中的排除模式
  return !autoRouteConfig.excludePatterns.some(pattern =>
    modulePath.includes(pattern)
  )
}

/**
 * 获取路由配置信息（用于调试）
 */
export function getAutoRouteInfo(): { total: number; routes: string[] } {
  const routes = generateAutoRoutes()
  const routePaths: string[] = []
  
  function collectPaths(routes: RouteRecordRaw[], prefix = '') {
    routes.forEach(route => {
      const fullPath = prefix + route.path
      routePaths.push(fullPath)
      
      if (route.children) {
        collectPaths(route.children, fullPath)
      }
    })
  }
  
  collectPaths(routes)
  
  return {
    total: routePaths.length,
    routes: routePaths
  }
}
