<?php
declare(strict_types=1);

namespace app\api\validate;

use think\Validate;

/**
 * 移动端用户验证器
 */
class UserValidate extends Validate
{
    protected $rule = [
        'username' => 'require|length:3,50|unique:fs_user',
        'password' => 'require|length:6,32',
        'nickname' => 'length:2,50',
        'email' => 'email',
        'phone' => 'mobile',
        'gender' => 'in:0,1,2',
    ];

    protected $message = [
        'username.require' => '用户名不能为空',
        'username.length' => '用户名长度必须在3-50个字符之间',
        'username.unique' => '用户名已存在',
        'password.require' => '密码不能为空',
        'password.length' => '密码长度必须在6-32个字符之间',
        'nickname.length' => '昵称长度必须在2-50个字符之间',
        'email.email' => '邮箱格式不正确',
        'phone.mobile' => '手机号格式不正确',
        'gender.in' => '性别值不正确',
    ];

    protected $scene = [
        'login' => ['username', 'password'],
        'register' => ['username', 'password', 'nickname', 'email', 'phone'],
        'update' => ['nickname', 'email', 'phone', 'gender'],
    ];
}
