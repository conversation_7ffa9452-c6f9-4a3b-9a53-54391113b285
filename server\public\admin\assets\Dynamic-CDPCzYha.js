import{m as a,M as e,P as s,D as t,ai as r,R as p,F as c,$ as d,u as n,X as l}from"./vendor-8T3zXQLl.js";import{_ as u}from"./_plugin-vue_export-helper-BCo6x5W8.js";const i={class:"card art-custom-card"},m={class:"list"},v={class:"user"},b={class:"type"},o={class:"target"},y=u(a({__name:"Dynamic",setup(a){const u=e([{username:"中小鱼",type:"关注了",target:"誶誶淰"},{username:"何小荷",type:"发表文章",target:"Vue3 + Typescript + Vite 项目实战笔记"},{username:"誶誶淰",type:"提出问题",target:"主题可以配置吗"},{username:"发呆草",type:"兑换了物品",target:"《奇特的一生》"},{username:"甜筒",type:"关闭了问题",target:"发呆草"},{username:"冷月呆呆",type:"兑换了物品",target:"《高效人士的七个习惯》"}]);return(a,e)=>(t(),s("div",i,[e[0]||(e[0]=r('<div class="card-header" data-v-902b6e1b><div class="title" data-v-902b6e1b><h4 class="box-title" data-v-902b6e1b>动态</h4><p class="subtitle" data-v-902b6e1b>新增<span class="text-success" data-v-902b6e1b>+6</span></p></div></div>',1)),p("div",m,[(t(!0),s(c,null,d(n(u),((a,e)=>(t(),s("div",{key:e},[p("span",v,l(a.username),1),p("span",b,l(a.type),1),p("span",o,l(a.target),1)])))),128))])]))}}),[["__scopeId","data-v-902b6e1b"]]);export{y as default};
