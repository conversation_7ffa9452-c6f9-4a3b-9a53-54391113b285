-- 更新文章表以匹配新的表单参数
-- 执行时间：2024-12-01
-- 说明：根据新的编辑表单调整文章表结构

-- 1. 检查当前表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'fs_articles'
ORDER BY ORDINAL_POSITION;

-- 2. 添加缺失的字段（如果不存在）

-- 检查并添加 is_featured 字段（推荐状态）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'fs_articles'
     AND COLUMN_NAME = 'is_featured') > 0,
    'SELECT "is_featured字段已存在" as result',
    'ALTER TABLE `fs_articles` ADD COLUMN `is_featured` tinyint(1) NOT NULL DEFAULT 0 COMMENT "推荐状态：0=不推荐，1=推荐" AFTER `author_name`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 is_visible 字段（替代原来的 is_published）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'fs_articles'
     AND COLUMN_NAME = 'is_visible') > 0,
    'SELECT "is_visible字段已存在" as result',
    'ALTER TABLE `fs_articles` ADD COLUMN `is_visible` tinyint(1) NOT NULL DEFAULT 1 COMMENT "显示状态：0=隐藏，1=显示" AFTER `is_featured`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 修改现有字段以匹配表单需求

-- 确保 title 字段长度为 200
ALTER TABLE `fs_articles` MODIFY COLUMN `title` varchar(200) NOT NULL COMMENT '文章标题';

-- 确保 summary 字段类型正确
ALTER TABLE `fs_articles` MODIFY COLUMN `summary` text COMMENT '文章简介';

-- 确保 content 字段类型正确（支持长文本）
ALTER TABLE `fs_articles` MODIFY COLUMN `content` longtext COMMENT '文章内容';

-- 确保 category_id 字段可为空
ALTER TABLE `fs_articles` MODIFY COLUMN `category_id` int(11) unsigned DEFAULT NULL COMMENT '分类ID';

-- 确保 cover_image 字段长度足够
ALTER TABLE `fs_articles` MODIFY COLUMN `cover_image` varchar(500) DEFAULT NULL COMMENT '封面图片URL';

-- 确保 author_name 字段长度合适
ALTER TABLE `fs_articles` MODIFY COLUMN `author_name` varchar(100) DEFAULT NULL COMMENT '作者姓名';

-- 确保 sort 字段类型正确
ALTER TABLE `fs_articles` MODIFY COLUMN `sort` int(11) unsigned NOT NULL DEFAULT 0 COMMENT '排序（数值越大越靠前）';

-- 删除不需要的字段
-- 删除 slug 字段（别名字段不再需要）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'fs_articles'
     AND COLUMN_NAME = 'slug') > 0,
    'ALTER TABLE `fs_articles` DROP COLUMN `slug`',
    'SELECT "slug字段不存在，无需删除" as result'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除 is_published 字段（已被 is_visible 替代）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'fs_articles'
     AND COLUMN_NAME = 'is_published') > 0,
    'ALTER TABLE `fs_articles` DROP COLUMN `is_published`',
    'SELECT "is_published字段不存在，无需删除" as result'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 数据迁移：将 is_published 数据迁移到 is_visible（如果需要）
UPDATE `fs_articles` 
SET `is_visible` = `is_published` 
WHERE `is_visible` IS NULL OR `is_visible` = 0;

-- 5. 添加索引优化查询性能
-- 为 is_visible 字段添加索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'fs_articles' 
     AND INDEX_NAME = 'idx_is_visible') > 0,
    'SELECT "idx_is_visible索引已存在" as result',
    'ALTER TABLE `fs_articles` ADD INDEX `idx_is_visible` (`is_visible`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为 sort 字段添加索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'fs_articles' 
     AND INDEX_NAME = 'idx_sort') > 0,
    'SELECT "idx_sort索引已存在" as result',
    'ALTER TABLE `fs_articles` ADD INDEX `idx_sort` (`sort`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 查看更新后的表结构
SELECT 
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    COLUMN_TYPE as '完整类型',
    IS_NULLABLE as '可为空',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '注释'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'fs_articles'
ORDER BY ORDINAL_POSITION;

-- 7. 查看表的索引信息
SHOW INDEX FROM `fs_articles`;

-- 8. 插入测试数据（可选）
INSERT IGNORE INTO `fs_articles` (
    `title`, 
    `summary`, 
    `content`, 
    `category_id`, 
    `cover_image`, 
    `author_name`, 
    `is_visible`, 
    `sort`,
    `created_at`,
    `updated_at`
) VALUES 
(
    '测试文章标题', 
    '这是一篇测试文章的简介，用于验证新的表单字段是否正常工作。', 
    '<p>这是测试文章的内容，包含了<strong>富文本</strong>格式。</p><p>用于验证编辑器功能是否正常。</p>', 
    1, 
    '/uploads/test-cover.jpg', 
    '测试作者', 
    1, 
    10,
    NOW(),
    NOW()
);

-- 完成提示
SELECT '✅ 文章表结构更新完成！' as '更新状态';
SELECT '📋 新的表单字段已匹配数据库结构' as '说明';
SELECT '🎯 支持的字段：title, summary, content, category_id, cover_image, author_name, is_visible, sort' as '字段列表';
