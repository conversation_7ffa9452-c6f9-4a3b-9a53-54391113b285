<!-- 通用表单弹窗组件 -->
<template>
  <ElDialog
    v-model="dialogVisible"
    :title="title"
    :width="width"
    :close-on-click-modal="false"
    align-center
    @close="handleClose"
  >
    <ElForm
      ref="formRef"
      :model="internalFormData"
      :rules="computedRules"
      :label-width="labelWidth"
      :label-position="labelPosition"
    >
      <ElRow :gutter="gutter">
        <ElCol
          v-for="field in formConfig"
          :key="field.prop"
          :span="field.span || 24"
        >
          <ElFormItem :label="field.label" :prop="field.prop">
            <!-- 输入框 -->
            <ElInput
              v-if="field.type === 'input'"
              v-model="internalFormData[field.prop]"
              :placeholder="field.placeholder || `请输入${field.label}`"
              :clearable="field.config?.clearable !== false"
              v-bind="field.config"
            />

            <!-- 密码输入框 -->
            <ElInput
              v-else-if="field.type === 'password'"
              v-model="internalFormData[field.prop]"
              type="password"
              :placeholder="field.placeholder || `请输入${field.label}`"
              :clearable="field.config?.clearable !== false"
              show-password
              v-bind="field.config"
            />
            
            <!-- 文本域 -->
            <ElInput
              v-else-if="field.type === 'textarea'"
              v-model="internalFormData[field.prop]"
              type="textarea"
              :placeholder="field.placeholder || `请输入${field.label}`"
              :rows="field.config?.rows || 3"
              v-bind="field.config"
            />
            
            <!-- 数字输入框 -->
            <ElInputNumber
              v-else-if="field.type === 'number'"
              v-model="internalFormData[field.prop]"
              :placeholder="field.placeholder || `请输入${field.label}`"
              style="width: 100%"
              v-bind="field.config"
            />
            
            <!-- 选择器 -->
            <ElSelect
              v-else-if="field.type === 'select'"
              v-model="internalFormData[field.prop]"
              :placeholder="field.placeholder || `请选择${field.label}`"
              :clearable="field.config?.clearable !== false"
              :multiple="field.config?.multiple"
              style="width: 100%"
              v-bind="field.config"
            >
              <ElOption
                v-for="option in getFieldOptions(field)"
                :key="option.value"
                :label="option.label"
                :value="option.value"
                :disabled="option.disabled"
              />
            </ElSelect>
            
            <!-- 单选框组 -->
            <ElRadioGroup
              v-else-if="field.type === 'radio'"
              v-model="internalFormData[field.prop]"
              v-bind="field.config"
            >
              <ElRadio
                v-for="option in getFieldOptions(field)"
                :key="option.value"
                :value="option.value"
                :disabled="option.disabled"
              >
                {{ option.label }}
              </ElRadio>
            </ElRadioGroup>
            
            <!-- 复选框组 -->
            <ElCheckboxGroup
              v-else-if="field.type === 'checkbox'"
              v-model="internalFormData[field.prop]"
              v-bind="field.config"
            >
              <ElCheckbox
                v-for="option in getFieldOptions(field)"
                :key="option.value"
                :label="option.value"
                :disabled="option.disabled"
              >
                {{ option.label }}
              </ElCheckbox>
            </ElCheckboxGroup>
            
            <!-- 开关 -->
            <ElSwitch
              v-else-if="field.type === 'switch'"
              v-model="internalFormData[field.prop]"
              v-bind="field.config"
            />
            
            <!-- 日期选择器 -->
            <ElDatePicker
              v-else-if="field.type === 'date'"
              v-model="internalFormData[field.prop]"
              :type="field.config?.type || 'date'"
              :placeholder="field.placeholder || `请选择${field.label}`"
              style="width: 100%"
              v-bind="field.config"
            />
            
            <!-- 日期范围选择器 -->
            <ElDatePicker
              v-else-if="field.type === 'daterange'"
              v-model="internalFormData[field.prop]"
              type="daterange"
              :start-placeholder="field.config?.startPlaceholder || '开始日期'"
              :end-placeholder="field.config?.endPlaceholder || '结束日期'"
              style="width: 100%"
              v-bind="field.config"
            />

            <!-- 富文本编辑器 -->
            <RichTextEditor
              v-else-if="field.type === 'editor'"
              v-model="internalFormData[field.prop]"
              :height="field.config?.height || '400px'"
              :placeholder="field.placeholder || `请输入${field.label}`"
              :mode="field.config?.mode || 'article'"
              v-bind="field.config"
            />

            <!-- 媒体文件选择器 -->
            <div v-else-if="field.type === 'upload'" class="media-upload-field">
              <!-- 已选择的图片预览 -->
              <div v-if="internalFormData[field.prop]" class="selected-media-box">
                <img
                  :src="getImageUrl(internalFormData[field.prop])"
                  :alt="field.label"
                  class="media-preview-image"
                />
                <div class="media-overlay">
                  <ElButton size="small" @click="openMediaPicker(field.prop)">重新选择</ElButton>
                  <ElButton size="small" type="danger" @click="removeMedia(field.prop)">移除</ElButton>
                </div>
              </div>
              <!-- 上传区域 -->
              <div v-else class="upload-area" @click="openMediaPicker(field.prop)">
                <div class="upload-content">
                  <ElIcon class="upload-icon"><Plus /></ElIcon>
                  <div class="upload-text">{{ field.config?.uploadText || '选择图片' }}</div>
                </div>
              </div>
              <div class="upload-tip" v-if="field.placeholder">
                {{ field.placeholder }}
              </div>
            </div>

            <!-- 自定义插槽 -->
            <slot
              v-else
              :name="`field-${field.prop}`"
              :field="field"
              :value="internalFormData[field.prop]"
              :update-value="(val: any) => internalFormData[field.prop] = val"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleClose">取消</ElButton>
        <ElButton 
          type="primary" 
          :loading="loading"
          @click="handleSubmit"
        >
          确定
        </ElButton>
      </div>
    </template>
  </ElDialog>

  <!-- 媒体文件选择器 -->
  <MediaPicker
    v-model="showMediaPicker"
    :title="mediaPickerTitle"
    :multiple="false"
    accept="image/*"
    @confirm="handleMediaConfirm"
  />
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElRow,
  ElCol,
  ElInput,
  ElInputNumber,
  ElSelect,
  ElOption,
  ElRadioGroup,
  ElRadio,
  ElCheckboxGroup,
  ElCheckbox,
  ElSwitch,
  ElDatePicker,
  ElButton,
  ElUpload,
  ElMessage,
  ElIcon
} from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import RichTextEditor from '@/components/common/rich-text-editor/index.vue'
import MediaPicker from '@/components/MediaPicker/index.vue'
import { resolveFileUrl } from '@/utils/url'
import type { FormFieldConfig } from '../crud-list-page/types'
import type { MediaFile } from '@/api/mediaApi'

interface Props {
  visible: boolean
  type: 'create' | 'edit'
  title: string
  formConfig: FormFieldConfig[]
  formData: Record<string, any>
  loading?: boolean
  width?: string
  labelWidth?: string
  labelPosition?: 'left' | 'right' | 'top'
  gutter?: number
  rules?: FormRules
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'submit', data: Record<string, any>): void
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  width: '600px',
  labelWidth: '100px',
  labelPosition: 'right',
  gutter: 20,
  loading: false
})

const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInstance>()

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 内部表单数据
const internalFormData = ref<Record<string, any>>({})

// 媒体选择器相关
const showMediaPicker = ref(false)
const mediaPickerTitle = ref('选择图片')
const currentMediaField = ref('')

// 计算表单验证规则
const computedRules = computed(() => {
  const rules: FormRules = { ...props.rules }
  
  // 根据字段配置自动生成规则
  props.formConfig.forEach(field => {
    if (field.required && !rules[field.prop]) {
      rules[field.prop] = [
        { required: true, message: `请输入${field.label}`, trigger: 'blur' }
      ]
    }
    
    // 合并字段自定义规则
    if (field.rules) {
      const existingRules = rules[field.prop] || []
      const fieldRules = Array.isArray(field.rules) ? field.rules : [field.rules]
      rules[field.prop] = (existingRules as any[]).concat(fieldRules)
    }
  })
  
  return rules
})

// 获取字段选项
const getFieldOptions = (field: FormFieldConfig) => {
  if (typeof field.options === 'function') {
    return field.options()
  }
  return field.options || []
}

// 监听表单数据变化
watch(
  () => props.formData,
  (newData) => {
    internalFormData.value = { ...newData }
  },
  { immediate: true, deep: true }
)

// 监听弹窗显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      // 重置表单数据
      internalFormData.value = { ...props.formData }
      
      // 清除验证
      nextTick(() => {
        formRef.value?.clearValidate()
      })
    }
  }
)

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  emit('close')
}

// 上传成功处理
const handleUploadSuccess = (response: any, prop: string) => {
  if (response && response.code === 200) {
    internalFormData.value[prop] = response.data.url || response.data.path || response.data
    ElMessage.success('上传成功')
  } else {
    ElMessage.error(response?.msg || '上传失败')
  }
}

// 上传失败处理
const handleUploadError = (error: any) => {
  console.error('上传失败:', error)
  ElMessage.error('上传失败，请重试')
}

// 打开媒体选择器
const openMediaPicker = (fieldProp: string) => {
  currentMediaField.value = fieldProp
  const field = props.formConfig.find(f => f.prop === fieldProp)
  mediaPickerTitle.value = field?.config?.pickerTitle || `选择${field?.label || '图片'}`
  showMediaPicker.value = true
}

// 媒体选择确认
const handleMediaConfirm = (files: MediaFile[]) => {
  if (files.length > 0 && currentMediaField.value) {
    // 保存文件路径，与文章编辑保持一致
    internalFormData.value[currentMediaField.value] = files[0].file_path
  }
  showMediaPicker.value = false
  currentMediaField.value = ''
}

// 移除媒体文件
const removeMedia = (fieldProp: string) => {
  internalFormData.value[fieldProp] = ''
}

// 获取图片URL
const getImageUrl = (path: string) => {
  return resolveFileUrl(path)
}

// 密码确认验证
const validateConfirmPassword = (rule: any, value: string, callback: Function) => {
  if (value && value !== internalFormData.value.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 支付密码确认验证
const validateConfirmPayPassword = (rule: any, value: string, callback: Function) => {
  if (value && value !== internalFormData.value.payPassword) {
    callback(new Error('两次输入的支付密码不一致'))
  } else {
    callback()
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 移除确认密码字段，不提交到后端
    const submitData = { ...internalFormData.value }
    delete submitData.confirmPassword
    delete submitData.confirmPayPassword

    emit('submit', submitData)
  } catch (error) {
    console.warn('表单验证失败:', error)
  }
}

// 暴露方法
defineExpose({
  validate: () => formRef.value?.validate(),
  resetFields: () => formRef.value?.resetFields(),
  clearValidate: () => formRef.value?.clearValidate(),
  getFormData: () => internalFormData.value
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.media-upload-field {
  width: 100%;
}

/* 上传区域样式 */
.upload-area {
  width: 80px;
  height: 80px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: border-color 0.3s;
  background-color: #fafafa;
}

.upload-area:hover {
  border-color: #409eff;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.upload-icon {
  font-size: 20px;
  color: #8c939d;
}

.upload-text {
  font-size: 12px;
  color: #8c939d;
}

/* 已选择图片样式 */
.selected-media-box {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #dcdfe6;
}

.media-preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.media-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.selected-media-box:hover .media-overlay {
  opacity: 1;
}

.upload-tip {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}
</style>
