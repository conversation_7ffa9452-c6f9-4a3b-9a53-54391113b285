var e=Object.defineProperty,t=Object.defineProperties,s=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,a=Object.prototype.propertyIsEnumerable,n=(t,s,r)=>s in t?e(t,s,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[s]=r;import{k as l,P as c,D as i,R as p,X as u,U as d,S as f}from"./vendor-84Inc-Pt.js";import{_ as b}from"./_plugin-vue_export-helper-BCo6x5W8.js";const v=["innerHTML"],m={class:"title"},y={class:"msg"},g={class:"res"},O={class:"btn-group"},j=l((P=((e,t)=>{for(var s in t||(t={}))o.call(t,s)&&n(e,s,t[s]);if(r)for(var s of r(t))a.call(t,s)&&n(e,s,t[s]);return e})({},{name:"ArtResultPage"}),t(P,s({__name:"ArtResultPage",props:{type:{default:"success"},title:{default:""},message:{default:""},iconCode:{default:""}},setup:e=>(e,t)=>(i(),c("div",{class:f(["page-content",e.type])},[p("i",{class:"iconfont-sys icon",innerHTML:e.iconCode},null,8,v),p("h1",m,u(e.title),1),p("p",y,u(e.message),1),p("div",g,[d(e.$slots,"content",{},void 0,!0)]),p("div",O,[d(e.$slots,"buttons",{},void 0,!0)])],2))}))));var P;const _=b(j,[["__scopeId","data-v-454d3696"]]);export{_};
