<?php
declare(strict_types=1);

namespace app\common\service;

use app\common\model\Admin;
use app\common\model\User;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use think\facade\Config;

/**
 * 认证服务类
 */
class AuthService
{
    /**
     * 管理员登录
     * @param string $username 用户名
     * @param string $password 密码
     * @return array
     * @throws \Exception
     */
    public static function adminLogin(string $username, string $password): array
    {
        $admin = Admin::where('username', $username)->find();
        
        if (!$admin) {
            throw new \Exception('用户名或密码错误');
        }

        if (!password_verify($password, $admin->password)) {
            throw new \Exception('用户名或密码错误');
        }

        if ($admin->status != 1) {
            throw new \Exception('账户已被禁用');
        }

        // 更新登录信息
        $admin->last_login_time = date('Y-m-d H:i:s');
        $admin->last_login_ip = request()->ip();
        $admin->save();

        // 生成JWT token
        $payload = [
            'id' => $admin->id,
            'username' => $admin->username,
            'type' => 'admin',
            'iat' => time(),
            'exp' => time() + (int)env('JWT_EXPIRE', 7200) // 默认2小时过期
        ];

        $token = JWT::encode($payload, env('JWT_KEY'), 'HS256');
        $refreshToken = JWT::encode([
            'id' => $admin->id,
            'type' => 'admin_refresh',
            'iat' => time(),
            'exp' => time() + (int)env('JWT_REFRESH_EXPIRE', 604800) // 默认7天过期
        ], env('JWT_KEY'), 'HS256');

        return [
            'token' => $token,
            'refreshToken' => $refreshToken
        ];
    }

    /**
     * 验证JWT Token
     * @param string $token
     * @return array
     * @throws \Exception
     */
    public static function verifyToken(string $token): array
    {
        try {
            $decoded = JWT::decode($token, new Key(env('JWT_KEY'), 'HS256'));
            $payload = (array)$decoded;

            // 验证Token是否过期
            if (isset($payload['exp']) && $payload['exp'] < time()) {
                throw new \Exception('登录已过期，请重新登录');
            }

            // 获取完整的用户信息
            if ($payload['type'] === 'admin') {
                $admin = Admin::with(['role'])->find($payload['id']);
                if (!$admin || $admin->status != 1) {
                    throw new \Exception('登录已过期，请重新登录');
                }

                // 补充用户信息
                $payload['nickname'] = $admin->nickname;
                $payload['roles'] = $admin->getRoleCodes();
                $payload['permissions'] = $admin->getPermissions();
            }

            return $payload;
        } catch (\Exception $e) {
            // 如果已经是我们自定义的错误消息，直接抛出
            if (strpos($e->getMessage(), '登录已过期') !== false) {
                throw $e;
            }
            // 其他token验证错误统一处理
            throw new \Exception('登录已过期，请重新登录');
        }
    }

    /**
     * 刷新Token
     * @param string $refreshToken
     * @return array
     * @throws \Exception
     */
    public static function refreshToken(string $refreshToken): array
    {
        try {
            $decoded = JWT::decode($refreshToken, new Key(env('JWT_KEY'), 'HS256'));
            $payload = (array)$decoded;
            
            if ($payload['type'] !== 'admin_refresh') {
                throw new \Exception('无效的刷新Token');
            }

            // 生成新的访问Token
            $newPayload = [
                'id' => $payload['id'],
                'username' => '', // 需要从数据库获取
                'type' => 'admin',
                'iat' => time(),
                'exp' => time() + (int)env('JWT_EXPIRE', 7200)
            ];

            $newToken = JWT::encode($newPayload, env('JWT_KEY'), 'HS256');
            
            return [
                'token' => $newToken,
                'refreshToken' => $refreshToken // 刷新Token保持不变
            ];
        } catch (\Exception $e) {
            throw new \Exception('刷新Token失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取当前登录用户信息
     * @param int $userId
     * @param string $type
     * @return array
     * @throws \Exception
     */
    public static function getCurrentUser(int $userId, string $type = 'admin'): array
    {
        if ($type === 'admin') {
            $admin = Admin::with(['role'])->find($userId);
            if (!$admin) {
                throw new \Exception('管理员不存在');
            }

            return [
                'userId' => $admin->id,
                'userName' => $admin->username,  // 前端期望的字段名
                'username' => $admin->username,  // 保持兼容性
                'nickname' => $admin->nickname,
                'avatar' => $admin->avatar ?? '',
                'email' => $admin->email ?? '',
                'phone' => $admin->phone ?? '',
                'roles' => $admin->getRoleCodes(),
                'permissions' => $admin->getPermissions(),
                'lastLoginTime' => $admin->last_login_time,
                'lastLoginIp' => $admin->last_login_ip
            ];
        } else {
            $user = User::find($userId);
            if (!$user) {
                throw new \Exception('用户不存在');
            }

            return [
                'userId' => $user->id,
                'userName' => $user->username,  // 前端期望的字段名
                'username' => $user->username,  // 保持兼容性
                'nickname' => $user->nickname,
                'avatar' => $user->avatar ?? '',
                'email' => $user->email ?? '',
                'phone' => $user->phone ?? ''
            ];
        }
    }

    /**
     * 检查用户权限
     * @param array $userPermissions
     * @param string $permission
     * @return bool
     */
    public static function hasPermission(array $userPermissions, string $permission): bool
    {
        // 超级管理员拥有所有权限
        if (in_array('*', $userPermissions)) {
            return true;
        }

        // 检查具体权限
        return in_array($permission, $userPermissions);
    }

    /**
     * 检查用户角色
     * @param array $userRoles
     * @param string $role
     * @return bool
     */
    public static function hasRole(array $userRoles, string $role): bool
    {
        return in_array($role, $userRoles);
    }

    /**
     * 用户登录
     * @param string $username 用户名
     * @param string $password 密码
     * @return array
     * @throws \Exception
     */
    public static function userLogin(string $username, string $password): array
    {
        $user = User::where('username', $username)->find();

        if (!$user) {
            throw new \Exception('用户名或密码错误');
        }

        if (!password_verify($password, $user->password)) {
            throw new \Exception('用户名或密码错误');
        }

        if ($user->status != 1) {
            throw new \Exception('账户已被禁用');
        }

        // 生成JWT token
        $payload = [
            'id' => $user->id,
            'username' => $user->username,
            'type' => 'user',
            'iat' => time(),
            'exp' => time() + (int)env('JWT_EXPIRE', 7200) // 默认2小时过期
        ];

        $token = JWT::encode($payload, env('JWT_KEY'), 'HS256');
        $refreshToken = JWT::encode([
            'id' => $user->id,
            'type' => 'user_refresh',
            'iat' => time(),
            'exp' => time() + (int)env('JWT_REFRESH_EXPIRE', 604800) // 默认7天过期
        ], env('JWT_KEY'), 'HS256');

        return [
            'token' => $token,
            'refreshToken' => $refreshToken
        ];
    }


}
