var e=Object.defineProperty,l=Object.defineProperties,a=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,n=(l,a,t)=>a in l?e(l,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[a]=t,i=(e,l)=>{for(var a in l||(l={}))o.call(l,a)&&n(e,a,l[a]);if(t)for(var a of t(l))r.call(l,a)&&n(e,a,l[a]);return e},s=(e,t)=>l(e,a(t)),u=(e,l,a)=>n(e,"symbol"!=typeof l?l+"":l,a),c=(e,l,a)=>new Promise(((t,o)=>{var r=e=>{try{i(a.next(e))}catch(l){o(l)}},n=e=>{try{i(a.throw(e))}catch(l){o(l)}},i=e=>e.done?t(e.value):Promise.resolve(e.value).then(r,n);i((a=a.apply(e,l)).next())}));import{a as d}from"./index-Wowen7jt.js";import{u as p,_ as f,A as v}from"./index-DdiMqMK0.js";import{r as m,M as h,d as g,o as y,t as b,e as _,n as C,m as V,w,P as k,D as A,x as z,F as R,u as x,ak as E,G as U,a8 as L,aL as P,$ as T,R as S,a6 as j,W as $,C as N,aK as O,a2 as D,U as K,a3 as I,aX as F,aq as B,ar as M,as as W,aN as q,aW as G,X as J,b0 as X,a4 as H,ap as Q,b1 as Z,V as Y,aa as ee,b2 as le,aT as ae,i as te,a5 as oe,p as re,aB as ne,aI as ie,E as se,Z as ue}from"./vendor-8T3zXQLl.js";import{_ as ce}from"./index-Zs-Thxwm.js";import{_ as de}from"./index-B5FHwYQA.js";import{_ as pe}from"./_plugin-vue_export-helper-BCo6x5W8.js";import{R as fe}from"./index-DS_-tevW.js";import{M as ve}from"./index-MG-hoaSG.js";import{A as me}from"./index-1JOyfOxu.js";var he=(e=>(e.CLEAR_ALL="clear_all",e.CLEAR_CURRENT="clear_current",e.CLEAR_PAGINATION="clear_pagination",e.KEEP_ALL="keep_all",e))(he||{});class ge{constructor(e=3e5,l=50,a=!1){u(this,"cache",new Map),u(this,"cacheTime"),u(this,"maxSize"),u(this,"enableLog"),this.cacheTime=e,this.maxSize=l,this.enableLog=a}log(e,...l){this.enableLog}generateKey(e){if(!e||"object"!=typeof e)return JSON.stringify(e);const l=this.sortObjectKeys(e);return JSON.stringify(l)}sortObjectKeys(e){const l={},a=Object.keys(e).sort();for(const t of a){const a=e[t];a&&"object"==typeof a&&!Array.isArray(a)?l[t]=this.sortObjectKeys(a):l[t]=a}return l}generateTags(e){const l=new Set,a=Object.keys(e).filter((l=>!["current","size","total"].includes(l)&&void 0!==e[l]&&""!==e[l]&&null!==e[l]));if(a.length>0){const t=a.map((l=>`${l}:${String(e[l])}`)).join("|");l.add(`search:${t}`)}else l.add("search:default");return l.add(`pagination:${e.size||10}`),l.add("pagination"),l}evictLRU(){if(this.cache.size<=this.maxSize)return;let e="",l=1/0,a=1/0;for(const[t,o]of this.cache.entries())(o.accessCount<l||o.accessCount===l&&o.lastAccessTime<a)&&(e=t,l=o.accessCount,a=o.lastAccessTime);e&&(this.cache.delete(e),this.log(`LRU 清理缓存: ${e}`))}set(e,l,a){const t=this.generateKey(e),o=this.generateTags(e),r=Date.now();this.evictLRU(),this.cache.set(t,{data:l,response:a,timestamp:r,params:t,tags:o,accessCount:1,lastAccessTime:r})}get(e){const l=this.generateKey(e),a=this.cache.get(l);return a?Date.now()-a.timestamp>this.cacheTime?(this.cache.delete(l),null):(a.accessCount++,a.lastAccessTime=Date.now(),a):null}clearByTags(e){let l=0;for(const[a,t]of this.cache.entries()){e.some((e=>Array.from(t.tags).some((l=>l.includes(e)))))&&(this.cache.delete(a),l++)}return l}clearCurrentSearch(e){const l=this.generateKey(e);return this.cache.delete(l)?1:0}clearPagination(){return this.clearByTags(["pagination"])}clear(){this.cache.clear()}getStats(){const e=this.cache.size;let l=0,a=0;for(const t of this.cache.values())l+=JSON.stringify(t.data).length,a+=t.accessCount;return{total:e,size:`${(l/1024).toFixed(2)}KB`,hitRate:`${e>0?(a/e).toFixed(1):"0"} avg hits`}}cleanupExpired(){let e=0;const l=Date.now();for(const[a,t]of this.cache.entries())l-t.timestamp>this.cacheTime&&(this.cache.delete(a),e++);return e}}const ye=e=>{if(!e)return{records:[],total:0,current:1,size:10};if(Array.isArray(e))return{records:e,total:e.length,current:1,size:e.length};if("object"==typeof e){const l=e;if("records"in l||"data"in l)return{records:l.records||l.data||[],total:l.total||l.count||0,current:l.current||l.page||l.pageNum||1,size:l.size||l.pageSize||l.limit||10};if("data"in l&&l.data&&"object"==typeof l.data){const e=l.data;if("list"in e||"records"in e||"items"in e)return{records:e.list||e.records||e.items||[],total:e.total||e.count||0,current:e.current||e.page||e.pageNum||l.current||l.page||1,size:e.size||e.pageSize||e.limit||l.size||l.pageSize||10};if(Array.isArray(e))return{records:e,total:e.length,current:1,size:e.length}}if("list"in l||"items"in l){const e=l.list||l.items||[];return{records:e,total:l.total||l.count||e.length,current:l.current||l.page||l.pageNum||1,size:l.size||l.pageSize||l.limit||10}}const a=["data","list","items","records","result"];for(const e of a)if(e in l&&Array.isArray(l[e])){const a=l[e];return{records:a,total:l.total||l.count||a.length,current:l.current||l.page||1,size:l.size||l.pageSize||10}}}return{records:[],total:0,current:1,size:10}},be=(e,l)=>{var a,t,o,r,n,i;e.total=Number(null!=(t=null!=(a=l.total)?a:e.total)?t:0),e.current=Number(null!=(r=null!=(o=l.current)?o:e.current)?r:1),e.size=Number(null!=(i=null!=(n=l.size)?n:e.size)?i:10);const s=Math.max(1,Math.ceil(e.total/e.size));e.current>s&&(e.current=s)};function _e(e){const{core:{apiFn:l,apiParams:a={},immediate:t=!0,columnsFactory:o,paginationKey:r={current:"current",size:"size"}},transform:{dataTransformer:n,responseAdapter:u=ye}={},performance:{enableCache:f=!1,cacheTime:v=3e5,debounceTime:V=300,maxCacheSize:w=50}={},hooks:{onSuccess:k,onError:A,onCacheHit:z,resetFormCallback:R}={},debug:{enableLog:x=!1}={}}=e,E=(null==r?void 0:r.current)||"current",U=(null==r?void 0:r.size)||"size",L=m(0),P=(e,...l)=>{},T=f?new ge(v,w,x):null,S=m(!1),j=m(null),$=m([]);let N=null,O=null;const D=h(Object.assign({[E]:1,[U]:10},a||{})),K=h({current:D[E]||1,size:D[U]||10,total:0}),{width:I}=d(),F=g((()=>s(i({},K),{small:I.value<768}))),B=o?p(o):null,M=null==B?void 0:B.columns,W=null==B?void 0:B.columnChecks,q=g((()=>$.value.length>0)),G=g((()=>(L.value,T?T.getStats():{total:0,size:"0KB",hitRate:"0 avg hits"}))),J=(e=>{const l=(e,...l)=>{};return(a,t)=>{const o={code:"UNKNOWN_ERROR",message:"未知错误",details:a};return a instanceof Error?(o.message=a.message,o.code=a.name):"string"==typeof a&&(o.message=a),l(`${t}:`,a),e&&e(o),o}})(A,x),X=(e,l)=>{if(!T)return;let a=0;switch(e){case he.CLEAR_ALL:T.clear(),P(`清空所有缓存 - ${l||""}`);break;case he.CLEAR_CURRENT:a=T.clearCurrentSearch(D),P(`清空当前搜索缓存 ${a} 条 - ${l||""}`);break;case he.CLEAR_PAGINATION:a=T.clearPagination(),P(`清空分页缓存 ${a} 条 - ${l||""}`);break;case he.KEEP_ALL:default:P(`保持缓存不变 - ${l||""}`)}L.value++},H=(e,...a)=>c(this,[e,...a],(function*(e,a=f){N&&N.abort();const t=new AbortController;N=t,S.value=!0,j.value=null;try{const o=Object.assign({},D,{[E]:K.current,[U]:K.size},e||{});if(a&&T){const e=T.get(o);if(e)return $.value=e.data,be(K,e.response),D[E]!==K.current&&(D[E]=K.current),D[U]!==K.size&&(D[U]=K.size),S.value=!1,z&&z(e.data,e.response),P("缓存命中"),e.response}const r=yield l(o);if(t.signal.aborted)throw new Error("请求已取消");const i=u(r);let s=(e=>{const l=e.records||e.data||[];return Array.isArray(l)?l:[]})(i);return n&&(s=n(s)),$.value=s,be(K,i),D[E]!==K.current&&(D[E]=K.current),D[U]!==K.size&&(D[U]=K.size),a&&T&&(T.set(o,s,i),L.value++,P("数据已缓存")),k&&k(s,i),i}catch(o){if(o instanceof Error&&"请求已取消"===o.message)return{records:[],total:0,current:1,size:10};$.value=[];throw J(o,"获取表格数据失败")}finally{S.value=!1,N===t&&(N=null)}})),Q=e=>c(this,null,(function*(){try{return yield H(e)}catch(l){return Promise.resolve()}})),Z=e=>c(this,null,(function*(){K.current=1,D[E]=1,X(he.CLEAR_CURRENT,"搜索数据");try{return yield H(e,!1)}catch(l){return Promise.resolve()}})),Y=((e,l)=>{let a=null,t=null,o=null,r=null;const n=(...n)=>new Promise(((i,s)=>{a&&clearTimeout(a),t=n,o=i,r=s,a=setTimeout((()=>c(void 0,null,(function*(){try{const l=yield e(...n);i(l)}catch(j){s(j)}finally{a=null,t=null,o=null,r=null}}))),l)}));return n.cancel=()=>{a&&(clearTimeout(a),a=null,t=null,o=null,r=null)},n.flush=()=>c(void 0,null,(function*(){if(a&&t&&o&&r){clearTimeout(a),a=null;const l=t,n=o,i=r;t=null,o=null,r=null;try{const a=yield e(...l);return n(a),a}catch(j){throw i(j),j}}return Promise.resolve()})),n})(Z,V);let ee=!1;const le=()=>{N&&N.abort(),Y.cancel()};return f&&T&&(O=setInterval((()=>{const e=T.cleanupExpired();e>0&&(P(`自动清理 ${e} 条过期缓存`),L.value++)}),v/2)),t&&y((()=>c(this,null,(function*(){yield Q()})))),b((()=>{le(),T&&T.clear(),O&&clearInterval(O)})),i({tableData:$,isLoading:_(S),hasError:_(j),isEmpty:g((()=>0===$.value.length)),hasData:q,paginationState:_(K),paginationMobile:F,onPageSizeChange:e=>c(this,null,(function*(){e<=0||(Y.cancel(),K.size=e,K.current=1,D[U]=e,D[E]=1,X(he.CLEAR_CURRENT,"分页大小变化"),yield Q())})),onCurrentPageChange:e=>c(this,null,(function*(){if(!(e<=0||ee))if(K.current!==e)try{ee=!0,K.current=e,D[E]!==e&&(D[E]=e),yield Q()}finally{ee=!1}else P("分页页码未变化，跳过请求")})),searchState:D,resetSearch:()=>c(this,null,(function*(){Y.cancel();const e={[E]:1,[U]:D[U]||10};Object.keys(D).forEach((e=>{delete D[e]})),Object.assign(D,a||{},e),K.current=1,K.size=e[U],j.value=null,X(he.CLEAR_ALL,"重置搜索"),yield Q(),R&&(yield C(),R())})),loadData:Q,searchData:Z,searchDataDebounced:Y,refreshAll:()=>c(this,null,(function*(){Y.cancel(),X(he.CLEAR_ALL,"手动刷新"),yield Q()})),refreshSoft:()=>c(this,null,(function*(){X(he.CLEAR_CURRENT,"软刷新"),yield Q()})),refreshAfterCreate:()=>c(this,null,(function*(){Y.cancel(),K.current=1,D[E]=1,X(he.CLEAR_PAGINATION,"新增数据"),yield Q()})),refreshAfterUpdate:()=>c(this,null,(function*(){X(he.CLEAR_CURRENT,"编辑数据"),yield Q()})),refreshAfterRemove:()=>c(this,null,(function*(){1===$.value.length&&K.current>1&&(K.current=K.current-1,D[E]=K.current),X(he.CLEAR_CURRENT,"删除数据"),yield Q()})),cacheStatistics:G,invalidateCache:X,clearExpiredCache:()=>{if(!T)return 0;const e=T.cleanupExpired();return e>0&&L.value++,e},abortRequest:le,clearAllData:()=>{$.value=[],j.value=null,X(he.CLEAR_ALL,"清空数据")}},B&&{columns:M,columnChecks:W})}const Ce={class:"common-search-bar"},Ve=pe(V({__name:"index",props:{filter:{},items:{},config:{},defaultValues:{}},emits:["update:filter","search","reset"],setup(e,{expose:l,emit:a}){const t=e,o=a,r=m(i({},t.filter)),n=g((()=>t.items.map((e=>s(i({},e),{onChange:l=>{e.onChange&&e.onChange(l),o("update:filter",r.value)}})))));w((()=>t.filter),(e=>{r.value=i({},e)}),{deep:!0}),w(r,(e=>{o("update:filter",e)}),{deep:!0});const u=()=>{const e=t.defaultValues||{};r.value=i({},e),o("update:filter",r.value),o("reset")},c=()=>{o("search",r.value)};return l({reset:u,search:c,getFilter:()=>r.value}),(e,l)=>{var a,t,o;return A(),k("div",Ce,[z(de,{filter:r.value,"onUpdate:filter":l[0]||(l[0]=e=>r.value=e),items:n.value,"label-width":(null==(a=e.config)?void 0:a.labelWidth)||"80px",gutter:(null==(t=e.config)?void 0:t.gutter)||20,"el-col-span":(null==(o=e.config)?void 0:o.span)||6,onReset:u,onSearch:c},null,8,["filter","items","label-width","gutter","el-col-span"])])}}}),[["__scopeId","data-v-bcea292f"]]),we={key:11,class:"media-upload-field"},ke={key:0,class:"selected-media-box"},Ae=["src","alt"],ze={class:"media-overlay"},Re=["onClick"],xe={class:"upload-content"},Ee={class:"upload-text"},Ue={key:2,class:"upload-tip"},Le={class:"dialog-footer"},Pe=pe(V({__name:"index",props:{visible:{type:Boolean},type:{},title:{},formConfig:{},formData:{},loading:{type:Boolean,default:!1},width:{default:"600px"},labelWidth:{default:"100px"},labelPosition:{default:"right"},gutter:{default:20},rules:{}},emits:["update:visible","submit","close"],setup(e,{expose:l,emit:a}){const t=e,o=a,r=m(),n=g({get:()=>t.visible,set:e=>o("update:visible",e)}),s=m({}),u=m(!1),d=m("选择图片"),p=m(""),f=g((()=>{const e=i({},t.rules);return t.formConfig.forEach((l=>{if(l.required&&!e[l.prop]&&(e[l.prop]=[{required:!0,message:`请输入${l.label}`,trigger:"blur"}]),l.rules){const a=e[l.prop]||[],t=Array.isArray(l.rules)?l.rules:[l.rules];e[l.prop]=a.concat(t)}})),e})),v=e=>"function"==typeof e.options?e.options():e.options||[];w((()=>t.formData),(e=>{s.value=i({},e)}),{immediate:!0,deep:!0}),w((()=>t.visible),(e=>{e&&(s.value=i({},t.formData),C((()=>{var e;null==(e=r.value)||e.clearValidate()})))}));const h=()=>{n.value=!1,o("close")},y=e=>{var l;p.value=e;const a=t.formConfig.find((l=>l.prop===e));d.value=(null==(l=null==a?void 0:a.config)?void 0:l.pickerTitle)||`选择${(null==a?void 0:a.label)||"图片"}`,u.value=!0},b=e=>{e.length>0&&p.value&&(s.value[p.value]=e[0].file_path),u.value=!1,p.value=""},_=()=>c(this,null,(function*(){if(r.value)try{yield r.value.validate();const e=i({},s.value);delete e.confirmPassword,delete e.confirmPayPassword,o("submit",e)}catch(e){}}));return l({validate:()=>{var e;return null==(e=r.value)?void 0:e.validate()},resetFields:()=>{var e;return null==(e=r.value)?void 0:e.resetFields()},clearValidate:()=>{var e;return null==(e=r.value)?void 0:e.clearValidate()},getFormData:()=>s.value}),(e,l)=>(A(),k(R,null,[z(x(E),{modelValue:n.value,"onUpdate:modelValue":l[0]||(l[0]=e=>n.value=e),title:e.title,width:e.width,"close-on-click-modal":!1,"align-center":"",onClose:h},{footer:U((()=>[S("div",Le,[z(x(j),{onClick:h},{default:U((()=>l[4]||(l[4]=[$("取消")]))),_:1,__:[4]}),z(x(j),{type:"primary",loading:e.loading,onClick:_},{default:U((()=>l[5]||(l[5]=[$(" 确定 ")]))),_:1,__:[5]},8,["loading"])])])),default:U((()=>[z(x(L),{ref_key:"formRef",ref:r,model:s.value,rules:f.value,"label-width":e.labelWidth,"label-position":e.labelPosition},{default:U((()=>[z(x(P),{gutter:e.gutter},{default:U((()=>[(A(!0),k(R,null,T(e.formConfig,(a=>(A(),N(x(O),{key:a.prop,span:a.span||24},{default:U((()=>[z(x(D),{label:a.label,prop:a.prop},{default:U((()=>{var t,o,r,n,i,u,c,d,p,f,m,h;return["input"===a.type?(A(),N(x(I),F({key:0,modelValue:s.value[a.prop],"onUpdate:modelValue":e=>s.value[a.prop]=e,placeholder:a.placeholder||`请输入${a.label}`,clearable:!1!==(null==(t=a.config)?void 0:t.clearable)},{ref_for:!0},a.config),null,16,["modelValue","onUpdate:modelValue","placeholder","clearable"])):"password"===a.type?(A(),N(x(I),F({key:1,modelValue:s.value[a.prop],"onUpdate:modelValue":e=>s.value[a.prop]=e,type:"password",placeholder:a.placeholder||`请输入${a.label}`,clearable:!1!==(null==(o=a.config)?void 0:o.clearable),"show-password":""},{ref_for:!0},a.config),null,16,["modelValue","onUpdate:modelValue","placeholder","clearable"])):"textarea"===a.type?(A(),N(x(I),F({key:2,modelValue:s.value[a.prop],"onUpdate:modelValue":e=>s.value[a.prop]=e,type:"textarea",placeholder:a.placeholder||`请输入${a.label}`,rows:(null==(r=a.config)?void 0:r.rows)||3},{ref_for:!0},a.config),null,16,["modelValue","onUpdate:modelValue","placeholder","rows"])):"number"===a.type?(A(),N(x(B),F({key:3,modelValue:s.value[a.prop],"onUpdate:modelValue":e=>s.value[a.prop]=e,placeholder:a.placeholder||`请输入${a.label}`,style:{width:"100%"}},{ref_for:!0},a.config),null,16,["modelValue","onUpdate:modelValue","placeholder"])):"select"===a.type?(A(),N(x(M),F({key:4,modelValue:s.value[a.prop],"onUpdate:modelValue":e=>s.value[a.prop]=e,placeholder:a.placeholder||`请选择${a.label}`,clearable:!1!==(null==(n=a.config)?void 0:n.clearable),multiple:null==(i=a.config)?void 0:i.multiple,style:{width:"100%"}},{ref_for:!0},a.config),{default:U((()=>[(A(!0),k(R,null,T(v(a),(e=>(A(),N(x(W),{key:e.value,label:e.label,value:e.value,disabled:e.disabled},null,8,["label","value","disabled"])))),128))])),_:2},1040,["modelValue","onUpdate:modelValue","placeholder","clearable","multiple"])):"radio"===a.type?(A(),N(x(q),F({key:5,modelValue:s.value[a.prop],"onUpdate:modelValue":e=>s.value[a.prop]=e},{ref_for:!0},a.config),{default:U((()=>[(A(!0),k(R,null,T(v(a),(e=>(A(),N(x(G),{key:e.value,value:e.value,disabled:e.disabled},{default:U((()=>[$(J(e.label),1)])),_:2},1032,["value","disabled"])))),128))])),_:2},1040,["modelValue","onUpdate:modelValue"])):"checkbox"===a.type?(A(),N(x(X),F({key:6,modelValue:s.value[a.prop],"onUpdate:modelValue":e=>s.value[a.prop]=e},{ref_for:!0},a.config),{default:U((()=>[(A(!0),k(R,null,T(v(a),(e=>(A(),N(x(H),{key:e.value,label:e.value,disabled:e.disabled},{default:U((()=>[$(J(e.label),1)])),_:2},1032,["label","disabled"])))),128))])),_:2},1040,["modelValue","onUpdate:modelValue"])):"switch"===a.type?(A(),N(x(Q),F({key:7,modelValue:s.value[a.prop],"onUpdate:modelValue":e=>s.value[a.prop]=e},{ref_for:!0},a.config),null,16,["modelValue","onUpdate:modelValue"])):"date"===a.type?(A(),N(x(Z),F({key:8,modelValue:s.value[a.prop],"onUpdate:modelValue":e=>s.value[a.prop]=e,type:(null==(u=a.config)?void 0:u.type)||"date",placeholder:a.placeholder||`请选择${a.label}`,style:{width:"100%"}},{ref_for:!0},a.config),null,16,["modelValue","onUpdate:modelValue","type","placeholder"])):"daterange"===a.type?(A(),N(x(Z),F({key:9,modelValue:s.value[a.prop],"onUpdate:modelValue":e=>s.value[a.prop]=e,type:"daterange","start-placeholder":(null==(c=a.config)?void 0:c.startPlaceholder)||"开始日期","end-placeholder":(null==(d=a.config)?void 0:d.endPlaceholder)||"结束日期",style:{width:"100%"}},{ref_for:!0},a.config),null,16,["modelValue","onUpdate:modelValue","start-placeholder","end-placeholder"])):"editor"===a.type?(A(),N(fe,F({key:10,modelValue:s.value[a.prop],"onUpdate:modelValue":e=>s.value[a.prop]=e,height:(null==(p=a.config)?void 0:p.height)||"400px",placeholder:a.placeholder||`请输入${a.label}`,mode:(null==(f=a.config)?void 0:f.mode)||"article"},{ref_for:!0},a.config),null,16,["modelValue","onUpdate:modelValue","height","placeholder","mode"])):"upload"===a.type?(A(),k("div",we,[s.value[a.prop]?(A(),k("div",ke,[S("img",{src:(h=s.value[a.prop],me(h)),alt:a.label,class:"media-preview-image"},null,8,Ae),S("div",ze,[z(x(j),{size:"small",onClick:e=>y(a.prop)},{default:U((()=>l[2]||(l[2]=[$("重新选择")]))),_:2,__:[2]},1032,["onClick"]),z(x(j),{size:"small",type:"danger",onClick:e=>{return l=a.prop,void(s.value[l]="");var l}},{default:U((()=>l[3]||(l[3]=[$("移除")]))),_:2,__:[3]},1032,["onClick"])])])):(A(),k("div",{key:1,class:"upload-area",onClick:e=>y(a.prop)},[S("div",xe,[z(x(ee),{class:"upload-icon"},{default:U((()=>[z(x(le))])),_:1}),S("div",Ee,J((null==(m=a.config)?void 0:m.uploadText)||"选择图片"),1)])],8,Re)),a.placeholder?(A(),k("div",Ue,J(a.placeholder),1)):Y("",!0)])):K(e.$slots,`field-${a.prop}`,{key:12,field:a,value:s.value[a.prop],updateValue:e=>s.value[a.prop]=e},void 0,!0)]})),_:2},1032,["label","prop"])])),_:2},1032,["span"])))),128))])),_:3},8,["gutter"])])),_:3},8,["model","rules","label-width","label-position"])])),_:3},8,["modelValue","title","width"]),z(ve,{modelValue:u.value,"onUpdate:modelValue":l[1]||(l[1]=e=>u.value=e),title:d.value,multiple:!1,accept:"image/*",onConfirm:b},null,8,["modelValue","title"])],64))}}),[["__scopeId","data-v-ad5ce557"]]),Te={class:"crud-list-page art-full-height"},Se=pe(V({__name:"index",props:{config:{}},emits:["create","update","delete","selection-change"],setup(e,{expose:l,emit:a}){var t,o;const r=e,n=a,s=m(!1),u=m("create"),d=m(!1),p=m({}),h=m([]),b=m((null==(t=r.config.search)?void 0:t.defaultParams)||{}),_=g((()=>{var e;const l=r.config.columns||[];if(null==(e=r.config.actions)?void 0:e.enabled){const e={prop:"operation",label:"操作",width:r.config.actions.width||120,fixed:"right",formatter:e=>{var l,a,t,o,n,i;return re("div",{class:"flex items-center gap-1",style:{whiteSpace:"nowrap"}},[(null==(a=null==(l=r.config.actions)?void 0:l.edit)?void 0:a.enabled)&&re(v,{type:"edit",onClick:()=>W(e)}),...(null==(o=null==(t=r.config.actions)?void 0:t.custom)?void 0:o.map((l=>["add","edit","delete","view","more"].includes(l.type)?re(ne,{content:l.text||"操作",placement:"top"},(()=>re(v,{type:l.type,onClick:()=>l.handler&&l.handler(e)}))):re(ne,{content:l.text||"操作",placement:"top"},(()=>re(j,{type:l.buttonType||"primary",size:"small",link:!0,onClick:()=>l.handler&&l.handler(e)},(()=>l.text)))))))||[],(null==(i=null==(n=r.config.actions)?void 0:n.delete)?void 0:i.enabled)&&re(v,{type:"delete",onClick:()=>q(e)})])}};return[...l,e]}return l})),{columnChecks:C,tableData:V,isLoading:w,paginationState:R,searchData:E,resetSearch:L,onPageSizeChange:P,onCurrentPageChange:T,refreshAll:S}=_e({core:{apiFn:r.config.api.list,apiParams:i({current:1,size:20},null==(o=r.config.search)?void 0:o.defaultParams),columnsFactory:()=>_.value,immediate:!1},hooks:{onError:e=>se.error(e.message)}}),O=g((()=>{var e;const l=(null==(e=r.config.dialog)?void 0:e.titles)||{};return"create"===u.value?l.create||"新增":l.edit||"编辑"})),D=()=>{E(b.value)},I=()=>{var e;b.value=i({},(null==(e=r.config.search)?void 0:e.defaultParams)||{}),L()},F=()=>{S()},B=e=>{h.value=e,n("selection-change",e)},M=()=>{var e;(null==(e=r.config.dialog)?void 0:e.enabled)?(u.value="create",p.value={},s.value=!0):n("create",{})},W=e=>{var l;(null==(l=r.config.dialog)?void 0:l.enabled)?(u.value="edit",p.value=i({},e),s.value=!0):n("update",e.id,{})},q=e=>c(this,null,(function*(){var l,a,t,o;try{yield ie.confirm((null==(a=null==(l=r.config.actions)?void 0:l.delete)?void 0:a.confirmText)||"确定要删除这条记录吗？","提示",{type:"warning"}),r.config.api.delete&&(yield r.config.api.delete(e.id),se.success("删除成功"),S()),n("delete",e.id)}catch(i){if("cancel"!==i){const e=(null==(o=null==(t=null==i?void 0:i.response)?void 0:t.data)?void 0:o.msg)||(null==i?void 0:i.message)||"删除失败";se.error(e)}}})),G=e=>c(this,null,(function*(){try{d.value=!0,"create"===u.value?(r.config.api.create&&(yield r.config.api.create(e)),n("create",e),se.success("创建成功")):(r.config.api.update&&(yield r.config.api.update(p.value.id,e)),n("update",p.value.id,e),se.success("更新成功")),s.value=!1,S()}catch(l){se.error("create"===u.value?"创建失败":"更新失败")}finally{d.value=!1}}));return y((()=>{!1!==r.config.autoLoad&&E()})),l({refresh:S,handleRefresh:F,getDataByPage:E,selectedRows:h}),(e,l)=>{var a,t;const o=ue("ripple");return A(),k("div",Te,[(null==(a=e.config.search)?void 0:a.enabled)?(A(),N(Ve,{key:0,filter:b.value,"onUpdate:filter":l[0]||(l[0]=e=>b.value=e),items:(null==(t=e.config.search)?void 0:t.items)||[],onReset:I,onSearch:D},null,8,["filter","items"])):Y("",!0),z(x(ae),{class:"art-table-card",shadow:"never"},{default:U((()=>{var a;return[z(f,{columns:x(C),"onUpdate:columns":l[1]||(l[1]=e=>te(C)?C.value=e:null),onRefresh:F},{left:U((()=>{var l,a;return[(null==(a=null==(l=e.config.actions)?void 0:l.create)?void 0:a.enabled)?oe((A(),N(x(j),{key:0,onClick:M,type:e.config.actions.create.type||"primary"},{default:U((()=>[$(J(e.config.actions.create.text||"新增"),1)])),_:1},8,["type"])),[[o]]):Y("",!0),K(e.$slots,"header-actions",{selectedRows:h.value},void 0,!0)]})),_:3},8,["columns"]),z(ce,{loading:x(w),data:x(V),columns:_.value,pagination:x(R),"table-config":e.config.table||{rowKey:"id"},layout:{marginTop:10},"onRow:selectionChange":B,"onPagination:sizeChange":x(P),"onPagination:currentChange":x(T)},null,8,["loading","data","columns","pagination","table-config","onPagination:sizeChange","onPagination:currentChange"]),(null==(a=e.config.dialog)?void 0:a.enabled)?(A(),N(Pe,{key:0,visible:s.value,"onUpdate:visible":l[2]||(l[2]=e=>s.value=e),type:u.value,title:O.value,"form-config":e.config.dialog.formConfig,"form-data":p.value,loading:d.value,onSubmit:G},null,8,["visible","type","title","form-config","form-data","loading"])):Y("",!0),K(e.$slots,"custom-dialog",{visible:s.value,type:u.value,data:p.value,close:()=>s.value=!1,submit:G},void 0,!0)]})),_:3})])}}}),[["__scopeId","data-v-52b451b4"]]);export{Se as C};
