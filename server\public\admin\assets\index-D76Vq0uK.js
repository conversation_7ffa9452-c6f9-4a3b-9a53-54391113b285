var e=Object.defineProperty,r=Object.defineProperties,A=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,a=(r,A,o)=>A in r?e(r,A,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[A]=o;import{c as s,A as y}from"./index-tBKMHRR1.js";import{m as c,d as g,P as n,D as b,R as B,Q as u}from"./vendor-8T3zXQLl.js";import{_ as w}from"./_plugin-vue_export-helper-BCo6x5W8.js";const j={class:"art-logo"},U=["src"],f=c((O=((e,r)=>{for(var A in r||(r={}))t.call(r,A)&&a(e,A,r[A]);if(o)for(var A of o(r))l.call(r,A)&&a(e,A,r[A]);return e})({},{name:"ArtLogo"}),r(O,A({__name:"index",props:{size:{default:36}},setup(e){const r=e,A=s(),o=g((()=>({width:`${r.size||A.getLogoSize()}px`}))),t=g((()=>{const e=A.getSiteLogo();return e?y(e):"data:image/webp;base64,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"}));return(e,r)=>(b(),n("div",j,[B("img",{style:u(o.value),src:t.value,alt:"logo"},null,12,U)]))}}))));var O;const k=w(f,[["__scopeId","data-v-e38521f8"]]);export{k as _};
