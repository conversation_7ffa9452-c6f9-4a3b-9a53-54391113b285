var e=Object.defineProperty,t=Object.defineProperties,r=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,a=Object.prototype.propertyIsEnumerable,p=(t,r,o)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[r]=o;import{_ as i}from"./ArtException-CXLsKjKz.js";import{k as s,C as l,u as c,D as m}from"./vendor-84Inc-Pt.js";import"./index-DG9-1w7X.js";/* empty css                  */import"./_plugin-vue_export-helper-BCo6x5W8.js";const b=s((u=((e,t)=>{for(var r in t||(t={}))n.call(t,r)&&p(e,r,t[r]);if(o)for(var r of o(t))a.call(t,r)&&p(e,r,t[r]);return e})({},{name:"Exception500"}),t(u,r({__name:"index",setup:e=>(e,t)=>{const r=i;return m(),l(r,{data:{title:"500",desc:e.$t("exceptionPage.500"),btnText:e.$t("exceptionPage.gohome"),imgUrl:c("/admin/assets/500-DtnECEn9.png")}},null,8,["data"])}}))));var u;export{b as default};
