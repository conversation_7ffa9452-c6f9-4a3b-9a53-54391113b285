<template>
  <div class="article-edit-page">
    <!-- 页面头部 -->
    <ElCard class="page-header" shadow="never">
      <div class="header-content">
        <div class="title-section">
          <h2>{{ isEdit ? '编辑文章' : '新增文章' }}</h2>
        </div>
        <div class="action-section">
          <ElButton @click="handleCancel">取消</ElButton>
          <ElButton type="primary" @click="handleSave" :loading="saving">保存</ElButton>
        </div>
      </div>
    </ElCard>

    <!-- 表单内容 -->
    <ElCard class="form-content" shadow="never">
      <div class="edit-layout">
        <!-- 左侧：基本信息表单 -->
        <div class="left-panel">
          <ElForm
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="80px"
            label-position="left"
          >
        <!-- 文章标题 -->
        <ElFormItem label="文章标题" prop="title">
          <ElInput
            v-model="formData.title"
            placeholder="请输入文章标题"
            maxlength="200"
            show-word-limit
          />
        </ElFormItem>

        <!-- 文章简介 -->
        <ElFormItem label="文章简介" prop="summary">
          <ElInput
            v-model="formData.summary"
            type="textarea"
            :rows="3"
            placeholder="请输入文章简介，建议150字以内"
            maxlength="150"
            show-word-limit
          />
        </ElFormItem>



        <!-- 文章分类 -->
        <ElFormItem label="文章分类" prop="category_id">
          <ElSelect
            v-model="formData.category_id"
            placeholder="请选择文章分类"
            style="width: 100%"
          >
            <ElOption
              v-for="category in categories"
              :key="category.value"
              :label="category.label"
              :value="category.value"
            />
          </ElSelect>
        </ElFormItem>

        <!-- 作者姓名 -->
        <ElFormItem label="作者姓名" prop="author_name">
          <ElInput
            v-model="formData.author_name"
            placeholder="请输入作者姓名"
          />
        </ElFormItem>

        <!-- 封面图片 -->
        <ElFormItem label="封面图片" prop="cover_image">
          <div class="cover-upload-area">
            <div
              v-if="!formData.cover_image"
              class="upload-placeholder"
              @click="showCoverPicker = true"
            >
              <div class="upload-icon">+</div>
            </div>
            <div v-else class="uploaded-image">
              <img :src="resolveFileUrl(formData.cover_image)" alt="封面图片" class="cover-preview" />
              <div class="image-overlay">
                <ElButton size="small" @click="showCoverPicker = true">更换</ElButton>
                <ElButton size="small" type="danger" @click="formData.cover_image = ''">删除</ElButton>
              </div>
            </div>
            <div class="upload-hint">建议尺寸：240*240像素</div>
            <MediaPicker
              v-model="showCoverPicker"
              :multiple="false"
              accept="image/*"
              title="选择封面图片"
              @confirm="handleCoverConfirm"
            />
          </div>
        </ElFormItem>

        <!-- 推荐选项 -->
        <ElFormItem label="推荐状态" prop="is_featured">
          <ElRadioGroup v-model="formData.is_featured">
            <ElRadio :value="0">不推荐</ElRadio>
            <ElRadio :value="1">推荐</ElRadio>
          </ElRadioGroup>
        </ElFormItem>

        <!-- 显示/隐藏选项 -->
        <ElFormItem label="显示状态" prop="is_visible">
          <ElRadioGroup v-model="formData.is_visible">
            <ElRadio :value="1">显示</ElRadio>
            <ElRadio :value="0">隐藏</ElRadio>
          </ElRadioGroup>
        </ElFormItem>

        <!-- 排序 -->
        <ElFormItem label="排序" prop="sort">
          <ElInputNumber
            v-model="formData.sort"
            :min="0"
            :max="9999"
            placeholder="数值越大越靠前"
            style="width: 100%"
          />
        </ElFormItem>








          </ElForm>
        </div>

        <!-- 右侧：富文本编辑器 -->
        <div class="right-panel">
          <div class="panel-title">文章内容</div>
          <ElForm
            :model="formData"
            label-width="0"
          >
            <ElFormItem prop="content">
              <div class="editor-container">
                <RichTextEditor
                  v-model="formData.content"
                  height="600px"
                  mode="article"
                  placeholder="请输入文章内容..."
                />
              </div>
            </ElFormItem>
          </ElForm>
        </div>
      </div>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import RichTextEditor from '@/components/common/rich-text-editor/index.vue'
import MediaPicker from '@/components/MediaPicker/index.vue'
import { ArticleService, type ArticleFormData } from '@/api/articleApi'
import type { MediaFile } from '@/api/mediaApi'
import { resolveFileUrl } from '@/utils/url'

defineOptions({ name: 'ArticleEdit' })

const router = useRouter()
const route = useRoute()
const formRef = ref<FormInstance>()
const saving = ref(false)
const loading = ref(false)

// 判断是编辑还是新增
const isEdit = computed(() => !!route.params.id)
const articleId = computed(() => route.params.id as string)

// 分类选项
const categories = ref([
  { label: '技术文章', value: 1 },
  { label: '产品介绍', value: 2 },
  { label: '公司动态', value: 3 }
])

// 封面图片选择器状态
const showCoverPicker = ref(false)

// 封面图片选择确认处理
const handleCoverConfirm = (files: MediaFile[]) => {
  if (files.length > 0) {
    // 保存文件路径，而不是完整URL
    formData.cover_image = files[0].file_path
    ElMessage.success('封面图片设置成功')
  }
}

// 表单数据
const formData = reactive<ArticleFormData>({
  title: '',
  summary: '',
  content: '',
  category_id: undefined,
  cover_image: '',
  author_name: '',
  is_featured: 0, // 默认不推荐
  is_visible: 1, // 默认显示
  sort: 0
})

// 表单验证规则
const formRules: FormRules = {
  title: [
    { required: true, message: '请输入文章标题', trigger: 'blur' },
    { max: 200, message: '标题不能超过200个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入文章内容', trigger: 'blur' }
  ]
}

// 加载文章数据（编辑模式）
const loadArticleData = async () => {
  if (!isEdit.value) return
  
  try {
    loading.value = true
    const article = await ArticleService.getArticleDetail(Number(articleId.value))
    
    // 填充表单数据
    Object.assign(formData, {
      title: article.title,
      summary: article.summary || '',
      content: article.content || '',
      category_id: article.category_id,
      cover_image: article.cover_image || '',
      author_name: article.author_name || '',
      is_featured: article.is_featured ?? 0, // 默认不推荐
      is_visible: article.is_visible ?? 1, // 默认显示
      sort: article.sort
    })
  } catch (error) {
    ElMessage.error('加载文章数据失败')
    router.push('/system/article/list')
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = async () => {
  try {
    await ElMessageBox.confirm('确定要取消吗？未保存的内容将丢失。', '提示', {
      type: 'warning'
    })
    router.push('/system/article/list')
  } catch {
    // 用户取消
  }
}

// 保存文章
const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    saving.value = true
    
    const data = { ...formData }
    
    if (isEdit.value) {
      await ArticleService.updateArticle(Number(articleId.value), data)
      ElMessage.success('文章更新成功')
    } else {
      await ArticleService.createArticle(data)
      ElMessage.success('文章保存成功')
    }
    
    router.push('/system/article/list')
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error(isEdit.value ? '更新失败' : '保存失败')
    }
  } finally {
    saving.value = false
  }
}



onMounted(() => {
  loadArticleData()
})
</script>

<style scoped lang="scss">
// 封面上传区域样式
.cover-upload-area {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  .upload-placeholder {
    width: 100px !important;
    height: 100px !important;
    border: 2px dashed #ddd !important;
    border-radius: 6px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    background-color: #fff !important;
    box-sizing: border-box !important;
    margin-bottom: 8px !important;

    &:hover {
      border-color: #409eff !important;
    }

    .upload-icon {
      font-size: 32px !important;
      color: #ccc !important;
      font-weight: 300 !important;
      line-height: 1 !important;
    }
  }

  .upload-hint {
    font-size: 12px !important;
    color: #999 !important;
    text-align: left !important;
    margin-top: 4px !important;
    line-height: 1.4;
  }

  .uploaded-image {
    position: relative;
    display: inline-block;

    .cover-preview {
      width: 100px;
      height: 100px;
      object-fit: cover;
      border-radius: 6px;
      border: 1px solid #ddd;
    }

    .image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      opacity: 0;
      transition: opacity 0.3s;
      border-radius: 8px;
    }

    &:hover .image-overlay {
      opacity: 1;
    }
  }
}

.article-edit-page {
  .page-header {
    margin-bottom: 16px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-section {
        h2 {
          margin: 0 0 8px 0;
          font-size: 20px;
          font-weight: 500;
        }
      }

      .action-section {
        display: flex;
        gap: 12px;
      }
    }
  }

  .form-content {
    :deep(.el-form-item__label) {
      font-weight: 500;
    }

    :deep(.el-input__count) {
      color: #909399;
    }
  }

  /* 左右分栏布局 */
  .edit-layout {
    display: flex;
    min-height: 600px;

    .left-panel {
      flex: 0 0 400px; /* 左侧固定宽度400px，增加宽度 */
      padding: 0 20px 0 24px; /* 左边距24px，右边距20px */
      border-right: 1px solid #ebeef5; /* 添加分割线 */

      :deep(.el-form-item) {
        margin-bottom: 20px;
        display: flex;
        align-items: flex-start;
      }

      :deep(.el-form-item__label) {
        font-weight: 500;
        color: #606266;
        font-size: 14px;
        padding-right: 12px !important;
        text-align: right !important;
        line-height: 32px; /* 与输入框高度对齐 */
        min-height: 32px;
      }

      // 隐藏必填字段的红色星号
      :deep(.el-form-item__label::before) {
        display: none !important;
      }

      :deep(.el-form-item__content) {
        flex: 1;
        line-height: 32px;
      }

      :deep(.el-input) {
        width: 100%;
      }

      :deep(.el-select) {
        width: 100%;
      }

      :deep(.el-input-number) {
        width: 100%;
      }

      :deep(.el-textarea) {
        width: 100%;
      }

      :deep(.el-input__count) {
        color: #909399;
      }

      :deep(.el-radio-group) {
        display: flex;
        gap: 16px;
      }
    }

    .right-panel {
      flex: 1; /* 右侧占剩余空间 */
      padding-left: 20px;

      .panel-title {
        font-weight: 500;
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
        margin-left: 100px; /* 与左侧label对齐 */
      }

      .editor-container {
        margin-left: 100px; /* 与左侧输入框对齐 */

        :deep(.rich-text-editor) {
          border: 1px solid #dcdfe6;
          border-radius: 4px;
        }

        :deep(.ql-toolbar) {
          border-bottom: 1px solid #dcdfe6;
          background-color: #fafafa;
        }

        :deep(.ql-container) {
          border: none;
        }

        :deep(.ql-editor) {
          min-height: 600px;
          font-size: 14px;
          line-height: 1.6;
          padding: 12px 15px;
        }

        :deep(.ql-editor.ql-blank::before) {
          color: #c0c4cc;
          font-style: normal;
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .article-edit-page .edit-layout {
    flex-direction: column;

    .left-panel {
      flex: none;
      width: 100%;
      padding-right: 0;
      border-right: none;
      border-bottom: 1px solid #ebeef5;
      padding-bottom: 20px;
      margin-bottom: 20px;
    }

    .right-panel {
      flex: none;
      width: 100%;
      padding-left: 0;

      .panel-title {
        margin-left: 80px;
      }

      .editor-container {
        margin-left: 80px;

        :deep(.ql-editor) {
          min-height: 500px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .article-edit-page .edit-layout .right-panel {
    .panel-title {
      margin-left: 0;
    }

    .editor-container {
      margin-left: 0;
    }
  }


}
</style>
