# 修复Token重复提示问题

## 🎯 问题现象
用户长时间不操作后，再次操作时出现多个重复的"登录已过期，请重新登录"提示框。

## 🔧 修复方案

### 核心思路
使用全局状态标志来防止重复处理401错误和重复显示提示消息。

### 修改的文件

#### 1. `admin/src/utils/http/index.ts`
- 添加全局状态管理：`isHandlingAuth` 和 `hasShownAuthMessage`
- 在 `handleUnauthorized` 函数中添加防重复逻辑
- 确保只显示一次错误提示，只执行一次登出操作

#### 2. `admin/src/utils/http/error.ts`
- 简化错误显示逻辑
- 移除复杂的防重复机制，由调用方控制

#### 3. 后端中间件
- 统一所有401错误的返回消息为"登录已过期，请重新登录"

## 🎯 修复效果

### 修复前
```
❌ 显示多个重复的错误提示
❌ 可能同时触发多次登出操作
❌ 用户体验差
```

### 修复后
```
✅ 只显示一次错误提示
✅ 只执行一次登出操作
✅ 用户体验良好
```

## 🧪 测试方法

1. 登录系统
2. 等待token过期（或手动清空token）
3. 同时触发多个操作（如点击多个菜单项）
4. 观察是否只显示一次提示

## 📝 技术细节

### 防重复机制
```typescript
// 全局状态标志
let isHandlingAuth = false
let hasShownAuthMessage = false

const handleUnauthorized = (): void => {
  // 防止重复处理
  if (isHandlingAuth) {
    return
  }
  
  isHandlingAuth = true
  
  // 只显示一次消息
  if (!hasShownAuthMessage) {
    hasShownAuthMessage = true
    // 显示错误提示
  }
  
  // 执行登出并重置状态
}
```

### 状态重置
- 登出后3秒重置状态标志
- 为下次登录做准备

## ✅ 验证结果
修复后，无论同时触发多少个API请求，都只会显示一次"登录已过期，请重新登录"的提示。
