import request from '@/utils/http'

export class UserService {
  // 登录
  static login(params: Api.Auth.LoginParams) {
    return request.post<Api.Auth.LoginResponse>({
      url: '/auth/login',
      params,
      showErrorMessage: false // 不显示错误消息，由登录页面自己处理
    })
  }

  // 获取用户信息
  static getUserInfo() {
    return request.get<Api.User.UserInfo>({
      url: '/auth/info'
    })
  }

  // 获取用户列表
  static getUserList(params: Api.Common.PaginatingSearchParams) {
    return request.get<Api.User.UserListData>({
      url: '/user/list',
      params
    })
  }

  // 创建用户
  static createUser(data: any) {
    // 验证必填字段
    if (!data.userPhone || !data.nickName || !data.password) {
      return Promise.reject(new Error('手机号、昵称和密码为必填字段'))
    }

    return request.post({
      url: '/user/create',
      data
    })
  }

  // 更新用户
  static updateUser(id: string | number, data: any) {
    if (!id || Number(id) <= 0) {
      return Promise.reject(new Error('无效的用户ID'))
    }

    return request.put({
      url: `/user/update/${id}`,
      data
    })
  }

  // 删除用户
  static deleteUser(id: string | number) {
    return request.del({
      url: `/user/delete/${id}`
    })
  }

  // 批量删除用户
  static batchDeleteUser(ids: (string | number)[]) {
    return request.post({
      url: '/user/batch-delete',
      data: { ids }
    })
  }
}
