<?php
declare(strict_types=1);

namespace app\common\service;

use app\common\model\Notice;
use think\exception\DbException;

/**
 * 公告服务类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class NoticeService
{
    /**
     * 获取公告列表
     *
     * @param array $params 查询参数
     * @return array
     * @throws DbException
     */
    public static function getList(array $params = []): array
    {
        $page = max(1, (int)($params['current'] ?? 1));
        $limit = max(1, min(100, (int)($params['size'] ?? 20))); // 限制每页最大100条

        $query = Notice::order('is_top desc, sort desc, id desc');

        // 搜索条件
        if (!empty($params['title'])) {
            $title = trim($params['title']);
            if ($title !== '') {
                $query->whereLike('title', '%' . $title . '%');
            }
        }

        if (isset($params['is_top']) && $params['is_top'] !== '') {
            $query->where('is_top', (int)$params['is_top']);
        }

        if (isset($params['status']) && $params['status'] !== '') {
            $query->where('status', (int)$params['status']);
        }

        $result = $query->paginate([
            'list_rows' => $limit,
            'page' => $page,
        ]);

        return [
            'list' => $result->items(),
            'total' => $result->total(),
            'current' => $page,
            'size' => $limit,
        ];
    }

    /**
     * 获取公告详情
     *
     * @param int $id 公告ID
     * @return Notice
     * @throws \Exception
     */
    public static function getDetail(int $id): Notice
    {
        if ($id <= 0) {
            throw new \Exception('无效的公告ID');
        }

        $notice = Notice::find($id);
        if (!$notice) {
            throw new \Exception('公告不存在');
        }

        return $notice;
    }

    /**
     * 创建公告
     *
     * @param array $data 公告数据
     * @return Notice
     * @throws \Exception
     */
    public static function create(array $data): Notice
    {
        if (empty($data['title'])) {
            throw new \Exception('公告标题不能为空');
        }

        if (empty($data['content'])) {
            throw new \Exception('公告内容不能为空');
        }

        // 设置默认值
        $data['type'] = $data['type'] ?? 1; // 默认系统通知
        $data['is_published'] = $data['is_published'] ?? 1; // 默认已发布
        $data['is_top'] = $data['is_top'] ?? 0; // 默认不置顶
        $data['status'] = $data['status'] ?? 1; // 默认启用
        $data['sort'] = $data['sort'] ?? 0; // 默认排序
        $data['view_count'] = 0; // 初始浏览量
        $data['published_at'] = date('Y-m-d H:i:s'); // 设置发布时间

        return Notice::create($data);
    }

    /**
     * 更新公告
     *
     * @param int $id 公告ID
     * @param array $data 更新数据
     * @return Notice
     * @throws \Exception
     */
    public static function update(int $id, array $data): Notice
    {
        if ($id <= 0) {
            throw new \Exception('无效的公告ID');
        }

        $notice = Notice::find($id);
        if (!$notice) {
            throw new \Exception('公告不存在');
        }

        // 验证必填字段
        if (isset($data['title']) && empty($data['title'])) {
            throw new \Exception('公告标题不能为空');
        }

        if (isset($data['content']) && empty($data['content'])) {
            throw new \Exception('公告内容不能为空');
        }

        // 如果从草稿改为发布，设置发布时间
        if (isset($data['is_published']) && $data['is_published'] == 1 && $notice->is_published == 0) {
            $data['published_at'] = date('Y-m-d H:i:s');
        }

        $notice->save($data);
        return $notice;
    }
}
