<!-- 管理员管理 - 使用通用组件 -->
<template>
  <CrudListPage
    :config="adminListConfig"
    @create="handleAdminCreate"
    @update="handleAdminUpdate"
    @delete="handleAdminDelete"
    @selection-change="handleSelectionChange"
  >
    <!-- 自定义头部操作 -->
    <template #header-actions="{ selectedRows }">
      <ElButton
        v-if="selectedRows.length > 0"
        type="danger"
        @click="handleBatchDelete"
      >
        批量删除 ({{ selectedRows.length }})
      </ElButton>
    </template>
  </CrudListPage>
</template>

<script setup lang="ts">
import { ref, h } from 'vue'
import { ElButton, ElMessage, ElMessageBox, ElTag } from 'element-plus'
import CrudListPage from '@/components/common/crud-list-page/index.vue'
import { AdminService, type AdminFormData } from '@/api/adminApi'
import type { CrudListConfig, CrudFormData } from '@/components/common/crud-list-page/types.ts'

defineOptions({ name: 'Admin' })

// 选中的行
const selectedRows = ref<any[]>([])

/**
 * 获取管理员状态配置
 */
const getAdminStatusConfig = (status: number) => {
  const statusMap: Record<number, { type: 'success' | 'danger' | 'info'; text: string }> = {
    1: { type: 'success', text: '正常' },
    2: { type: 'danger', text: '禁用' }
  }
  return statusMap[status] || { type: 'info', text: '未知' }
}

// 管理员列表配置
const adminListConfig: CrudListConfig = {
  // API配置
  api: {
    list: AdminService.getAdminList,
    create: (data: CrudFormData) => AdminService.createAdmin(data as AdminFormData),
    update: (id: string | number, data: CrudFormData) => AdminService.updateAdmin(Number(id), data as Partial<AdminFormData>),
    delete: (id: string | number) => AdminService.deleteAdmin(Number(id))
  },

  // 表格列配置
  columns: [
    { type: 'selection' },
    { type: 'index', label: '序号' },
    { prop: 'username', label: '用户名' },
    { prop: 'nickname', label: '昵称' },
    { prop: 'email', label: '邮箱' },
    { prop: 'phone', label: '手机号' },
    {
      prop: 'roles',
      label: '角色',
      formatter: (row: any) => {
        if (!row.roles || row.roles.length === 0) {
          return h(ElTag, { type: 'info', size: 'small' }, () => '无角色')
        }
        return h('div', { class: 'flex flex-wrap gap-1' },
          row.roles.map((role: any) =>
            h(ElTag, {
              key: role.id,
              type: 'primary',
              size: 'small',
              style: 'margin-right: 4px; margin-bottom: 2px;'
            }, () => role.name)
          )
        )
      }
    },
    {
      prop: 'status',
      label: '状态',
      formatter: (row: any) => {
        const config = getAdminStatusConfig(row.status)
        return h(ElTag, { type: config.type, size: 'small' }, () => config.text)
      }
    },
    {
      prop: 'lastLoginTime',
      label: '最后登录',
      formatter: (row: any) => {
        return row.lastLoginTime || '从未登录'
      }
    },
    { prop: 'createdAt', label: '创建时间', sortable: true }
  ],

  // 搜索配置
  search: {
    enabled: false
  },

  // 操作配置
  actions: {
    enabled: true,
    width: 240,
    create: {
      enabled: true,
      text: '新增管理员',
      type: 'primary'
    },
    edit: {
      enabled: true
    },
    delete: {
      enabled: true,
      confirmText: '确定要删除这个管理员吗？'
    },
    custom: [
      {
        type: 'view',
        text: '重置密码',
        handler: (row) => handleResetPassword(row)
      },
      {
        type: 'edit',
        text: '状态切换',
        handler: (row) => handleToggleStatus(row)
      }
    ]
  },

  // 弹窗配置
  dialog: {
    enabled: true,
    width: '600px',
    titles: {
      create: '新增管理员',
      edit: '编辑管理员'
    },
    formConfig: [
      {
        prop: 'username',
        label: '用户名',
        type: 'input',
        required: true,
        span: 12
      },
      {
        prop: 'password',
        label: '密码',
        type: 'input',
        required: true,
        span: 12,
        config: { type: 'password' }
      },
      {
        prop: 'nickname',
        label: '昵称',
        type: 'input',
        span: 12
      },
      {
        prop: 'email',
        label: '邮箱',
        type: 'input',
        span: 12
      },
      {
        prop: 'phone',
        label: '手机号',
        type: 'input',
        span: 12
      },
      {
        prop: 'status',
        label: '状态',
        type: 'switch',
        span: 12
      }
    ]
  },

  // 表格配置
  table: {
    rowKey: 'id',
    stripe: true,
    border: false
  }
}

// 事件处理
const handleAdminCreate = (data: CrudFormData) => {
  console.log('创建管理员:', data)
}

const handleAdminUpdate = (id: string | number, data: CrudFormData) => {
  console.log('更新管理员:', id, data)
}

const handleAdminDelete = (id: string | number) => {
  console.log('删除管理员:', id)
}

const handleSelectionChange = (rows: any[]) => {
  selectedRows.value = rows
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个管理员吗？`,
      '批量删除',
      { type: 'warning' }
    )

    // 执行批量删除逻辑
    console.log('批量删除管理员:', selectedRows.value)
    ElMessage.success('批量删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 重置密码
const handleResetPassword = async (admin: any) => {
  try {
    const { value: password } = await ElMessageBox.prompt(
      `请输入 ${admin.username} 的新密码`,
      '重置密码',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'password',
        inputValidator: (value) => {
          if (!value) {
            return '密码不能为空'
          }
          if (value.length < 6) {
            return '密码长度不能少于6位'
          }
          return true
        }
      }
    )

    await AdminService.resetPassword(admin.id, password)
    ElMessage.success('密码重置成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重置密码失败:', error)
      ElMessage.error('重置密码失败')
    }
  }
}

// 切换状态
const handleToggleStatus = async (admin: any) => {
  try {
    const action = admin.status === 1 ? '禁用' : '启用'
    await ElMessageBox.confirm(
      `确定要${action}管理员 ${admin.username} 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await AdminService.toggleStatus(admin.id)
    ElMessage.success(`${action}成功`)
    // 这里需要刷新数据，但通用组件会自动处理
  } catch (error) {
    if (error !== 'cancel') {
      console.error('状态切换失败:', error)
      ElMessage.error('状态切换失败')
    }
  }
}
</script>

<style lang="scss" scoped>

.flex {
  display: flex;
}

.flex-wrap {
  flex-wrap: wrap;
}

.gap-1 {
  gap: 4px;
}

.gap-2 {
  gap: 8px;
}
</style>
