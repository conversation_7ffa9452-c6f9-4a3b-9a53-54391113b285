var e=Object.defineProperty,a=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,r=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t,s=(e,a,l)=>new Promise(((t,r)=>{var s=e=>{try{d(l.next(e))}catch(a){r(a)}},o=e=>{try{d(l.throw(e))}catch(a){r(a)}},d=e=>e.done?t(e.value):Promise.resolve(e.value).then(s,o);d((l=l.apply(e,a)).next())}));import"./index-1JOyfOxu.js";/* empty css                  *//* empty css                  *//* empty css                *//* empty css                  *//* empty css                     *//* empty css               *//* empty css               */import"./el-form-item-l0sNRNKZ.js";/* empty css                 */import{A as o}from"./adminApi-CgMYJCXv.js";import{m as d,r as u,M as i,d as n,w as m,o as p,C as c,D as b,G as f,x as v,aL as g,aK as _,a2 as h,a3 as y,ar as V,as as w,P as j,$ as k,F as D,a8 as U,R as x,W as O,a6 as I,ak as P,E as R}from"./vendor-8T3zXQLl.js";import{_ as A}from"./_plugin-vue_export-helper-BCo6x5W8.js";const C={class:"dialog-footer"},E=A(d({__name:"admin-dialog",props:{visible:{type:Boolean},type:{},adminData:{default:null}},emits:["update:visible","submit"],setup(e,{emit:d}){const A=e,E=d,$=u(),L=u(!1),q=u([]),G=i({username:"",password:"",nickname:"",email:"",phone:"",avatar:"",status:1,roleIds:[]}),K=n({get:()=>A.visible,set:e=>E("update:visible",e)}),B=n((()=>"add"===A.type?"新增管理员":"编辑管理员")),F={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:50,message:"用户名长度在 3 到 50 个字符",trigger:"blur"}],password:[{validator:(e,a,l)=>{"edit"!==A.type||a&&""!==a.trim()?"add"!==A.type||a&&""!==a.trim()?a&&a.trim()&&(a.length<6||a.length>32)?l(new Error("密码长度在 6 到 32 个字符")):l():l(new Error("请输入密码")):l()},trigger:"blur"}],nickname:[{min:2,max:50,message:"昵称长度在 2 到 50 个字符",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]};m((()=>A.visible),(e=>{e&&(H(),"edit"===A.type&&A.adminData&&Object.assign(G,{username:A.adminData.username,nickname:A.adminData.nickname,email:A.adminData.email,phone:A.adminData.phone,avatar:A.adminData.avatar,status:A.adminData.status,roleIds:A.adminData.roles.map((e=>e.id)),password:""}))}));const H=()=>{var e;Object.assign(G,{username:"",password:"",nickname:"",email:"",phone:"",avatar:"",status:1,roleIds:[]}),null==(e=$.value)||e.clearValidate()},J=()=>{K.value=!1},M=()=>s(this,null,(function*(){var e,s;if($.value)try{if(yield $.value.validate(),L.value=!0,"add"===A.type)yield o.createAdmin(G),R.success("创建成功");else{const e=((e,s)=>{for(var o in s||(s={}))l.call(s,o)&&r(e,o,s[o]);if(a)for(var o of a(s))t.call(s,o)&&r(e,o,s[o]);return e})({},G);e.password&&""!==e.password.trim()||delete e.password,yield o.updateAdmin(A.adminData.id,e),R.success("更新成功")}E("submit"),J()}catch(d){(null==(s=null==(e=d.response)?void 0:e.data)?void 0:s.message)?R.error(`提交失败: ${d.response.data.message}`):d.message?R.error(`提交失败: ${d.message}`):R.error("提交失败")}finally{L.value=!1}})),S=()=>s(this,null,(function*(){try{q.value=yield o.getRoles()}catch(e){R.error("获取角色列表失败")}}));return p((()=>{S()})),(e,a)=>{const l=y,t=h,r=_,s=g,o=w,d=V,u=U,i=I,n=P;return b(),c(n,{modelValue:K.value,"onUpdate:modelValue":a[8]||(a[8]=e=>K.value=e),title:B.value,width:"600px","close-on-click-modal":!1,onClose:J},{footer:f((()=>[x("div",C,[v(i,{onClick:J},{default:f((()=>a[9]||(a[9]=[O("取消")]))),_:1,__:[9]}),v(i,{type:"primary",loading:L.value,onClick:M},{default:f((()=>a[10]||(a[10]=[O(" 确定 ")]))),_:1,__:[10]},8,["loading"])])])),default:f((()=>[v(u,{ref_key:"formRef",ref:$,model:G,rules:F,"label-width":"80px","label-position":"right"},{default:f((()=>[v(s,{gutter:20},{default:f((()=>[v(r,{span:12},{default:f((()=>[v(t,{label:"用户名",prop:"username"},{default:f((()=>[v(l,{modelValue:G.username,"onUpdate:modelValue":a[0]||(a[0]=e=>G.username=e),placeholder:"请输入用户名",clearable:""},null,8,["modelValue"])])),_:1})])),_:1}),v(r,{span:12},{default:f((()=>[v(t,{label:"昵称",prop:"nickname"},{default:f((()=>[v(l,{modelValue:G.nickname,"onUpdate:modelValue":a[1]||(a[1]=e=>G.nickname=e),placeholder:"请输入昵称",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),v(s,{gutter:20},{default:f((()=>[v(r,{span:12},{default:f((()=>[v(t,{label:"邮箱",prop:"email"},{default:f((()=>[v(l,{modelValue:G.email,"onUpdate:modelValue":a[2]||(a[2]=e=>G.email=e),placeholder:"请输入邮箱",clearable:""},null,8,["modelValue"])])),_:1})])),_:1}),v(r,{span:12},{default:f((()=>[v(t,{label:"手机号",prop:"phone"},{default:f((()=>[v(l,{modelValue:G.phone,"onUpdate:modelValue":a[3]||(a[3]=e=>G.phone=e),placeholder:"请输入手机号",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),v(s,{gutter:20},{default:f((()=>[v(r,{span:12},{default:f((()=>[v(t,{label:"密码",prop:"password"},{default:f((()=>[v(l,{modelValue:G.password,"onUpdate:modelValue":a[4]||(a[4]=e=>G.password=e),type:"password",placeholder:"add"===e.type?"请输入密码":"留空则不修改密码","show-password":"",clearable:""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),v(r,{span:12},{default:f((()=>[v(t,{label:"状态",prop:"status"},{default:f((()=>[v(d,{modelValue:G.status,"onUpdate:modelValue":a[5]||(a[5]=e=>G.status=e),placeholder:"请选择状态",style:{width:"100%"}},{default:f((()=>[v(o,{label:"正常",value:1}),v(o,{label:"禁用",value:2})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),v(t,{label:"分配角色",prop:"roleIds"},{default:f((()=>[v(d,{modelValue:G.roleIds,"onUpdate:modelValue":a[6]||(a[6]=e=>G.roleIds=e),multiple:"",placeholder:"请选择角色",style:{width:"100%"},clearable:""},{default:f((()=>[(b(!0),j(D,null,k(q.value,(e=>(b(),c(o,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),v(t,{label:"头像",prop:"avatar"},{default:f((()=>[v(l,{modelValue:G.avatar,"onUpdate:modelValue":a[7]||(a[7]=e=>G.avatar=e),placeholder:"请输入头像URL",clearable:""},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-bf6600ba"]]);export{E as default};
