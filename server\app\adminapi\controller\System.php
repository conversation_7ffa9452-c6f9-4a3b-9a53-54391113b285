<?php
declare(strict_types=1);

namespace app\adminapi\controller;

use app\common\controller\BaseController;
use app\common\service\SystemService;

/**
 * 系统配置控制器
 */
class System extends BaseController
{
    /**
     * 获取系统配置
     */
    public function getConfig()
    {
        try {
            $config = SystemService::getConfig();
            return $this->success($config);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 设置系统配置
     */
    public function setConfig()
    {
        $data = $this->request->post();
        
        try {
            SystemService::setConfig($data);
            return $this->success([], '配置保存成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取系统信息
     */
    public function info()
    {
        try {
            $info = [
                'php_version' => PHP_VERSION,
                'thinkphp_version' => \think\App::VERSION,
                'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
                'mysql_version' => \think\facade\Db::query('SELECT VERSION() as version')[0]['version'] ?? 'Unknown',
                'upload_max_filesize' => ini_get('upload_max_filesize'),
                'post_max_size' => ini_get('post_max_size'),
                'memory_limit' => ini_get('memory_limit'),
                'max_execution_time' => ini_get('max_execution_time'),
            ];
            
            return $this->success($info);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 清除缓存
     */
    public function clearCache()
    {
        try {
            // 清除模板缓存
            $runtimePath = runtime_path();
            $this->deleteDir($runtimePath . 'cache');
            $this->deleteDir($runtimePath . 'temp');
            
            return $this->success([], '缓存清除成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 递归删除目录
     */
    private function deleteDir($dir)
    {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . DIRECTORY_SEPARATOR . $file;
            if (is_dir($path)) {
                $this->deleteDir($path);
            } else {
                unlink($path);
            }
        }
        rmdir($dir);
    }
}
