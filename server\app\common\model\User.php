<?php
declare(strict_types=1);

namespace app\common\model;

use think\Model;

/**
 * 用户模型
 */
class User extends Model
{
    // 直接指定完整表名，与其他模型保持一致
    protected $table = 'fs_user';
    protected $pk = 'id';
    
    // 隐藏字段
    protected $hidden = ['password', 'pay_password'];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 字段类型转换
    protected $type = [
        'status' => 'integer',
        'referrer_id' => 'integer',
        'birthday' => 'date',
    ];

    /**
     * 密码修改器
     * @param $value
     * @return string
     */
    public function setPasswordAttr($value): string
    {
        return password_hash($value, PASSWORD_DEFAULT);
    }

    /**
     * 支付密码修改器
     * @param $value
     * @return string
     */
    public function setPayPasswordAttr($value): string
    {
        return empty($value) ? '' : password_hash($value, PASSWORD_DEFAULT);
    }

    /**
     * 状态获取器
     * @param $value
     * @return string
     */
    public function getStatusTextAttr($value): string
    {
        $status = [1 => '正常', 2 => '禁用', 3 => '注销'];
        return $status[$this->status] ?? '未知';
    }

    /**
     * 头像获取器 - 返回原始路径，让前端处理URL
     * @param $value
     * @return string
     */
    public function getAvatarAttr($value): string
    {
        // 直接返回原始路径，不做任何处理
        // 前端会使用 resolveFileUrl 函数来生成正确的URL
        return $value ?? '';
    }

    /**
     * 推荐人关联
     * @return \think\model\relation\BelongsTo
     */
    public function referrer()
    {
        return $this->belongsTo(User::class, 'referrer_id', 'id');
    }

    /**
     * 推荐人昵称获取器
     * @param $value
     * @return string
     */
    public function getReferrerNameAttr($value): string
    {
        if ($this->referrer_id > 0 && $this->referrer) {
            return $this->referrer->nickname;
        }
        return '';
    }
}
