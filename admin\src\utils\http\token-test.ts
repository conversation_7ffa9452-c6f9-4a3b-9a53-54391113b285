/**
 * Token过期处理测试工具
 * 用于测试token过期时的用户体验优化
 */

import { ElMessage } from 'element-plus'

// 模拟token过期的测试函数
export function testTokenExpiration() {
  console.log('🧪 开始测试Token过期处理...')
  
  // 模拟多个并发请求在token过期时的情况
  const requests = [
    { url: '/api/user/list', method: 'GET' },
    { url: '/api/role/list', method: 'GET' },
    { url: '/api/menu/list', method: 'GET' },
    { url: '/api/system/config', method: 'GET' }
  ]
  
  console.log('📝 测试场景：')
  console.log('1. 用户长时间未操作，token已过期')
  console.log('2. 用户同时触发多个API请求')
  console.log('3. 期望结果：只显示一次"登录已过期，请重新登录"提示')
  
  // 模拟并发请求
  requests.forEach((req, index) => {
    setTimeout(() => {
      console.log(`🚀 发送请求 ${index + 1}: ${req.method} ${req.url}`)
      // 这里会触发实际的HTTP请求，如果token过期会被拦截器处理
    }, index * 100) // 间隔100ms发送请求
  })
}

// 验证优化效果的检查点
export function verifyOptimization() {
  console.log('✅ Token过期处理优化验证：')
  console.log('1. ✅ 统一错误消息：所有401错误都显示"登录已过期，请重新登录"')
  console.log('2. ✅ 防重复提示：3秒内相同消息只显示一次')
  console.log('3. ✅ 自动登出：检测到401错误后自动跳转登录页')
  console.log('4. ✅ 状态重置：登出后重置所有用户状态')
  console.log('5. ✅ 友好体验：不显示技术性错误信息')
}

// 在开发环境下可以调用此函数进行测试
if (import.meta.env.DEV) {
  // 添加到全局对象，方便在控制台调用
  ;(window as any).testTokenExpiration = testTokenExpiration
  ;(window as any).verifyOptimization = () => verifyOptimization()
  
  console.log('🔧 开发模式：可在控制台调用以下函数进行测试')
  console.log('- testTokenExpiration(): 测试token过期处理')
  console.log('- verifyOptimization(): 查看优化效果说明')
}
