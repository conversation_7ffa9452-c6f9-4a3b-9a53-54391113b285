<!-- WangEditor 富文本编辑器 插件地址：https://www.wangeditor.com/ -->
<template>
  <div class="editor-wrapper">
    <Toolbar
      class="editor-toolbar"
      :editor="editorRef"
      :mode="mode"
      :defaultConfig="toolbarConfig"
    />
    <Editor
      :style="{ height: height, overflowY: 'hidden' }"
      v-model="modelValue"
      :mode="mode"
      :defaultConfig="editorConfig"
      @onCreated="onCreateEditor"
    />
  </div>
</template>

<script setup lang="ts">
  import '@wangeditor/editor/dist/css/style.css'
  import { onBeforeUnmount, onMounted, shallowRef, computed, inject } from 'vue'
  import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
  import { useUserStore } from '@/store/modules/user'
  import { ElMessage } from 'element-plus'
  import EmojiText from '@/utils/ui/emojo'
  import { IDomEditor, IToolbarConfig, IEditorConfig } from '@wangeditor/editor'

  defineOptions({ name: 'ArtWangEditor' })

  // Props 定义
  interface Props {
    /** 编辑器高度 */
    height?: string
    /** 自定义工具栏配置 */
    toolbarKeys?: string[]
    /** 插入新工具到指定位置 */
    insertKeys?: { index: number; keys: string[] }
    /** 排除的工具栏项 */
    excludeKeys?: string[]
    /** 编辑器模式 */
    mode?: 'default' | 'simple'
    /** 占位符文本 */
    placeholder?: string
    /** 上传配置 */
    uploadConfig?: {
      maxFileSize?: number
      maxNumberOfFiles?: number
      server?: string
    }
  }

  const props = withDefaults(defineProps<Props>(), {
    height: '500px',
    mode: 'default',
    placeholder: '请输入内容...',
    excludeKeys: () => ['fontFamily']
  })

  const modelValue = defineModel<string>({ required: true })

  // 编辑器实例
  const editorRef = shallowRef<IDomEditor>()
  const userStore = useUserStore()

  // 注入父组件提供的媒体选择器控制函数
  const openMediaPicker = inject<() => void>('openMediaPicker')

  // 常量配置
  const DEFAULT_UPLOAD_CONFIG = {
    maxFileSize: 3 * 1024 * 1024, // 3MB
    maxNumberOfFiles: 10,
    fieldName: 'file',
    allowedFileTypes: ['image/*']
  } as const

  // 图标映射配置
  const ICON_MAP = {
    bold: '&#xe630;',
    blockquote: '&#xe61c;',
    underline: '&#xe65a;',
    italic: '&#xe638;',
    'group-more-style': '&#xe648;',
    color: '&#xe68c;',
    bgColor: '&#xe691;',
    bulletedList: '&#xe64e;',
    numberedList: '&#xe66c;',
    todo: '&#xe641;',
    'group-justify': '&#xe67e;',
    'group-indent': '&#xe63e;',
    emotion: '&#xe690;',
    insertLink: '&#xe63a;',
    'group-image': '&#xe634;',
    insertTable: '&#xe67b;',
    codeBlock: '&#xe68b;',
    divider: '&#xe66d;',
    undo: '&#xe65e;',
    redo: '&#xe659;',
    fullScreen: '&#xe633;',
    tableFullWidth: '&#xe67b;'
  } as const

  // 计算属性：上传服务器地址
  const uploadServer = computed(
    () =>
      props.uploadConfig?.server || `${import.meta.env.VITE_API_URL}/api/common/upload/wangeditor`
  )

  // 合并上传配置
  const mergedUploadConfig = computed(() => ({
    ...DEFAULT_UPLOAD_CONFIG,
    ...props.uploadConfig
  }))

  // 工具栏配置
  const toolbarConfig = computed((): Partial<IToolbarConfig> => {
    const config: Partial<IToolbarConfig> = {}

    // 完全自定义工具栏
    if (props.toolbarKeys && props.toolbarKeys.length > 0) {
      config.toolbarKeys = props.toolbarKeys
    }

    // 插入新工具
    if (props.insertKeys) {
      config.insertKeys = props.insertKeys
    }

    // 排除工具
    if (props.excludeKeys && props.excludeKeys.length > 0) {
      config.excludeKeys = props.excludeKeys
    }

    return config
  })

  // 自定义图片上传函数
  const customUploadImg = (file: File, insertFn: (url: string, alt?: string, href?: string) => void) => {
    // 保存插入函数供后续使用
    window.tempInsertImageFn = insertFn
    // 打开父组件的媒体选择器
    if (openMediaPicker) {
      openMediaPicker()
    }
  }

  // 编辑器配置
  const editorConfig: Partial<IEditorConfig> = {
    placeholder: props.placeholder,
    MENU_CONF: {
      // 配置图片上传 - 完全禁用默认上传，使用自定义方式
      uploadImage: {
        // 设置为空字符串禁用默认上传
        server: '',
        // 自定义上传函数
        customUpload: customUploadImg,
        // 其他配置
        fieldName: 'file',
        maxFileSize: 10 * 1024 * 1024, // 10M
        maxNumberOfFiles: 10,
        allowedFileTypes: ['image/*'],
        // 禁用拖拽上传
        withCredentials: false,
        timeout: 30 * 1000, // 30秒
      }
    }
  }

  // 编辑器创建回调
  const onCreateEditor = (editor: IDomEditor) => {
    editorRef.value = editor

    // 监听全屏事件
    editor.on('fullScreen', () => {
      console.log('编辑器进入全屏模式')
    })

    // 拦截图片上传按钮点击事件
    setTimeout(() => {
      try {
        // 查找图片上传按钮
        const uploadButton = document.querySelector('[data-menu-key="uploadImage"]')
        if (uploadButton) {
          // 移除原有的点击事件
          const newButton = uploadButton.cloneNode(true)
          uploadButton.parentNode?.replaceChild(newButton, uploadButton)

          // 添加自定义点击事件
          newButton.addEventListener('click', (e) => {
            e.preventDefault()
            e.stopPropagation()


            // 创建一个虚拟的插入函数
            const insertFn = (url: string, alt?: string, href?: string) => {

              try {
                // 使用 WangEditor 的 API 插入图片
                const imageNode = {
                  type: 'image',
                  src: url,
                  alt: alt || '',
                  style: {
                    width: '',
                    height: ''
                  },
                  children: [{ text: '' }]
                }
                editor.insertNode(imageNode)

              } catch (error) {
                console.error('插入图片失败:', error)
              }
            }

            // 保存插入函数并打开媒体选择器

            window.tempInsertImageFn = insertFn
            if (openMediaPicker) {

              openMediaPicker()
            }
          })


        }
      } catch (error) {
        console.warn('拦截图片上传按钮失败:', error)
      }
    }, 500)
  }

  // 优化的图标替换函数
  const overrideIcons = () => {
    requestAnimationFrame(() => {
      Object.entries(ICON_MAP).forEach(([menuKey, iconCode]) => {
        const button = document.querySelector(`button[data-menu-key="${menuKey}"]`)
        if (button) {
          button.innerHTML = `<i class='iconfont-sys'>${iconCode}</i>`
        }
      })
    })
  }

  // 暴露编辑器实例和方法
  defineExpose({
    /** 获取编辑器实例 */
    getEditor: () => editorRef.value,
    /** 设置编辑器内容 */
    setHtml: (html: string) => editorRef.value?.setHtml(html),
    /** 获取编辑器内容 */
    getHtml: () => editorRef.value?.getHtml(),
    /** 清空编辑器 */
    clear: () => editorRef.value?.clear(),
    /** 聚焦编辑器 */
    focus: () => editorRef.value?.focus()
  })

  // 生命周期
  onMounted(() => {
    overrideIcons()
  })

  onBeforeUnmount(() => {
    const editor = editorRef.value
    if (editor) {
      editor.destroy()
    }
  })
</script>

<style lang="scss">
  @use './style';
</style>
