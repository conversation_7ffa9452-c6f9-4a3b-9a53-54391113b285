<!-- 系统logo -->
<template>
  <div class="art-logo">
    <img :style="logoStyle" :src="logoSrc" alt="logo" />
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { useSystemStore } from '@/store/modules/system'
  import { resolveFileUrl } from '@/utils/url'
  import defaultLogo from '@imgs/common/logo.webp'

  defineOptions({ name: 'ArtLogo' })

  interface Props {
    /** logo 大小 */
    size?: number | string
  }

  const props = withDefaults(defineProps<Props>(), {
    size: 36
  })

  const systemStore = useSystemStore()

  // 使用传入的size或系统配置的size
  const logoStyle = computed(() => {
    const size = props.size || systemStore.getLogoSize()
    return { width: `${size}px` }
  })

  // 动态Logo源 - 自动处理URL
  const logoSrc = computed(() => {
    const siteLogo = systemStore.getSiteLogo()
    if (siteLogo) {
      return resolveFileUrl(siteLogo)
    }
    return defaultLogo
  })
</script>

<style lang="scss" scoped>
  .art-logo {
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 100%;
      height: 100%;
    }
  }
</style>
