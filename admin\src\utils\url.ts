/**
 * URL处理工具函数
 */

/**
 * 获取API基础URL
 */
export function getApiBaseUrl(): string {
  return import.meta.env.VITE_API_URL || window.location.origin + '/adminapi'
}

/**
 * 获取静态资源基础URL
 */
export function getStaticBaseUrl(): string {
  // 从API URL中提取域名部分
  const apiUrl = getApiBaseUrl()
  try {
    const url = new URL(apiUrl)
    return `${url.protocol}//${url.host}`
  } catch {
    return window.location.origin
  }
}

/**
 * 处理文件URL - 自动判断是否需要拼接域名
 * @param filePath 文件路径
 * @returns 完整的文件URL
 */
export function resolveFileUrl(filePath: string | null | undefined): string {
  if (!filePath) {
    return ''
  }

  // 如果已经是完整的URL，直接返回
  if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
    return filePath
  }

  // 如果是相对路径，拼接基础URL
  const baseUrl = getStaticBaseUrl()
  // 统一处理路径分隔符：将反斜杠转换为正斜杠，移除开头的斜杠
  const cleanPath = filePath
    .replace(/\\/g, '/') // 将反斜杠转换为正斜杠
    .replace(/^\/+/, '') // 移除开头的斜杠
  return `${baseUrl}/${cleanPath}`
}

/**
 * 处理头像URL
 * @param avatar 头像路径
 * @param defaultAvatar 默认头像
 * @returns 完整的头像URL
 */
export function resolveAvatarUrl(avatar: string | null | undefined, defaultAvatar?: string): string {
  if (!avatar) {
    return defaultAvatar || new URL('@/assets/img/user/avatar.webp', import.meta.url).href
  }

  return resolveFileUrl(avatar)
}

/**
 * 处理图片URL，支持错误回退
 * @param imagePath 图片路径
 * @param fallbackImage 回退图片
 * @returns 完整的图片URL
 */
export function resolveImageUrl(imagePath: string | null | undefined, fallbackImage?: string): string {
  if (!imagePath) {
    return fallbackImage || ''
  }

  return resolveFileUrl(imagePath)
}

/**
 * 获取文件下载URL
 * @param filePath 文件路径
 * @returns 下载URL
 */
export function getDownloadUrl(filePath: string): string {
  const baseUrl = getApiBaseUrl()
  return `${baseUrl}/media/download?path=${encodeURIComponent(filePath)}`
}

/**
 * 检查URL是否有效
 * @param url URL地址
 * @returns 是否有效
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * 获取文件预览URL（用于图片预览等）
 * @param filePath 文件路径
 * @param options 预览选项
 * @returns 预览URL
 */
export function getPreviewUrl(filePath: string, options?: {
  width?: number
  height?: number
  quality?: number
}): string {
  const baseUrl = resolveFileUrl(filePath)
  
  if (!options || Object.keys(options).length === 0) {
    return baseUrl
  }

  // 如果有预览参数，可以在这里添加图片处理逻辑
  // 例如：?w=200&h=200&q=80
  const params = new URLSearchParams()
  if (options.width) params.set('w', options.width.toString())
  if (options.height) params.set('h', options.height.toString())
  if (options.quality) params.set('q', options.quality.toString())
  
  const queryString = params.toString()
  return queryString ? `${baseUrl}?${queryString}` : baseUrl
}
