<?php
/**
 * 数据库迁移配置
 */
return [
    'paths' => [
        'migrations' => 'database/migrations',
        'seeds' => 'database/seeds'
    ],
    'environments' => [
        'default_migration_table' => 'phinxlog',
        'default_environment' => 'development',
        'development' => [
            'adapter' => 'mysql',
            'host' => env('database.hostname', '127.0.0.1'),
            'name' => env('database.database', 'fanshop'),
            'user' => env('database.username', 'root'),
            'pass' => env('database.password', '123456'),
            'port' => env('database.hostport', '3306'),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
        ]
    ]
];
