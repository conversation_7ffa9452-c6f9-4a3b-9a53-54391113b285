import{A as s,W as a}from"./index-DG9-1w7X.js";import{k as t,P as n,D as l,R as c,X as o,u as i}from"./vendor-84Inc-Pt.js";import{_ as r}from"./_plugin-vue_export-helper-BCo6x5W8.js";const e={class:"card about-project art-custom-card"},u={class:"button-wrap"},d=r(t({__name:"AboutProject",setup(t){const r=s.systemInfo.name,d=s=>{window.open(s)};return(s,t)=>(l(),n("div",e,[c("div",null,[t[8]||(t[8]=c("h2",{class:"box-title"},"关于项目",-1)),c("p",null,o(i(r))+" 是一款专注于用户体验和视觉设计的后台管理系统模版",1),t[9]||(t[9]=c("p",null,"使用了 Vue3、TypeScript、Vite、Element Plus 等前沿技术",-1)),c("div",u,[c("div",{class:"btn art-custom-card",onClick:t[0]||(t[0]=s=>d(i(a).DOCS))},t[4]||(t[4]=[c("span",null,"项目官网",-1),c("i",{class:"iconfont-sys"},"",-1)])),c("div",{class:"btn art-custom-card",onClick:t[1]||(t[1]=s=>d(i(a).INTRODUCE))},t[5]||(t[5]=[c("span",null,"文档",-1),c("i",{class:"iconfont-sys"},"",-1)])),c("div",{class:"btn art-custom-card",onClick:t[2]||(t[2]=s=>d(i(a).GITHUB_HOME))},t[6]||(t[6]=[c("span",null,"Github",-1),c("i",{class:"iconfont-sys"},"",-1)])),c("div",{class:"btn art-custom-card",onClick:t[3]||(t[3]=s=>d(i(a).BLOG))},t[7]||(t[7]=[c("span",null,"博客",-1),c("i",{class:"iconfont-sys"},"",-1)]))])]),t[10]||(t[10]=c("img",{class:"right-img",src:"/admin/assets/draw1-Ce1WF34i.png",alt:"draw1"},null,-1))]))}}),[["__scopeId","data-v-a0198b8a"]]);export{d as default};
