<?php
declare(strict_types=1);

namespace app\adminapi\controller;

use app\common\controller\BaseController;
use app\adminapi\validate\CategoryValidate;
use app\common\service\CategoryService;
use app\common\model\Category as CategoryModel;
use think\response\Json;

class Category extends BaseController
{
    /**
     * 列表
     */
    public function list(): Json
    {
        $params = $this->request->param();
        $result = CategoryService::getList($params);
        return $this->success($result);
    }

    /**
     * 创建
     */
    public function create(): Json
    {
        $data = $this->request->param();
        
        try {
            validate(CategoryValidate::class)->scene('create')->check($data);
            $result = CategoryService::create($data);
            return $this->success($result, '创建成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 更新
     */
    public function update(): J<PERSON>
    {
        $id = $this->request->param('id');
        $data = $this->request->param();
        
        try {
            validate(CategoryValidate::class)->scene('update')->check($data);
            $result = CategoryService::update($id, $data);
            return $this->success($result, '更新成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除
     */
    public function delete(): Json
    {
        $id = $this->request->param('id');
        
        try {
            $category = CategoryModel::find($id);
            if (!$category) {
                return $this->error('分类不存在');
            }
            
            $category->delete();
            return $this->success([], '删除成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
