/**
 * 文章管理API服务
 */

import request from '@/utils/http'

// 文章管理信息接口
export interface ArticleInfo {
  id: number
  title: string
  content?: string
  summary?: string
  category_id?: number
  tags?: string
  cover_image?: string
  author_id?: number
  author_name?: string
  view_count: number
  like_count: number
  is_featured: number
  featured_text: string
  status: number
  status_text: string
  is_visible: number
  sort: number
  created_at: string
  updated_at: string
}

// 文章管理表单数据接口
export interface ArticleFormData {
  title: string
  summary?: string
  content?: string
  category_id?: number
  cover_image?: string
  author_id?: number
  author_name?: string
  is_featured: number
  is_visible: number
  sort: number
}

// 文章管理列表查询参数接口
export interface ArticleListParams {
  current?: number
  size?: number
  title?: string
  status?: number | string
  is_featured?: number | string
  author_name?: string
  category_id?: number
}

// 文章管理列表响应接口
export interface ArticleListResponse {
  list: ArticleInfo[]
  total: number
  current: number
  size: number
}

/**
 * 文章管理服务类
 */
export class ArticleService {
  
  /**
   * 获取列表
   */
  static async getArticleList(params: ArticleListParams = {}): Promise<ArticleListResponse> {
    return request.get<ArticleListResponse>({
      url: '/article/list',
      params
    })
  }

  /**
   * 获取详情
   */
  static async getArticleDetail(id: number): Promise<ArticleInfo> {
    return request.get<ArticleInfo>({
      url: `/article/detail/${id}`
    })
  }

  /**
   * 创建
   */
  static async createArticle(data: ArticleFormData): Promise<ArticleInfo> {
    return request.post<ArticleInfo>({
      url: '/article/create',
      data
    })
  }

  /**
   * 更新
   */
  static async updateArticle(id: number, data: Partial<ArticleFormData>): Promise<ArticleInfo> {
    return request.put<ArticleInfo>({
      url: `/article/update/${id}`,
      data
    })
  }

  /**
   * 删除
   */
  static async deleteArticle(id: number): Promise<void> {
    return request.del({
      url: `/article/delete/${id}`
    })
  }

  /**
   * 批量删除
   */
  static async batchDeleteArticles(ids: number[]): Promise<void> {
    return request.post({
      url: '/article/batch-delete',
      data: { ids }
    })
  }

  /**
   * 更新状态
   */
  static async updateArticleStatus(id: number, status: number): Promise<void> {
    return request.put({
      url: `/article/status/${id}`,
      data: { status }
    })
  }



  /**
   * 获取状态选项
   */
  static async getStatusOptions(): Promise<any[]> {
    const response = await request.get<Record<string, string>>({
      url: '/article/status-options'
    })
    return Object.entries(response).map(([value, label]) => ({
      value: Number(value),
      label: label as string
    }))
  }

  /**
   * 获取发布状态选项
   */
  static async getPublishedOptions(): Promise<any[]> {
    const response = await request.get<Record<string, string>>({
      url: '/article/published-options'
    })
    return Object.entries(response).map(([value, label]) => ({
      value: Number(value),
      label: label as string
    }))
  }
}

export default ArticleService
