import request from '@/utils/http'

// 角色信息接口
export interface RoleInfo {
  id: number
  roleName: string
  roleCode: string
  des: string
  date: string
  enable: boolean
}

// 角色表单数据接口
export interface RoleFormData {
  roleName: string
  roleCode: string
  des: string
  enable: boolean
}

// 权限设置数据接口
export interface PermissionData {
  menuIds: number[]
  permissionIds: number[]
}

// 角色服务类
export class RoleService {
  // 获取角色列表
  static getRoleList() {
    return request.get<RoleInfo[]>({
      url: '/role/list'
    })
  }

  // 创建角色
  static createRole(data: RoleFormData) {
    return request.post<RoleInfo>({
      url: '/role/create',
      data
    })
  }

  // 更新角色
  static updateRole(id: number, data: RoleFormData) {
    return request.put<RoleInfo>({
      url: `/role/update/${id}`,
      data
    })
  }

  // 删除角色
  static deleteRole(id: number) {
    return request.del({
      url: `/role/delete/${id}`
    })
  }

  // 设置角色权限
  static setPermissions(id: number, data: PermissionData) {
    return request.post({
      url: `/role/permissions/${id}`,
      data
    })
  }

  // 获取角色权限
  static getPermissions(id: number) {
    return request.get<PermissionData>({
      url: `/role/permissions/${id}`
    })
  }
}
