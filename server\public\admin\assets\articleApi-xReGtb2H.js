var t=(t,e,r)=>new Promise(((l,i)=>{var u=t=>{try{n(r.next(t))}catch(e){i(e)}},a=t=>{try{n(r.throw(t))}catch(e){i(e)}},n=t=>t.done?l(t.value):Promise.resolve(t.value).then(u,a);n((r=r.apply(t,e)).next())}));import{E as e}from"./index-Bcx6y5fH.js";class r{static getArticleList(){return t(this,arguments,(function*(t={}){return e.get({url:"/article/list",params:t})}))}static getArticleDetail(r){return t(this,null,(function*(){return e.get({url:`/article/detail/${r}`})}))}static createArticle(r){return t(this,null,(function*(){return e.post({url:"/article/create",data:r})}))}static updateArticle(r,l){return t(this,null,(function*(){return e.put({url:`/article/update/${r}`,data:l})}))}static deleteArticle(r){return t(this,null,(function*(){return e.del({url:`/article/delete/${r}`})}))}static batchDeleteArticles(r){return t(this,null,(function*(){return e.post({url:"/article/batch-delete",data:{ids:r}})}))}static updateArticleStatus(r,l){return t(this,null,(function*(){return e.put({url:`/article/status/${r}`,data:{status:l}})}))}static getStatusOptions(){return t(this,null,(function*(){const t=yield e.get({url:"/article/status-options"});return Object.entries(t).map((([t,e])=>({value:Number(t),label:e})))}))}static getPublishedOptions(){return t(this,null,(function*(){const t=yield e.get({url:"/article/published-options"});return Object.entries(t).map((([t,e])=>({value:Number(t),label:e})))}))}}export{r as A};
