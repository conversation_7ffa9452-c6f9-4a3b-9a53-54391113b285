import{_ as s}from"./index-D76Vq0uK.js";import{c as e}from"./index-tBKMHRR1.js";import{m as t,P as l,D as i,R as a,x as o,X as n,u as r}from"./vendor-8T3zXQLl.js";import{_ as c}from"./_plugin-vue_export-helper-BCo6x5W8.js";const m={class:"login-left-view"},p={class:"logo"},f={class:"title"},d={class:"text-wrap"},g=c(t({__name:"LoginLeftView",setup(t){const c=e();return(e,t)=>{const g=s;return i(),l("div",m,[a("div",p,[o(g,{class:"icon"}),a("h1",f,n(r(c).getSiteName()),1)]),t[0]||(t[0]=a("img",{class:"left-bg",src:"/admin/assets/lf_bg-DnUoi7WV.webp"},null,-1)),t[1]||(t[1]=a("img",{class:"left-img",src:"/admin/assets/lf_icon2-CrdSAdav.webp"},null,-1)),a("div",d,[a("h1",null,n(e.$t("login.leftView.title")),1),a("p",null,n(e.$t("login.leftView.subTitle")),1)])])}}}),[["__scopeId","data-v-e36b5bfc"]]);export{g as _};
