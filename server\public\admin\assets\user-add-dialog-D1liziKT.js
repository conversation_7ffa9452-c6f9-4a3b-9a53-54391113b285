var e=(e,a,l)=>new Promise(((r,s)=>{var o=e=>{try{t(l.next(e))}catch(a){s(a)}},d=e=>{try{t(l.throw(e))}catch(a){s(a)}},t=e=>e.done?r(e.value):Promise.resolve(e.value).then(o,d);t((l=l.apply(e,a)).next())}));import{U as a,A as l}from"./index-tBKMHRR1.js";/* empty css                  *//* empty css                *//* empty css               *//* empty css               *//* empty css                 */import"./el-form-item-l0sNRNKZ.js";/* empty css                  */import{m as r,d as s,r as o,M as d,w as t,P as i,D as u,x as p,G as m,a8 as n,a2 as c,R as f,a6 as g,W as w,u as v,aa as _,b2 as b,a3 as P,aL as y,aK as h,ak as V,F as j,E as k,n as x}from"./vendor-8T3zXQLl.js";import{M as U}from"./index-CxM7mvjC.js";import{_ as q}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                   *//* empty css                      *//* empty css                  *//* empty css                     *//* empty css                    */const C={class:"media-upload-field"},N={key:0,class:"selected-media-box"},E=["src"],R={class:"media-overlay"},$={class:"dialog-footer"},z=q(r({__name:"user-add-dialog",props:{visible:{type:Boolean}},emits:["update:visible","submit"],setup(r,{emit:q}){const z=r,G=q,K=s({get:()=>z.visible,set:e=>G("update:visible",e)}),L=o(),M=o(!1),O=d({avatar:"",userPhone:"",nickName:"",password:"",confirmPassword:"",payPassword:"",confirmPayPassword:"",referrer:""}),A={userPhone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],nickName:[{required:!0,message:"请输入昵称",trigger:"blur"},{min:2,max:20,message:"昵称长度必须在2-20位之间",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:32,message:"密码长度必须在6-32位之间",trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入密码",trigger:"blur"},{validator:(e,a,l)=>{a!==O.password?l(new Error("两次输入的密码不一致")):l()},trigger:"blur"}],payPassword:[{required:!0,message:"请输入支付密码",trigger:"blur"},{pattern:/^\d{6}$/,message:"支付密码必须是6位数字",trigger:"blur"}],confirmPayPassword:[{required:!0,message:"请再次输入支付密码",trigger:"blur"},{validator:(e,a,l)=>{a!==O.payPassword?l(new Error("两次输入的支付密码不一致")):l()},trigger:"blur"}]};t((()=>z.visible),(e=>{e&&(Object.assign(O,{avatar:"",userPhone:"",nickName:"",password:"",confirmPassword:"",payPassword:"",confirmPayPassword:"",referrer:""}),x((()=>{var e;null==(e=L.value)||e.clearValidate()})))}));const B=()=>e(this,null,(function*(){L.value&&(yield L.value.validate((l=>e(this,null,(function*(){if(l)try{yield a.createUser(O),k.success("添加用户成功"),K.value=!1,G("submit")}catch(e){k.error("添加用户失败："+(null==e?void 0:e.message)||"未知错误")}})))))})),D=()=>{M.value=!0},F=()=>{O.avatar=""},H=e=>{e.length>0&&(O.avatar=e[0].file_url||"")};return(e,a)=>{const r=g,s=c,o=P,d=h,t=y,k=n,x=V;return u(),i(j,null,[p(x,{modelValue:K.value,"onUpdate:modelValue":a[8]||(a[8]=e=>K.value=e),title:"新增用户",width:"600px","align-center":""},{footer:m((()=>[f("div",$,[p(r,{onClick:a[7]||(a[7]=e=>K.value=!1)},{default:m((()=>a[13]||(a[13]=[w("取消")]))),_:1,__:[13]}),p(r,{type:"primary",onClick:B},{default:m((()=>a[14]||(a[14]=[w("确定")]))),_:1,__:[14]})])])),default:m((()=>[p(k,{ref_key:"formRef",ref:L,model:O,rules:A,"label-width":"100px"},{default:m((()=>[p(s,{label:"头像",prop:"avatar"},{default:m((()=>{return[f("div",C,[O.avatar?(u(),i("div",N,[f("img",{src:(e=O.avatar,l(e)),alt:"头像",class:"media-preview-image"},null,8,E),f("div",R,[p(r,{size:"small",onClick:D},{default:m((()=>a[10]||(a[10]=[w("重新选择")]))),_:1,__:[10]}),p(r,{size:"small",type:"danger",onClick:F},{default:m((()=>a[11]||(a[11]=[w("移除")]))),_:1,__:[11]})])])):(u(),i("div",{key:1,class:"upload-area",onClick:D},[p(v(_),{class:"upload-icon"},{default:m((()=>[p(v(b))])),_:1})])),a[12]||(a[12]=f("div",{class:"upload-tip"},"请选择头像（可选）",-1))])];var e})),_:1}),p(s,{label:"手机号",prop:"userPhone"},{default:m((()=>[p(o,{modelValue:O.userPhone,"onUpdate:modelValue":a[0]||(a[0]=e=>O.userPhone=e),placeholder:"请输入手机号"},null,8,["modelValue"])])),_:1}),p(s,{label:"昵称",prop:"nickName"},{default:m((()=>[p(o,{modelValue:O.nickName,"onUpdate:modelValue":a[1]||(a[1]=e=>O.nickName=e),placeholder:"请输入昵称"},null,8,["modelValue"])])),_:1}),p(t,{gutter:20},{default:m((()=>[p(d,{span:12},{default:m((()=>[p(s,{label:"密码",prop:"password"},{default:m((()=>[p(o,{modelValue:O.password,"onUpdate:modelValue":a[2]||(a[2]=e=>O.password=e),type:"password",placeholder:"请输入密码","show-password":""},null,8,["modelValue"])])),_:1})])),_:1}),p(d,{span:12},{default:m((()=>[p(s,{label:"确认密码",prop:"confirmPassword"},{default:m((()=>[p(o,{modelValue:O.confirmPassword,"onUpdate:modelValue":a[3]||(a[3]=e=>O.confirmPassword=e),type:"password",placeholder:"请再次输入密码","show-password":""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),p(t,{gutter:20},{default:m((()=>[p(d,{span:12},{default:m((()=>[p(s,{label:"支付密码",prop:"payPassword"},{default:m((()=>[p(o,{modelValue:O.payPassword,"onUpdate:modelValue":a[4]||(a[4]=e=>O.payPassword=e),type:"password",placeholder:"请输入6位数字支付密码","show-password":""},null,8,["modelValue"])])),_:1})])),_:1}),p(d,{span:12},{default:m((()=>[p(s,{label:"确认密码",prop:"confirmPayPassword"},{default:m((()=>[p(o,{modelValue:O.confirmPayPassword,"onUpdate:modelValue":a[5]||(a[5]=e=>O.confirmPayPassword=e),type:"password",placeholder:"请再次输入密码","show-password":""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),p(s,{label:"推荐人",prop:"referrer"},{default:m((()=>[p(o,{modelValue:O.referrer,"onUpdate:modelValue":a[6]||(a[6]=e=>O.referrer=e),placeholder:"请输入推荐人手机号或用户名（可选）"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),p(U,{modelValue:M.value,"onUpdate:modelValue":a[9]||(a[9]=e=>M.value=e),title:"选择用户头像",multiple:!1,accept:"image/*",onConfirm:H},null,8,["modelValue"])],64)}}}),[["__scopeId","data-v-77bdbcab"]]);export{z as default};
