<template>
  <CrudListPage
    :config="categoryListConfig"
    @create="handleCategoryCreate"
    @update="handleCategoryUpdate"
    @delete="handleCategoryDelete"
  />
</template>

<script setup lang="ts">
import { reactive, h } from 'vue'
import { ElTag } from 'element-plus'
import CrudListPage from '@/components/common/crud-list-page/index.vue'
import { CategoryService, type CategoryInfo, type CategoryFormData } from '@/api/categoryApi'
import type { CrudListConfig, CrudFormData } from '@/components/common/crud-list-page/types'


// 状态配置
const getStatusConfig = (status: number) => {
  const configs = {
    0: { type: 'danger' as const, text: '禁用' },
    1: { type: 'success' as const, text: '启用' }
  }
  return configs[status as keyof typeof configs] || { type: 'info' as const, text: '未知' }
}

// 配置对象
const categoryListConfig: CrudListConfig<CategoryInfo> = reactive({
  // API配置
  api: {
    list: CategoryService.getCategoryList,
    create: (data: CrudFormData) => CategoryService.createCategory(data as CategoryFormData),
    update: (id: string | number, data: CrudFormData) => CategoryService.updateCategory(Number(id), data as Partial<CategoryFormData>),
    delete: (id: string | number) => CategoryService.deleteCategory(Number(id))
  },

  // 表格列配置
  columns: [
    { type: 'selection' },
    { type: 'index', label: '序号' },
    { prop: 'name', label: '分类名称' },
    { prop: 'article_count', label: '文章数量' },
    {
      prop: 'status',
      label: '状态',
      formatter: (row: any) => {
        const config = getStatusConfig(row.status)
        return h(ElTag, { type: config.type, size: 'small' }, () => config.text)
      }
    },
    { prop: 'sort', label: '排序' },
    { prop: 'created_at', label: '创建时间' }
  ],

  // 搜索配置
  search: {
    enabled: true,
    items: [
      {
        prop: 'name',
        label: '分类名称',
        type: 'input',
        placeholder: '请输入分类名称'
      },
      {
        prop: 'status',
        label: '状态',
        type: 'select',
        options: [
          { label: '全部', value: '' },
          { label: '禁用', value: 0 },
          { label: '启用', value: 1 }
        ]
      }
    ]
  },

  // 弹窗配置
  dialog: {
    enabled: true,
    width: '600px',
    titles: {
      create: '新增分类',
      edit: '编辑分类'
    },
    formConfig: [
      {
        prop: 'name',
        label: '分类名称',
        type: 'input',
        required: true,
        placeholder: '请输入分类名称',
        span: 24,
        rules: [
          { required: true, message: '请输入分类名称', trigger: 'blur' },
          { max: 50, message: '分类名称不能超过50个字符', trigger: 'blur' }
        ]
      },
      {
        prop: 'parent_id',
        label: '上级分类',
        type: 'select',
        placeholder: '请选择上级分类',
        span: 12,
        options: [
          { label: '顶级分类', value: 0 },
          { label: '技术文章', value: 1 },
          { label: '产品介绍', value: 2 }
        ]
      },
      {
        prop: 'status',
        label: '状态',
        type: 'radio',
        span: 12,
        options: [
          { label: '禁用', value: 0 },
          { label: '启用', value: 1 }
        ]
      },
      {
        prop: 'sort',
        label: '排序',
        type: 'number',
        span: 24,
        placeholder: '请输入排序值，数字越大越靠前'
      }
    ]
  },

  // 操作按钮配置
  actions: {
    enabled: true,
    create: {
      enabled: true,
      text: '新增分类',
      type: 'primary'
    },
    edit: {
      enabled: true
    },
    delete: {
      enabled: true,
      confirmText: '确定要删除这个分类吗？删除后该分类下的文章将变为未分类状态。'
    }
  },

  // 表格配置
  table: {
    rowKey: 'id',
    stripe: true,
    border: false,
    size: 'default'
  }
})

// 事件处理
const handleCategoryCreate = (data: CrudFormData) => {
  console.log('创建分类:', data)
}

const handleCategoryUpdate = (id: string | number, data: CrudFormData) => {
  console.log('更新分类:', id, data)
}

const handleCategoryDelete = (id: string | number) => {
  console.log('删除分类:', id)
}
</script>
