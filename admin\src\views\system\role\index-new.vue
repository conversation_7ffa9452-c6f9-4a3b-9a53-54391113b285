<!-- 重构后的角色管理页面 - 使用通用组件 -->
<template>
  <CrudListPage
    :config="roleListConfig"
    @create="handleRoleCreate"
    @update="handleRoleUpdate"
    @delete="handleRoleDelete"
  >
    <!-- 自定义头部操作 -->
    <template #header-actions>
      <ElButton @click="handleSyncPermissions">
        同步权限
      </ElButton>
    </template>
  </CrudListPage>
  
  <!-- 权限设置弹窗 -->
  <ElDialog
    v-model="permissionDialogVisible"
    title="权限设置"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="permission-tree">
      <ElTree
        ref="permissionTreeRef"
        :data="permissionTreeData"
        :props="treeProps"
        show-checkbox
        node-key="id"
        :default-checked-keys="checkedPermissions"
        :default-expand-all="true"
      />
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="permissionDialogVisible = false">取消</ElButton>
        <ElButton type="primary" @click="handleSavePermissions">保存</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { ref, h } from 'vue'
import { ElButton, ElDialog, ElTree, ElTag, ElMessage } from 'element-plus'
import CrudListPage from '@/components/common/crud-list-page/index.vue'
import { RoleService, type RoleFormData } from '@/api/roleApi'
import type { CrudListConfig, CrudFormData } from '@/components/common/crud-list-page/types'

defineOptions({ name: 'RoleListNew' })

// 权限弹窗相关
const permissionDialogVisible = ref(false)
const permissionTreeRef = ref()
const currentRole = ref<any>(null)
const checkedPermissions = ref<string[]>([])

// 权限树数据
const permissionTreeData = ref([
  {
    id: '1',
    label: '系统管理',
    children: [
      { id: '1-1', label: '用户管理' },
      { id: '1-2', label: '角色管理' },
      { id: '1-3', label: '菜单管理' }
    ]
  },
  {
    id: '2',
    label: '内容管理',
    children: [
      { id: '2-1', label: '文章管理' },
      { id: '2-2', label: '分类管理' }
    ]
  }
])

const treeProps = {
  children: 'children',
  label: 'label'
}

// 格式化日期
const formatDate = (date: string) => {
  return new Date(date)
    .toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
    .replace(/\//g, '-')
}

// 角色列表配置
const roleListConfig: CrudListConfig = {
  // API配置
  api: {
    list: RoleService.getRoleList,
    create: (data: CrudFormData) => RoleService.createRole(data as RoleFormData),
    update: (id: string | number, data: CrudFormData) => RoleService.updateRole(Number(id), data as RoleFormData),
    delete: (id: string | number) => RoleService.deleteRole(Number(id))
  },
  
  // 表格列配置
  columns: [
    { type: 'selection', width: 50 },
    { type: 'index', label: '序号', width: 80 },
    { prop: 'roleName', label: '角色名称', width: 150 },
    { prop: 'roleCode', label: '角色编码', width: 150 },
    { prop: 'des', label: '描述', minWidth: 200 },
    {
      prop: 'enable',
      label: '状态',
      width: 100,
      formatter: (row) => {
        return h(ElTag, {
          type: row.enable ? 'primary' : 'info'
        }, () => row.enable ? '启用' : '禁用')
      }
    },
    {
      prop: 'date',
      label: '创建时间',
      width: 180,
      formatter: (row) => formatDate(row.date)
    },
    {
      prop: 'operation',
      label: '操作',
      width: 200,
      fixed: 'right',
      formatter: (row) => {
        return h('div', { class: 'flex gap-2' }, [
          // 权限设置按钮
          h(ElButton, {
            type: 'primary',
            size: 'small',
            link: true,
            onClick: () => handlePermissionSetting(row)
          }, () => '权限设置'),
          
          // 编辑按钮
          h(ElButton, {
            type: 'primary',
            size: 'small',
            link: true,
            onClick: () => handleEdit(row)
          }, () => '编辑'),
          
          // 删除按钮
          h(ElButton, {
            type: 'danger',
            size: 'small',
            link: true,
            onClick: () => handleDelete(row)
          }, () => '删除')
        ])
      }
    }
  ],
  
  // 搜索配置
  search: {
    enabled: true,
    defaultParams: {
      roleName: '',
      roleCode: '',
      enable: ''
    },
    items: [
      {
        label: '角色名称',
        prop: 'roleName',
        type: 'input',
        config: { clearable: true }
      },
      {
        label: '角色编码',
        prop: 'roleCode',
        type: 'input',
        config: { clearable: true }
      },
      {
        label: '状态',
        prop: 'enable',
        type: 'select',
        config: { clearable: true },
        options: () => [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 }
        ]
      }
    ]
  },
  
  // 操作配置
  actions: {
    enabled: false, // 禁用默认操作列，使用自定义操作列
    create: {
      enabled: true,
      text: '新增角色',
      type: 'primary'
    }
  },
  
  // 弹窗配置
  dialog: {
    enabled: true,
    width: '600px',
    titles: {
      create: '新增角色',
      edit: '编辑角色'
    },
    formConfig: [
      {
        prop: 'roleName',
        label: '角色名称',
        type: 'input',
        required: true,
        span: 12,
        rules: [
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ]
      },
      {
        prop: 'roleCode',
        label: '角色编码',
        type: 'input',
        required: true,
        span: 12,
        rules: [
          { pattern: /^[A-Z_]+$/, message: '只能包含大写字母和下划线', trigger: 'blur' }
        ]
      },
      {
        prop: 'des',
        label: '描述',
        type: 'textarea',
        span: 24,
        config: { rows: 3 }
      },
      {
        prop: 'enable',
        label: '状态',
        type: 'switch',
        span: 12
      }
    ]
  },
  
  // 表格配置
  table: {
    rowKey: 'id',
    stripe: false,
    border: false
  }
}

// 事件处理
const handleRoleCreate = (data: CrudFormData) => {
  console.log('创建角色:', data)
}

const handleRoleUpdate = (id: string | number, data: CrudFormData) => {
  console.log('更新角色:', id, data)
}

const handleRoleDelete = (id: string | number) => {
  console.log('删除角色:', id)
}

// 自定义操作处理
const handlePermissionSetting = (row: any) => {
  currentRole.value = row
  checkedPermissions.value = row.permissions || []
  permissionDialogVisible.value = true
}

const handleEdit = (row: any) => {
  // 触发编辑操作
  console.log('编辑角色:', row)
}

const handleDelete = (row: any) => {
  // 触发删除操作
  console.log('删除角色:', row)
}

const handleSyncPermissions = () => {
  ElMessage.success('权限同步成功')
}

const handleSavePermissions = () => {
  const checkedKeys = permissionTreeRef.value.getCheckedKeys()
  console.log('保存权限:', checkedKeys)
  
  // 这里应该调用API保存权限
  ElMessage.success('权限设置成功')
  permissionDialogVisible.value = false
}
</script>

<style scoped>
.permission-tree {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.flex {
  display: flex;
}

.gap-2 {
  gap: 8px;
}
</style>
