<!-- 角色管理 - 使用通用组件 -->
<template>
  <CrudListPage
    :config="roleListConfig"
    @create="handleRoleCreate"
    @update="handleRoleUpdate"
    @delete="handleRoleDelete"
    @selection-change="handleSelectionChange"
  >
    <!-- 自定义头部操作 -->
    <template #header-actions="{ selectedRows }">
      <ElButton @click="handleSyncPermissions">
        同步权限
      </ElButton>
    </template>
  </CrudListPage>
</template>

<script setup lang="ts">
import { ref, h } from 'vue'
import { ElButton, ElMessage, ElTag } from 'element-plus'
import CrudListPage from '@/components/common/crud-list-page/index.vue'
import { RoleService, type RoleInfo, type RoleFormData } from '@/api/roleApi'
import type { CrudListConfig, CrudFormData } from '@/components/common/crud-list-page/types.ts'

defineOptions({ name: 'Role' })

// 选中的行
const selectedRows = ref<any[]>([])

/**
 * 获取角色状态配置
 */
const getRoleStatusConfig = (enable: boolean) => {
  return enable
    ? { type: 'success' as const, text: '启用' }
    : { type: 'danger' as const, text: '禁用' }
}

// 角色列表配置
const roleListConfig: CrudListConfig = {
  // API配置
  api: {
    list: RoleService.getRoleList,
    create: (data: CrudFormData) => RoleService.createRole(data as RoleFormData),
    update: (id: string | number, data: CrudFormData) => RoleService.updateRole(Number(id), data as RoleFormData),
    delete: (id: string | number) => RoleService.deleteRole(Number(id))
  },

  // 表格列配置
  columns: [
    { type: 'selection' },
    { type: 'index', label: '序号' },
    { prop: 'roleName', label: '角色名称' },
    { prop: 'roleCode', label: '角色编码' },
    { prop: 'des', label: '描述' },
    {
      prop: 'enable',
      label: '状态',
      formatter: (row: any) => {
        const config = getRoleStatusConfig(row.enable)
        return h(ElTag, { type: config.type }, () => config.text)
      }
    },
    {
      prop: 'date',
      label: '创建时间',
      sortable: true,
      formatter: (row: any) => {
        return formatDate(row.date)
      }
    }
  ],

  // 搜索配置
  search: {
    enabled: false
  },

  // 操作配置
  actions: {
    enabled: true,
    create: {
      enabled: true,
      text: '新增角色',
      type: 'primary'
    },
    edit: {
      enabled: true
    },
    delete: {
      enabled: true,
      confirmText: '确定要删除这个角色吗？'
    },
    custom: [
      {
        type: 'view',
        text: '权限设置',
        handler: (row) => showPermissionDialog(row)
      }
    ]
  },

  // 弹窗配置
  dialog: {
    enabled: true,
    width: '600px',
    titles: {
      create: '新增角色',
      edit: '编辑角色'
    },
    formConfig: [
      {
        prop: 'roleName',
        label: '角色名称',
        type: 'input',
        required: true,
        span: 12
      },
      {
        prop: 'roleCode',
        label: '角色编码',
        type: 'input',
        required: true,
        span: 12
      },
      {
        prop: 'des',
        label: '描述',
        type: 'textarea',
        span: 24
      },
      {
        prop: 'enable',
        label: '状态',
        type: 'switch',
        span: 12
      }
    ]
  },

  // 表格配置
  table: {
    rowKey: 'id',
    stripe: true,
    border: false
  }
}

// 事件处理
const handleRoleCreate = (data: CrudFormData) => {
  console.log('创建角色:', data)
}

const handleRoleUpdate = (id: string | number, data: CrudFormData) => {
  console.log('更新角色:', id, data)
}

const handleRoleDelete = (id: string | number) => {
  console.log('删除角色:', id)
}

const handleSelectionChange = (rows: any[]) => {
  selectedRows.value = rows
}

const handleSyncPermissions = () => {
  console.log('同步权限功能开发中...')
  ElMessage.success('同步权限功能开发中...')
}

// 权限设置相关
const showPermissionDialog = (row: RoleInfo) => {
  console.log('权限设置功能开发中...', row)
  ElMessage.success('权限设置功能开发中...')
}

// 格式化日期
const formatDate = (date: string) => {
  return new Date(date)
    .toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
    .replace(/\//g, '-')
}
</script>

<style lang="scss" scoped>
  .page-content {
    .svg-icon {
      width: 1.8em;
      height: 1.8em;
      overflow: hidden;
      vertical-align: -8px;
      fill: currentcolor;
    }
  }
</style>
