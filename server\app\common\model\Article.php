<?php
declare(strict_types=1);

namespace app\common\model;

use think\Model;

/**
 * 文章管理模型
 * 
 * @property int $id 文章ID
 * @property string $title 文章标题

 * @property string $content 文章内容
 * @property string $summary 文章摘要
 * @property int $category_id 分类ID
 * @property string $tags 标签
 * @property string $cover_image 封面图片
 * @property int $author_id 作者ID
 * @property string $author_name 作者姓名
 * @property int $view_count 浏览次数
 * @property int $like_count 点赞次数
 * @property string $published_at 发布时间
 * @property int $is_published 是否发布：0=草稿，1=已发布
 * @property int $is_featured 是否推荐：0=否，1=是
 * @property int $status 状态：1=启用，2=禁用
 * @property int $sort 排序
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 * @property string $deleted_at 删除时间
 */
class Article extends Model
{
    // 表名
    protected $table = 'fs_articles';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'category_id' => 'integer',
        'author_id' => 'integer',
        'view_count' => 'integer',
        'like_count' => 'integer',
        'is_published' => 'integer',
        'is_featured' => 'integer',
        'status' => 'integer',
        'sort' => 'integer',
        'published_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];
    
    // 允许批量赋值的字段
    protected $field = [
        'title', 'content', 'summary', 'category_id', 'tags',
        'cover_image', 'author_id', 'author_name', 'is_visible',
        'is_featured', 'status', 'sort'
    ];
    
    // 追加字段
    protected $append = ['status_text', 'published_text', 'featured_text'];

    /**
     * 状态文本获取器
     */
    public function getStatusTextAttr($value, $data): string
    {
        $statusMap = [
            1 => '启用',
            0 => '禁用'
        ];
        return $statusMap[$data['status'] ?? 1] ?? '未知';
    }

    /**
     * 发布状态文本获取器
     */
    public function getPublishedTextAttr($value, $data): string
    {
        return ($data['is_published'] ?? 0) ? '已发布' : '草稿';
    }

    /**
     * 推荐状态文本获取器
     */
    public function getFeaturedTextAttr($value, $data): string
    {
        return ($data['is_featured'] ?? 0) ? '是' : '否';
    }



    /**
     * 作用域：启用状态
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', 1);
    }

    /**
     * 作用域：已发布
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', 1);
    }

    /**
     * 作用域：推荐文章
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', 1);
    }



    /**
     * 获取状态选项
     */
    public static function getStatusOptions(): array
    {
        return [
            1 => '启用',
            2 => '禁用'
        ];
    }

    /**
     * 获取发布状态选项
     */
    public static function getPublishedOptions(): array
    {
        return [
            0 => '草稿',
            1 => '已发布'
        ];
    }

    /**
     * 增加浏览次数
     */
    public function increaseViewCount(): bool
    {
        return $this->save(['view_count' => $this->view_count + 1]);
    }

    /**
     * 发布文章
     */
    public function publish(): bool
    {
        return $this->save([
            'is_published' => 1,
            'published_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 取消发布
     */
    public function unpublish(): bool
    {
        return $this->save([
            'is_published' => 0,
            'published_at' => null
        ]);
    }
}
