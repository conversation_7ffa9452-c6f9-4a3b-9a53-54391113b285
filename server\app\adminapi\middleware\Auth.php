<?php
declare(strict_types=1);

namespace app\adminapi\middleware;

use app\common\service\AuthService;
use think\Response;

/**
 * 管理员认证中间件
 */
class Auth
{
    /**
     * 处理请求
     *
     * @param \think\Request $request
     * @param \Closure       $next
     * @return Response
     */
    public function handle($request, \Closure $next)
    {
        // 获取Authorization头
        $authorization = $request->header('Authorization');
        
        if (!$authorization) {
            return json([
                'code' => 401,
                'msg' => '登录已过期，请重新登录',
                'data' => []
            ]);
        }

        // 提取Token (Bearer Token格式)
        $token = '';
        if (strpos($authorization, 'Bearer ') === 0) {
            $token = substr($authorization, 7);
        } else {
            $token = $authorization;
        }

        if (!$token) {
            return json([
                'code' => 401,
                'msg' => '登录已过期，请重新登录',
                'data' => []
            ]);
        }

        try {
            // 验证Token
            $payload = AuthService::verifyToken($token);
            
            // 检查Token类型
            if (!isset($payload['type']) || $payload['type'] !== 'admin') {
                return json([
                    'code' => 401,
                    'msg' => '登录已过期，请重新登录',
                    'data' => []
                ]);
            }

            // 将用户信息注入到请求中
            $request->user = $payload;
            
            return $next($request);
        } catch (\Exception $e) {
            return json([
                'code' => 401,
                'msg' => '登录已过期，请重新登录',
                'data' => []
            ]);
        }
    }
}
