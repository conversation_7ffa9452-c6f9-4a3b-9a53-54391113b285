/**
 * 公告管理API服务
 *
 * <AUTHOR>
 * @since 1.0.0
 */
import request from '@/utils/http'

/**
 * 公告信息接口
 */
export interface NoticeInfo {
  /** 公告ID */
  id: number
  /** 公告标题 */
  title: string
  /** 公告内容 */
  content: string
  /** 封面图片 */
  cover_image?: string
  /** 公告类型 */
  type?: number
  /** 公告类型文本 */
  type_text?: string
  /** 是否发布 */
  is_published?: number
  /** 发布状态文本 */
  published_text?: string
  /** 是否置顶 */
  is_top: number
  /** 置顶状态文本 */
  top_text: string
  /** 状态 */
  status: number
  /** 状态文本 */
  status_text: string
  /** 排序 */
  sort: number
  /** 浏览量 */
  view_count: number
  /** 发布时间 */
  published_at?: string
  /** 创建时间 */
  created_at: string
  /** 更新时间 */
  updated_at: string
}

/**
 * 公告表单数据接口
 */
export interface NoticeFormData {
  /** 公告标题 */
  title: string
  /** 公告内容 */
  content: string
  /** 封面图片 */
  cover_image?: string
  /** 公告类型 */
  type?: number
  /** 是否发布 */
  is_published?: number
  /** 是否置顶 */
  is_top: number
  /** 状态 */
  status: number
  /** 排序 */
  sort: number
}

/**
 * 公告列表查询参数接口
 */
export interface NoticeListParams {
  /** 当前页码 */
  current?: number
  /** 每页数量 */
  size?: number
  /** 公告标题 */
  title?: string
  /** 公告类型 */
  type?: number
  /** 是否发布 */
  is_published?: number
  /** 是否置顶 */
  is_top?: number
  /** 状态 */
  status?: number
}

/**
 * 公告服务类
 */
export class NoticeService {
  /**
   * 获取公告列表
   *
   * @param params 查询参数
   * @returns 公告列表数据
   */
  static async getNoticeList(params: NoticeListParams): Promise<{
    list: NoticeInfo[]
    total: number
    current: number
    size: number
  }> {
    return request.get({
      url: '/notice/list',
      params
    })
  }

  /**
   * 创建公告
   *
   * @param data 公告数据
   * @returns 创建的公告信息
   */
  static async createNotice(data: NoticeFormData): Promise<NoticeInfo> {
    return request.post({
      url: '/notice/create',
      data
    })
  }

  /**
   * 更新公告
   *
   * @param id 公告ID
   * @param data 更新数据
   * @returns 更新后的公告信息
   */
  static async updateNotice(id: number, data: Partial<NoticeFormData>): Promise<NoticeInfo> {
    if (!id || id <= 0) {
      throw new Error('无效的公告ID')
    }

    return request.put({
      url: `/notice/update/${id}`,
      data
    })
  }

  /**
   * 获取公告详情
   *
   * @param id 公告ID
   * @returns 公告详情
   */
  static async getNoticeDetail(id: number): Promise<NoticeInfo> {
    if (!id || id <= 0) {
      throw new Error('无效的公告ID')
    }

    return request.get<NoticeInfo>({
      url: `/notice/detail/${id}`
    })
  }

  /**
   * 删除公告
   *
   * @param id 公告ID
   */
  static async deleteNotice(id: number): Promise<void> {
    if (!id || id <= 0) {
      throw new Error('无效的公告ID')
    }

    return request.del({
      url: `/notice/delete/${id}`
    })
  }
}
