<?php
declare(strict_types=1);

namespace app\adminapi\validate;

use think\Validate;

/**
 * 文章管理验证器
 */
class ArticleValidate extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        // 基本信息
        'title' => 'require|length:2,200',
        'content' => 'length:0,50000',
        'summary' => 'length:0,1000',

        // 分类和标签
        'category_id' => 'integer|gt:0',
        'tags' => 'length:0,500',

        // 图片
        'cover_image' => 'length:0,500',

        // 作者信息
        'author_id' => 'integer|gt:0',
        'author_name' => 'length:0,100',

        // 显示和推荐状态
        'is_visible' => 'in:0,1',
        'is_featured' => 'in:0,1',

        // 状态
        'status' => 'in:1,2',

        // 排序
        'sort' => 'integer|egt:0'
    ];

    /**
     * 错误信息
     */
    protected $message = [
        'title.require' => '文章标题不能为空',
        'title.length' => '文章标题长度必须在2-200个字符之间',

        'content.length' => '文章内容不能超过50000个字符',
        'summary.length' => '文章摘要不能超过1000个字符',

        'category_id.integer' => '分类ID必须是整数',
        'category_id.gt' => '分类ID必须大于0',

        'tags.length' => '标签不能超过500个字符',

        'cover_image.length' => '封面图片路径不能超过500个字符',

        'author_id.integer' => '作者ID必须是整数',
        'author_id.gt' => '作者ID必须大于0',

        'author_name.length' => '作者姓名不能超过100个字符',

        'is_visible.in' => '显示状态值无效',
        'is_featured.in' => '推荐状态值无效',

        'status.in' => '文章状态值无效',

        'sort.integer' => '排序必须是整数',
        'sort.egt' => '排序不能为负数'
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        'create' => ['title', 'content', 'summary', 'category_id', 'tags', 'cover_image', 'author_id', 'author_name', 'is_visible', 'is_featured', 'status', 'sort'],
        'update' => ['title', 'content', 'summary', 'category_id', 'tags', 'cover_image', 'author_id', 'author_name', 'is_visible', 'is_featured', 'status', 'sort'],
        'status' => ['status'],
        'visible' => ['is_visible']
    ];


}
