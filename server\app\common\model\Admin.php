<?php
declare(strict_types=1);

namespace app\common\model;

use think\Model;
use think\model\concern\SoftDelete;

/**
 * 管理员模型
 */
class Admin extends Model
{
    protected $table = 'fs_admin';
    protected $pk = 'id';
    
    // 隐藏字段
    protected $hidden = ['password'];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 字段类型转换
    protected $type = [
        'status' => 'integer',
        'last_login_time' => 'datetime',
    ];

    /**
     * 关联角色
     */
    public function role()
    {
        return $this->belongsToMany(Role::class, 'admin_role', 'admin_id', 'role_id');
    }

    /**
     * 获取用户权限
     * @return array
     */
    public function getPermissions(): array
    {
        $permissions = [];
        foreach ($this->role as $role) {
            foreach ($role->permission as $permission) {
                $permissions[] = $permission->code;
            }
        }
        return array_unique($permissions);
    }

    /**
     * 获取用户角色代码
     * @return array
     */
    public function getRoleCodes(): array
    {
        return $this->role->column('code');
    }

    /**
     * 密码修改器
     * @param $value
     * @return string
     */
    public function setPasswordAttr($value): string
    {
        return password_hash($value, PASSWORD_DEFAULT);
    }

    /**
     * 状态获取器
     * @param $value
     * @return string
     */
    public function getStatusTextAttr($value): string
    {
        $status = [1 => '正常', 2 => '禁用'];
        return $status[$this->status] ?? '未知';
    }
}
