# CrudListPage 操作按钮配置指南

## 📋 操作按钮类型说明

### 🎯 支持的 ArtButtonTable 类型

`ArtButtonTable` 组件支持以下预定义类型，这些类型会自动显示对应的图标和样式：

| 类型 | 图标 | 颜色 | 用途 |
|------|------|------|------|
| `add` | &#xe602; | PRIMARY | 添加操作 |
| `edit` | &#xe642; | SECONDARY | 编辑操作 |
| `delete` | &#xe783; | ERROR | 删除操作 |
| `view` | &#xe689; | INFO | 查看/预览操作 |
| `more` | &#xe6df; | - | 更多操作 |

### 🔧 操作按钮配置

#### 1. 基础配置
```javascript
const config = {
  actions: {
    enabled: true,
    width: 180, // 操作列宽度
    
    // 新增按钮（显示在表格头部）
    create: {
      enabled: true,
      text: '新增文章',
      type: 'primary'
    },
    
    // 编辑按钮（每行都有）
    edit: {
      enabled: true
    },
    
    // 删除按钮（每行都有）
    delete: {
      enabled: true,
      confirmText: '确定要删除这条记录吗？'
    }
  }
}
```

#### 2. 自定义操作按钮
```javascript
const config = {
  actions: {
    enabled: true,
    custom: [
      // ✅ 推荐：使用支持的类型
      {
        type: 'view',        // 使用 ArtButtonTable 的 view 类型
        text: '预览',
        handler: (row) => {
          console.log('预览:', row)
        }
      },
      
      // ✅ 也可以：使用 ElButton（适合特殊需求）
      {
        type: 'custom',      // 不支持的类型，会自动使用 ElButton
        text: '特殊操作',
        buttonType: 'warning', // ElButton 的 type
        handler: (row) => {
          console.log('特殊操作:', row)
        }
      }
    ]
  }
}
```

### 🎨 实际应用示例

#### 文章管理
```javascript
// admin/src/views/system/article/list/index.vue
const articleConfig = {
  actions: {
    enabled: true,
    width: 180,
    create: {
      enabled: true,
      text: '新增文章',
      type: 'primary'
    },
    edit: {
      enabled: true
    },
    delete: {
      enabled: true,
      confirmText: '确定要删除这篇文章吗？'
    },
    custom: [
      {
        type: 'view',  // ✅ 使用支持的类型
        text: '预览',
        handler: (row) => {
          // 预览文章逻辑
        }
      }
    ]
  }
}
```

#### 角色管理
```javascript
// admin/src/views/system/role/index.vue
const roleConfig = {
  actions: {
    enabled: true,
    width: 180,
    create: {
      enabled: true,
      text: '新增角色',
      type: 'primary'
    },
    edit: {
      enabled: true
    },
    delete: {
      enabled: true,
      confirmText: '确定要删除这个角色吗？'
    },
    custom: [
      {
        type: 'view',  // ✅ 权限设置使用 view 类型
        text: '权限设置',
        handler: (row) => {
          // 权限设置逻辑
        }
      }
    ]
  }
}
```

### 🔄 组件内部处理逻辑

`CrudListPage` 组件会根据操作类型自动选择合适的渲染方式：

```javascript
// 支持的 ArtButtonTable 类型
const supportedTypes = ['add', 'edit', 'delete', 'view', 'more']

if (supportedTypes.includes(action.type)) {
  // 使用 ArtButtonTable（图标按钮）
  return h(ArtButtonTable, {
    type: action.type,
    onClick: () => action.handler && action.handler(row)
  })
} else {
  // 使用 ElButton（文字按钮）
  return h(ElButton, {
    type: action.buttonType || 'primary',
    size: 'small',
    link: true,
    onClick: () => action.handler && action.handler(row)
  }, () => action.text)
}
```

### ⚠️ 常见问题

#### 1. 操作按钮不显示
**原因**：`actions.enabled` 未设置为 `true`
```javascript
// ❌ 错误
const config = {
  actions: {
    // enabled: false, // 默认值
    edit: { enabled: true }
  }
}

// ✅ 正确
const config = {
  actions: {
    enabled: true,  // 必须设置
    edit: { enabled: true }
  }
}
```

#### 2. 自定义按钮显示异常
**原因**：使用了不支持的 `type`
```javascript
// ❌ 可能有问题
{
  type: 'custom',  // 不是 ArtButtonTable 支持的类型
  text: '预览'
}

// ✅ 推荐方案1：使用支持的类型
{
  type: 'view',    // ArtButtonTable 支持
  text: '预览'
}

// ✅ 推荐方案2：明确使用 ElButton
{
  type: 'custom',  // 会自动使用 ElButton
  text: '特殊操作',
  buttonType: 'warning'
}
```

### 🎯 最佳实践

1. **优先使用支持的类型**：`view`、`edit`、`delete` 等
2. **合理设置操作列宽度**：根据按钮数量调整 `width`
3. **提供清晰的确认文本**：删除操作要有明确的确认提示
4. **统一操作风格**：同类型页面使用相似的操作配置

### 🔧 扩展建议

如果需要更多图标类型，可以扩展 `ArtButtonTable` 组件：

```javascript
// admin/src/components/core/forms/art-button-table/index.vue
const defaultButtons = {
  add: { icon: '&#xe602;', color: BgColorEnum.PRIMARY },
  edit: { icon: '&#xe642;', color: BgColorEnum.SECONDARY },
  delete: { icon: '&#xe783;', color: BgColorEnum.ERROR },
  view: { icon: '&#xe689;', color: BgColorEnum.INFO },
  more: { icon: '&#xe6df;', color: '' },
  // 可以添加更多类型
  preview: { icon: '&#xe123;', color: BgColorEnum.SUCCESS },
  download: { icon: '&#xe456;', color: BgColorEnum.WARNING }
}
```
