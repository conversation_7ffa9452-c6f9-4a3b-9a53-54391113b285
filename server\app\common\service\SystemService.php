<?php
declare(strict_types=1);

namespace app\common\service;

use app\common\model\SystemConfig;

/**
 * 系统配置服务类
 */
class SystemService
{
    /**
     * 获取系统配置
     */
    public static function getConfig()
    {
        $configs = SystemConfig::select()->toArray();
        
        $result = [];
        foreach ($configs as $config) {
            $value = $config['config_value'];
            
            // 根据类型转换值
            switch ($config['config_type']) {
                case 'number':
                    $value = (int)$value;
                    break;
                case 'boolean':
                    $value = (bool)$value;
                    break;
                case 'json':
                    $value = json_decode($value, true);
                    break;
                default:
                    $value = (string)$value;
            }
            
            $result[$config['config_key']] = $value;
        }
        
        return $result;
    }

    /**
     * 设置系统配置
     */
    public static function setConfig($data)
    {
        foreach ($data as $key => $value) {
            $config = SystemConfig::where('config_key', $key)->find();
            
            if ($config) {
                // 根据类型转换值
                $configValue = $value;
                if ($config->config_type === 'json') {
                    $configValue = json_encode($value);
                } else {
                    $configValue = (string)$value;
                }
                
                $config->config_value = $configValue;
                $config->save();
            } else {
                // 新增配置
                SystemConfig::create([
                    'config_key' => $key,
                    'config_value' => is_array($value) ? json_encode($value) : (string)$value,
                    'config_type' => is_array($value) ? 'json' : (is_numeric($value) ? 'number' : 'string'),
                    'config_desc' => ''
                ]);
            }
        }
        
        return true;
    }

    /**
     * 获取单个配置值
     */
    public static function getConfigValue($key, $default = null)
    {
        $config = SystemConfig::where('config_key', $key)->find();
        
        if (!$config) {
            return $default;
        }
        
        $value = $config->config_value;
        
        // 根据类型转换值
        switch ($config->config_type) {
            case 'number':
                return (int)$value;
            case 'boolean':
                return (bool)$value;
            case 'json':
                return json_decode($value, true);
            default:
                return (string)$value;
        }
    }

    /**
     * 设置单个配置值
     */
    public static function setConfigValue($key, $value, $desc = '', $type = 'string')
    {
        $config = SystemConfig::where('config_key', $key)->find();
        
        if ($config) {
            $config->config_value = is_array($value) ? json_encode($value) : (string)$value;
            $config->save();
        } else {
            SystemConfig::create([
                'config_key' => $key,
                'config_value' => is_array($value) ? json_encode($value) : (string)$value,
                'config_desc' => $desc,
                'config_type' => $type
            ]);
        }
        
        return true;
    }
}
