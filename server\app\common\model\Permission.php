<?php
declare(strict_types=1);

namespace app\common\model;

use think\Model;

/**
 * 权限模型
 */
class Permission extends Model
{
    protected $table = 'fs_permission';
    protected $pk = 'id';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 字段类型转换
    protected $type = [
        'menu_id' => 'integer',
    ];

    /**
     * 关联菜单
     */
    public function menu()
    {
        return $this->belongsTo(Menu::class, 'menu_id');
    }

    /**
     * 关联角色
     */
    public function role()
    {
        return $this->belongsToMany(Role::class, 'role_permission', 'role_id', 'permission_id');
    }
}
