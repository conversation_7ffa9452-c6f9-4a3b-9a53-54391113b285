var e=Object.defineProperty,t=Object.defineProperties,r=Object.getOwnPropertyDescriptors,n=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,c=(t,r,n)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[r]=n,l=(e,t)=>{for(var r in t||(t={}))a.call(t,r)&&c(e,r,t[r]);if(n)for(var r of n(t))s.call(t,r)&&c(e,r,t[r]);return e},i=(e,t,r)=>c(e,"symbol"!=typeof t?t+"":t,r),o=(e,t,r)=>new Promise(((n,a)=>{var s=e=>{try{l(r.next(e))}catch(t){a(t)}},c=e=>{try{l(r.throw(e))}catch(t){a(t)}},l=e=>e.done?n(e.value):Promise.resolve(e.value).then(s,c);l((r=r.apply(e,t)).next())}));import{a as u}from"./index-XUvv1IdG.js";import{u as h}from"./useTableColumns-Bny9oEJ9.js";import{r as d,M as f,c as g,d as m,p as y,b as p,n as v}from"./vendor-84Inc-Pt.js";var A=(e=>(e.CLEAR_ALL="clear_all",e.CLEAR_CURRENT="clear_current",e.CLEAR_PAGINATION="clear_pagination",e.KEEP_ALL="keep_all",e))(A||{});class b{constructor(e=3e5,t=50,r=!1){i(this,"cache",new Map),i(this,"cacheTime"),i(this,"maxSize"),i(this,"enableLog"),this.cacheTime=e,this.maxSize=t,this.enableLog=r}log(e,...t){this.enableLog}generateKey(e){if(!e||"object"!=typeof e)return JSON.stringify(e);const t=this.sortObjectKeys(e);return JSON.stringify(t)}sortObjectKeys(e){const t={},r=Object.keys(e).sort();for(const n of r){const r=e[n];r&&"object"==typeof r&&!Array.isArray(r)?t[n]=this.sortObjectKeys(r):t[n]=r}return t}generateTags(e){const t=new Set,r=Object.keys(e).filter((t=>!["current","size","total"].includes(t)&&void 0!==e[t]&&""!==e[t]&&null!==e[t]));if(r.length>0){const n=r.map((t=>`${t}:${String(e[t])}`)).join("|");t.add(`search:${n}`)}else t.add("search:default");return t.add(`pagination:${e.size||10}`),t.add("pagination"),t}evictLRU(){if(this.cache.size<=this.maxSize)return;let e="",t=1/0,r=1/0;for(const[n,a]of this.cache.entries())(a.accessCount<t||a.accessCount===t&&a.lastAccessTime<r)&&(e=n,t=a.accessCount,r=a.lastAccessTime);e&&(this.cache.delete(e),this.log(`LRU 清理缓存: ${e}`))}set(e,t,r){const n=this.generateKey(e),a=this.generateTags(e),s=Date.now();this.evictLRU(),this.cache.set(n,{data:t,response:r,timestamp:s,params:n,tags:a,accessCount:1,lastAccessTime:s})}get(e){const t=this.generateKey(e),r=this.cache.get(t);return r?Date.now()-r.timestamp>this.cacheTime?(this.cache.delete(t),null):(r.accessCount++,r.lastAccessTime=Date.now(),r):null}clearByTags(e){let t=0;for(const[r,n]of this.cache.entries()){e.some((e=>Array.from(n.tags).some((t=>t.includes(e)))))&&(this.cache.delete(r),t++)}return t}clearCurrentSearch(e){const t=this.generateKey(e);return this.cache.delete(t)?1:0}clearPagination(){return this.clearByTags(["pagination"])}clear(){this.cache.clear()}getStats(){const e=this.cache.size;let t=0,r=0;for(const n of this.cache.values())t+=JSON.stringify(n.data).length,r+=n.accessCount;return{total:e,size:`${(t/1024).toFixed(2)}KB`,hitRate:`${e>0?(r/e).toFixed(1):"0"} avg hits`}}cleanupExpired(){let e=0;const t=Date.now();for(const[r,n]of this.cache.entries())t-n.timestamp>this.cacheTime&&(this.cache.delete(r),e++);return e}}const z=e=>{if(!e)return{records:[],total:0,current:1,size:10};if(Array.isArray(e))return{records:e,total:e.length,current:1,size:e.length};if("object"==typeof e){const t=e;if("records"in t||"data"in t)return{records:t.records||t.data||[],total:t.total||t.count||0,current:t.current||t.page||t.pageNum||1,size:t.size||t.pageSize||t.limit||10};if("data"in t&&t.data&&"object"==typeof t.data){const e=t.data;if("list"in e||"records"in e||"items"in e)return{records:e.list||e.records||e.items||[],total:e.total||e.count||0,current:e.current||e.page||e.pageNum||t.current||t.page||1,size:e.size||e.pageSize||e.limit||t.size||t.pageSize||10};if(Array.isArray(e))return{records:e,total:e.length,current:1,size:e.length}}if("list"in t||"items"in t){const e=t.list||t.items||[];return{records:e,total:t.total||t.count||e.length,current:t.current||t.page||t.pageNum||1,size:t.size||t.pageSize||t.limit||10}}const r=["data","list","items","records","result"];for(const e of r)if(e in t&&Array.isArray(t[e])){const r=t[e];return{records:r,total:t.total||t.count||r.length,current:t.current||t.page||1,size:t.size||t.pageSize||10}}}return{records:[],total:0,current:1,size:10}},C=(e,t)=>{var r,n,a,s,c,l;e.total=null!=(n=null!=(r=t.total)?r:e.total)?n:0,e.current=null!=(s=null!=(a=t.current)?a:e.current)?s:1,e.size=null!=(l=null!=(c=t.size)?c:e.size)?l:10;const i=Math.max(1,Math.ceil(e.total/e.size));e.current>i&&(e.current=i)};function E(e){const{core:{apiFn:n,apiParams:a={},immediate:s=!0,columnsFactory:c,paginationKey:i={current:"current",size:"size"}},transform:{dataTransformer:E,responseAdapter:R=z}={},performance:{enableCache:L=!1,cacheTime:T=3e5,debounceTime:O=300,maxCacheSize:S=50}={},hooks:{onSuccess:j,onError:N,onCacheHit:_,resetFormCallback:w}={},debug:{enableLog:P=!1}={}}=e,x=(null==i?void 0:i.current)||"current",$=(null==i?void 0:i.size)||"size",K=d(0),D=(e,...t)=>{},U=L?new b(T,S,P):null,k=d(!1),I=d(null),F=d([]);let M=null,B=null;const G=f(Object.assign({[x]:1,[$]:10},a||{})),J=f({current:G[x]||1,size:G[$]||10,total:0}),{width:q}=u(),H=g((()=>{return e=l({},J),n={small:q.value<768},t(e,r(n));var e,n})),W=c?h(c):null,Y=null==W?void 0:W.columns,Q=null==W?void 0:W.columnChecks,V=g((()=>F.value.length>0)),X=g((()=>(K.value,U?U.getStats():{total:0,size:"0KB",hitRate:"0 avg hits"}))),Z=(e=>{const t=(e,...t)=>{};return(r,n)=>{const a={code:"UNKNOWN_ERROR",message:"未知错误",details:r};return r instanceof Error?(a.message=r.message,a.code=r.name):"string"==typeof r&&(a.message=r),t(`${n}:`,r),e&&e(a),a}})(N,P),ee=(e,t)=>{if(!U)return;let r=0;switch(e){case A.CLEAR_ALL:U.clear(),D(`清空所有缓存 - ${t||""}`);break;case A.CLEAR_CURRENT:r=U.clearCurrentSearch(G),D(`清空当前搜索缓存 ${r} 条 - ${t||""}`);break;case A.CLEAR_PAGINATION:r=U.clearPagination(),D(`清空分页缓存 ${r} 条 - ${t||""}`);break;case A.KEEP_ALL:default:D(`保持缓存不变 - ${t||""}`)}K.value++},te=(e,...t)=>o(this,[e,...t],(function*(e,t=L){M&&M.abort();const r=new AbortController;M=r,k.value=!0,I.value=null;try{const a=Object.assign({},G,{[x]:J.current,[$]:J.size},e||{});if(t&&U){const e=U.get(a);if(e)return F.value=e.data,C(J,e.response),G[x]!==J.current&&(G[x]=J.current),G[$]!==J.size&&(G[$]=J.size),k.value=!1,_&&_(e.data,e.response),D("缓存命中"),e.response}const s=yield n(a);if(r.signal.aborted)throw new Error("请求已取消");const c=R(s);let l=(e=>{const t=e.records||e.data||[];return Array.isArray(t)?t:[]})(c);return E&&(l=E(l)),F.value=l,C(J,c),G[x]!==J.current&&(G[x]=J.current),G[$]!==J.size&&(G[$]=J.size),t&&U&&(U.set(a,l,c),K.value++,D("数据已缓存")),j&&j(l,c),c}catch(a){if(a instanceof Error&&"请求已取消"===a.message)return{records:[],total:0,current:1,size:10};F.value=[];throw Z(a,"获取表格数据失败")}finally{k.value=!1,M===r&&(M=null)}})),re=e=>o(this,null,(function*(){try{return yield te(e)}catch(t){return Promise.resolve()}})),ne=e=>o(this,null,(function*(){J.current=1,G[x]=1,ee(A.CLEAR_CURRENT,"搜索数据");try{return yield te(e,!1)}catch(t){return Promise.resolve()}})),ae=((e,t)=>{let r=null,n=null,a=null,s=null;const c=(...c)=>new Promise(((l,i)=>{r&&clearTimeout(r),n=c,a=l,s=i,r=setTimeout((()=>o(void 0,null,(function*(){try{const t=yield e(...c);l(t)}catch(I){i(I)}finally{r=null,n=null,a=null,s=null}}))),t)}));return c.cancel=()=>{r&&(clearTimeout(r),r=null,n=null,a=null,s=null)},c.flush=()=>o(void 0,null,(function*(){if(r&&n&&a&&s){clearTimeout(r),r=null;const t=n,c=a,l=s;n=null,a=null,s=null;try{const r=yield e(...t);return c(r),r}catch(I){throw l(I),I}}return Promise.resolve()})),c})(ne,O);let se=!1;const ce=()=>{M&&M.abort(),ae.cancel()};return L&&U&&(B=setInterval((()=>{const e=U.cleanupExpired();e>0&&(D(`自动清理 ${e} 条过期缓存`),K.value++)}),T/2)),s&&m((()=>o(this,null,(function*(){yield re()})))),y((()=>{ce(),U&&U.clear(),B&&clearInterval(B)})),l({tableData:F,isLoading:p(k),hasError:p(I),isEmpty:g((()=>0===F.value.length)),hasData:V,paginationState:p(J),paginationMobile:H,onPageSizeChange:e=>o(this,null,(function*(){e<=0||(ae.cancel(),J.size=e,J.current=1,G[$]=e,G[x]=1,ee(A.CLEAR_CURRENT,"分页大小变化"),yield re())})),onCurrentPageChange:e=>o(this,null,(function*(){if(!(e<=0||se))if(J.current!==e)try{se=!0,J.current=e,G[x]!==e&&(G[x]=e),yield re()}finally{se=!1}else D("分页页码未变化，跳过请求")})),searchState:G,resetSearch:()=>o(this,null,(function*(){ae.cancel();const e={[x]:1,[$]:G[$]||10};Object.keys(G).forEach((e=>{delete G[e]})),Object.assign(G,a||{},e),J.current=1,J.size=e[$],I.value=null,ee(A.CLEAR_ALL,"重置搜索"),yield re(),w&&(yield v(),w())})),loadData:re,searchData:ne,searchDataDebounced:ae,refreshAll:()=>o(this,null,(function*(){ae.cancel(),ee(A.CLEAR_ALL,"手动刷新"),yield re()})),refreshSoft:()=>o(this,null,(function*(){ee(A.CLEAR_CURRENT,"软刷新"),yield re()})),refreshAfterCreate:()=>o(this,null,(function*(){ae.cancel(),J.current=1,G[x]=1,ee(A.CLEAR_PAGINATION,"新增数据"),yield re()})),refreshAfterUpdate:()=>o(this,null,(function*(){ee(A.CLEAR_CURRENT,"编辑数据"),yield re()})),refreshAfterRemove:()=>o(this,null,(function*(){1===F.value.length&&J.current>1&&(J.current=J.current-1,G[x]=J.current),ee(A.CLEAR_CURRENT,"删除数据"),yield re()})),cacheStatistics:X,invalidateCache:ee,clearExpiredCache:()=>{if(!U)return 0;const e=U.cleanupExpired();return e>0&&K.value++,e},abortRequest:ce,clearAllData:()=>{F.value=[],I.value=null,ee(A.CLEAR_ALL,"清空数据")}},W&&{columns:Y,columnChecks:Q})}export{E as u};
