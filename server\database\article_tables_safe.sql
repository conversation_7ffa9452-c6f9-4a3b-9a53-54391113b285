-- 文章管理相关表结构（安全版本）

-- 分类表
CREATE TABLE IF NOT EXISTS `fs_categories` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '分类名称',
  `slug` varchar(50) NOT NULL DEFAULT '' COMMENT '分类别名',
  `description` varchar(500) DEFAULT '' COMMENT '分类描述',
  `parent_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '上级分类ID',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章分类表';

-- 公告表
CREATE TABLE IF NOT EXISTS `fs_notices` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `title` varchar(200) NOT NULL DEFAULT '' COMMENT '公告标题',
  `content` text NOT NULL COMMENT '公告内容',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '公告类型：1=系统通知，2=功能更新，3=服务公告',
  `is_published` tinyint(1) NOT NULL DEFAULT '0' COMMENT '发布状态：0=草稿，1=已发布',
  `is_top` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否置顶：0=否，1=是',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `view_count` int(11) NOT NULL DEFAULT '0' COMMENT '浏览量',
  `published_at` datetime DEFAULT NULL COMMENT '发布时间',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_is_published` (`is_published`),
  KEY `idx_is_top` (`is_top`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公告表';

-- 插入默认分类数据（如果不存在）
INSERT IGNORE INTO `fs_categories` (`name`, `slug`, `description`, `parent_id`, `sort`, `status`, `created_at`, `updated_at`) VALUES
('技术文章', 'tech', '技术相关文章分类', 0, 1, 1, NOW(), NOW()),
('产品介绍', 'product', '产品介绍文章分类', 0, 2, 1, NOW(), NOW()),
('公司动态', 'news', '公司新闻动态分类', 0, 3, 1, NOW(), NOW());

-- 插入示例公告数据（如果不存在）
INSERT IGNORE INTO `fs_notices` (`title`, `content`, `type`, `is_published`, `is_top`, `status`, `sort`, `view_count`, `published_at`, `created_at`, `updated_at`) VALUES
('系统维护通知', '系统将于今晚22:00-24:00进行维护，期间可能无法正常访问，请提前做好准备。', 1, 1, 1, 1, 10, 156, NOW(), NOW(), NOW()),
('新功能上线公告', '我们很高兴地宣布，新的文章管理功能已经上线，欢迎大家使用！', 2, 1, 0, 1, 5, 89, NOW(), NOW(), NOW()),
('节假日服务安排', '春节期间客服服务时间调整为9:00-18:00，其他时间请通过邮件联系。', 3, 0, 0, 1, 0, 0, NULL, NOW(), NOW());
