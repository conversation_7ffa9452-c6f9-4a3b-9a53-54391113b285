<template>
  <CrudListPage
    :config="noticeListConfig"
    @create="handleNoticeCreate"
    @update="handleNoticeUpdate"
    @delete="handleNoticeDelete"
  />
</template>

<script setup lang="ts">
import { reactive, h } from 'vue'
import { useRouter } from 'vue-router'
import { ElTag } from 'element-plus'
import CrudListPage from '@/components/common/crud-list-page/index.vue'
import { NoticeService, type NoticeInfo, type NoticeFormData } from '@/api/noticeApi'
import type { CrudListConfig, CrudFormData } from '@/components/common/crud-list-page/types'
import { resolveFileUrl } from '@/utils/url'

const router = useRouter()

// 置顶状态配置
const getTopConfig = (status: number) => {
  const configs = {
    0: { type: 'info' as const, text: '否' },
    1: { type: 'warning' as const, text: '是' }
  }
  return configs[status as keyof typeof configs] || { type: 'info' as const, text: '否' }
}

// 状态配置
const getStatusConfig = (status: number) => {
  const configs = {
    0: { type: 'danger' as const, text: '禁用' },
    1: { type: 'success' as const, text: '启用' }
  }
  return configs[status as keyof typeof configs] || { type: 'info' as const, text: '未知' }
}

// 配置对象
const noticeListConfig: CrudListConfig<NoticeInfo> = reactive({
  // API配置
  api: {
    list: NoticeService.getNoticeList,
    update: (id: string | number, data: CrudFormData) => NoticeService.updateNotice(Number(id), data as Partial<NoticeFormData>),
    delete: (id: string | number) => NoticeService.deleteNotice(Number(id))
  },

  // 表格列配置
  columns: [
    { type: 'selection' },
    { type: 'index', label: '序号' },
    {
      prop: 'cover_image',
      label: '封面',
      formatter: (row: any) => {
        return h('div', { class: 'notice-cover-cell' }, [
          row.cover_image
            ? h('img', {
                src: resolveFileUrl(row.cover_image),
                alt: '封面',
                class: 'cover-image',
                style: {
                  width: '50px',
                  height: '50px',
                  objectFit: 'cover',
                  borderRadius: '6px',
                  border: '1px solid #e5e7eb'
                }
              })
            : h('div', {
                class: 'no-cover',
                style: {
                  width: '50px',
                  height: '50px',
                  backgroundColor: '#f3f4f6',
                  borderRadius: '6px',
                  border: '1px solid #e5e7eb',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '12px',
                  color: '#9ca3af'
                }
              }, '无图')
        ])
      }
    },
    { prop: 'title', label: '公告标题' },
    {
      prop: 'is_top',
      label: '置顶',
      formatter: (row: any) => {
        const config = getTopConfig(row.is_top)
        return h(ElTag, { type: config.type, size: 'small' }, () => config.text)
      }
    },
    {
      prop: 'status',
      label: '状态',
      formatter: (row: any) => {
        const config = getStatusConfig(row.status)
        return h(ElTag, { type: config.type, size: 'small' }, () => config.text)
      }
    },
    { prop: 'view_count', label: '浏览量' },
    { prop: 'sort', label: '排序' },
    { prop: 'created_at', label: '创建时间' }
  ],

  // 搜索配置
  search: {
    enabled: false
  },



  // 操作按钮配置
  actions: {
    enabled: true,
    create: {
      enabled: true,
      text: '新增公告',
      type: 'primary'
    },
    edit: {
      enabled: true
    },
    delete: {
      enabled: true,
      confirmText: '确定要删除这条公告吗？'
    }
  },

  // 表格配置
  table: {
    rowKey: 'id',
    stripe: true,
    border: false
  }
})

// 事件处理
const handleNoticeCreate = () => {
  // 跳转到编辑页面（新增模式）
  router.push('/system/article/notice/edit')
}

const handleNoticeUpdate = (id: string | number) => {
  // 跳转到编辑页面（编辑模式）
  router.push(`/system/article/notice/edit/${id}`)
}

const handleNoticeDelete = (id: string | number) => {
  console.log('删除公告:', id)
}
</script>

<style scoped>
.notice-cover-cell {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 0;
}

.notice-cover-cell .cover-image {
  transition: transform 0.2s ease;
  cursor: pointer;
}

.notice-cover-cell .cover-image:hover {
  transform: scale(1.05);
}

.no-cover {
  user-select: none;
  font-weight: 500;
}
</style>
