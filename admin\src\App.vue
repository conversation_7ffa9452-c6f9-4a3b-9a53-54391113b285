<template>
  <ElConfigProvider size="default" :locale="locales[language]" :z-index="3000">
    <RouterView></RouterView>
  </ElConfigProvider>
</template>

<script setup lang="ts">
  import { useUserStore } from './store/modules/user'
  import { useSystemStore } from './store/modules/system'
  import zh from 'element-plus/es/locale/lang/zh-cn'
  import en from 'element-plus/es/locale/lang/en'
  import { systemUpgrade } from './utils/sys'
  import { UserService } from './api/usersApi'
  import { setThemeTransitionClass } from './utils/theme/animation'
  import { checkStorageCompatibility } from './utils/storage'

  const userStore = useUserStore()
  const systemStore = useSystemStore()
  const { language } = storeToRefs(userStore)

  const locales = {
    zh: zh,
    en: en
  }

  onBeforeMount(() => {
    setThemeTransitionClass(true)
  })

  onMounted(() => {
    // 检查存储兼容性
    checkStorageCompatibility()
    // 提升暗黑主题下页面刷新视觉体验
    setThemeTransitionClass(false)
    // 系统升级
    systemUpgrade()
    // 获取用户信息
    getUserInfo()
  })

  // 获取用户信息
  const getUserInfo = async () => {
    // 检查是否有 token，而不是检查 isLogin 状态
    if (userStore.accessToken) {
      try {
        const data = await UserService.getUserInfo()
        userStore.setUserInfo(data)
        // 如果获取用户信息成功，说明 token 有效，设置登录状态
        userStore.setLoginStatus(true)
        // 用户信息获取成功后，加载系统配置
        await systemStore.loadSystemConfig()
      } catch (error: any) {
        console.error('获取用户信息失败:', error)

        // 只有在明确是认证错误时才退出登录
        if (error.response?.status === 401 || error.response?.status === 403) {
          console.error('登录已过期，退出登录')
          // 不显示额外的错误提示，让HTTP拦截器统一处理
          userStore.logOut()
        } else {
          // 其他错误（网络错误、服务器错误等）不退出登录
          console.warn('获取用户信息失败，但保持登录状态。错误:', error.message)
          // 仍然设置登录状态，因为token存在
          userStore.setLoginStatus(true)
          // 尝试加载系统配置
          try {
            await systemStore.loadSystemConfig()
          } catch (configError) {
            console.warn('加载系统配置失败:', configError)
          }
        }
      }
    }
  }
</script>
