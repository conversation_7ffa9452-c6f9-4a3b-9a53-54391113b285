<?php
declare(strict_types=1);

namespace app\adminapi\controller;

use app\common\controller\BaseController;
use app\common\service\UploadService;

/**
 * 文件上传控制器
 */
class Upload extends BaseController
{
    /**
     * 图片上传
     */
    public function image()
    {
        $file = $this->request->file('file');
        
        if (!$file) {
            return $this->error('请选择文件');
        }

        // 验证文件类型
        $allowTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        if (!in_array(strtolower($file->extension()), $allowTypes)) {
            return $this->error('不支持的图片格式');
        }

        // 验证文件大小 (5MB)
        if ($file->getSize() > 5 * 1024 * 1024) {
            return $this->error('图片大小不能超过5MB');
        }

        try {
            $result = UploadService::uploadImage($file, $this->request->user);
            return $this->success($result, '上传成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 文件上传
     */
    public function file()
    {
        $file = $this->request->file('file');
        
        if (!$file) {
            return $this->error('请选择文件');
        }

        // 验证文件类型
        $allowTypes = explode(',', env('UPLOAD_ALLOWED_EXT', 'jpg,jpeg,png,gif,webp,pdf,doc,docx,xls,xlsx'));
        if (!in_array(strtolower($file->extension()), $allowTypes)) {
            return $this->error('不支持的文件格式');
        }

        // 验证文件大小
        $maxSize = (int)env('UPLOAD_MAX_SIZE', 10485760); // 默认10MB
        if ($file->getSize() > $maxSize) {
            return $this->error('文件大小不能超过' . round($maxSize / 1024 / 1024, 2) . 'MB');
        }

        try {
            $result = UploadService::uploadFile($file, $this->request->user);
            return $this->success($result, '上传成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取上传文件列表
     */
    public function list()
    {
        $params = $this->request->get();
        $current = (int)($params['current'] ?? 1);
        $size = (int)($params['size'] ?? 20);
        $type = $params['type'] ?? '';

        try {
            $result = UploadService::getFileList($current, $size, $type);
            return $this->paginate($result['list'], $result['total'], $current, $size);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除文件
     */
    public function delete()
    {
        $id = $this->request->param('id');
        
        try {
            UploadService::deleteFile($id);
            return $this->success([], '删除成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
