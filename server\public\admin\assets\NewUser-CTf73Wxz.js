import{_ as a}from"./index-DacxPkpy.js";import"./index-TSQrMSQp.js";/* empty css                    *//* empty css                    *//* empty css                  *//* empty css                       *//* empty css                        */import{b as e,c as r,d as s,e as t,a as o,f as l}from"./avatar6-BkZJe8A3.js";import{k as p,r as i,M as n,d as c,P as m,D as u,R as d,x as v,W as g,aM as b,u as x,i as f,G as j,aN as _,aO as w,X as h,aP as y}from"./vendor-84Inc-Pt.js";import{_ as V}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                   *//* empty css                      *//* empty css                     *//* empty css                 */import"./el-tooltip-l0sNRNKZ.js";import"./index-XUvv1IdG.js";const k={class:"card art-custom-card"},M={class:"card-header"},U={style:{display:"flex","align-items":"center"}},D=["src"],N={class:"user-name"},O={style:{display:"flex","align-items":"center"}},P={style:{"margin-left":"10px"}},T=V(p({__name:"NewUser",setup(p){const V=i("本月"),T=n([{username:"中小鱼",province:"北京",sex:0,age:22,percentage:60,pro:0,color:"rgb(var(--art-primary)) !important",avatar:e},{username:"何小荷",province:"深圳",sex:1,age:21,percentage:20,pro:0,color:"rgb(var(--art-secondary)) !important",avatar:r},{username:"誶誶淰",province:"上海",sex:1,age:23,percentage:60,pro:0,color:"rgb(var(--art-warning)) !important",avatar:s},{username:"发呆草",province:"长沙",sex:0,age:28,percentage:50,pro:0,color:"rgb(var(--art-info)) !important",avatar:t},{username:"甜筒",province:"浙江",sex:1,age:26,percentage:70,pro:0,color:"rgb(var(--art-error)) !important",avatar:o},{username:"冷月呆呆",province:"湖北",sex:1,age:25,percentage:90,pro:0,color:"rgb(var(--art-success)) !important",avatar:l}]);c((()=>{W()}));const W=()=>{setTimeout((()=>{for(let a=0;a<T.length;a++){let e=T[a];T[a].pro=e.percentage}}),100)};return(e,r)=>{const s=_,t=b,o=w,l=y,p=a;return u(),m("div",k,[d("div",M,[r[1]||(r[1]=d("div",{class:"title"},[d("h4",{class:"box-title"},"新用户"),d("p",{class:"subtitle"},[g("这个月增长"),d("span",{class:"text-success"},"+20%")])],-1)),v(t,{modelValue:x(V),"onUpdate:modelValue":r[0]||(r[0]=a=>f(V)?V.value=a:null)},{default:j((()=>[v(s,{value:"本月",label:"本月"}),v(s,{value:"上月",label:"上月"}),v(s,{value:"今年",label:"今年"})])),_:1},8,["modelValue"])]),v(p,{class:"table",data:x(T),"table-config":{size:"large"}},{default:j((()=>[v(o,{label:"头像",prop:"avatar",width:"150px"},{default:j((a=>[d("div",U,[d("img",{class:"avatar",src:a.row.avatar,alt:"avatar"},null,8,D),d("span",N,h(a.row.username),1)])])),_:1}),v(o,{label:"地区",prop:"province"}),v(o,{label:"性别",prop:"avatar"},{default:j((a=>[d("div",O,[d("span",P,h(1===a.row.sex?"男":"女"),1)])])),_:1}),v(o,{label:"进度",width:"240"},{default:j((a=>[v(l,{percentage:a.row.pro,color:a.row.color,"stroke-width":4,"aria-label":`${a.row.username}的完成进度: ${a.row.pro}%`},null,8,["percentage","color","aria-label"])])),_:1})])),_:1},8,["data"])])}}}),[["__scopeId","data-v-ae0b0b3b"]]);export{T as default};
