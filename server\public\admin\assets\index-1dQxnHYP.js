var e=Object.defineProperty,r=Object.defineProperties,t=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable,p=(r,t,o)=>t in r?e(r,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[t]=o;import{m as s,C as c,D as i,B as l}from"./vendor-8T3zXQLl.js";const b=s((u=((e,r)=>{for(var t in r||(r={}))a.call(r,t)&&p(e,t,r[t]);if(o)for(var t of o(r))n.call(r,t)&&p(e,t,r[t]);return e})({},{name:"RouterOutlet"}),r(u,t({__name:"index",setup:e=>(e,r)=>{const t=l("router-view");return i(),c(t)}}))));var u;export{b as default};
