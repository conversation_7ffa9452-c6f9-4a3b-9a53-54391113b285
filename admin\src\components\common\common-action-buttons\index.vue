<!-- 通用操作按钮组件 -->
<template>
  <div class="common-action-buttons" :class="{ 'flex-layout': layout === 'flex' }">
    <template v-for="(action, index) in visibleActions" :key="index">
      <!-- 编辑按钮 -->
      <ElTooltip
        v-if="action.type === 'edit'"
        :content="action.tooltip || '编辑'"
        placement="top"
        :disabled="!action.tooltip"
      >
        <ArtButtonTable
          type="edit"
          :size="size"
          :disabled="action.disabled"
          @click="handleAction(action, 'edit')"
        />
      </ElTooltip>
      
      <!-- 删除按钮 -->
      <ElTooltip
        v-else-if="action.type === 'delete'"
        :content="action.tooltip || '删除'"
        placement="top"
        :disabled="!action.tooltip"
      >
        <ArtButtonTable
          type="delete"
          :size="size"
          :disabled="action.disabled"
          @click="handleAction(action, 'delete')"
        />
      </ElTooltip>
      
      <!-- 查看按钮 -->
      <ElTooltip
        v-else-if="action.type === 'view'"
        :content="action.tooltip || '查看'"
        placement="top"
        :disabled="!action.tooltip"
      >
        <ArtButtonTable
          type="view"
          :size="size"
          :disabled="action.disabled"
          @click="handleAction(action, 'view')"
        />
      </ElTooltip>
      
      <!-- 自定义按钮 -->
      <ElTooltip
        v-else-if="action.type === 'custom'"
        :content="action.tooltip"
        placement="top"
        :disabled="!action.tooltip"
      >
        <ElButton
          :type="action.buttonType || 'primary'"
          :size="size"
          :icon="action.icon"
          :disabled="action.disabled"
          :loading="action.loading"
          link
          @click="handleAction(action, 'custom')"
        >
          {{ action.text }}
        </ElButton>
      </ElTooltip>
      
      <!-- 更多操作下拉菜单 -->
      <ElDropdown
        v-else-if="action.type === 'dropdown'"
        :size="size"
        @command="(command: string) => handleDropdownAction(action, command)"
      >
        <ElButton :size="size" link>
          更多
          <ElIcon class="el-icon--right">
            <ArrowDown />
          </ElIcon>
        </ElButton>
        <template #dropdown>
          <ElDropdownMenu>
            <ElDropdownItem
              v-for="item in action.items"
              :key="item.command"
              :command="item.command"
              :disabled="item.disabled || !hasPermission(item.permission)"
              :divided="item.divided"
            >
              <ElIcon v-if="item.icon" class="mr-2">
                <component :is="item.icon" />
              </ElIcon>
              {{ item.text }}
            </ElDropdownItem>
          </ElDropdownMenu>
        </template>
      </ElDropdown>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  ElButton,
  ElTooltip,
  ElDropdown,
  ElDropdownMenu,
  ElDropdownItem,
  ElIcon
} from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'

// 操作按钮配置接口
export interface ActionConfig {
  type: 'edit' | 'delete' | 'view' | 'custom' | 'dropdown'
  text?: string
  tooltip?: string
  icon?: string
  buttonType?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  permission?: string
  disabled?: boolean
  loading?: boolean
  visible?: boolean
  handler?: (row: any) => void
  // 下拉菜单项
  items?: Array<{
    command: string
    text: string
    icon?: string
    permission?: string
    disabled?: boolean
    divided?: boolean
    handler?: (row: any) => void
  }>
}

interface Props {
  actions: ActionConfig[]
  row: Record<string, any>
  size?: 'large' | 'default' | 'small'
  layout?: 'inline' | 'flex'
  maxVisible?: number // 最大显示按钮数，超出的放入更多菜单
}

interface Emits {
  (e: 'action', type: string, row: Record<string, any>, action: ActionConfig): void
}

const props = withDefaults(defineProps<Props>(), {
  size: 'default',
  layout: 'inline',
  maxVisible: 3
})

const emit = defineEmits<Emits>()

// 权限检查函数（可以根据实际项目调整）
const hasPermission = (permission?: string): boolean => {
  if (!permission) return true
  
  // 这里应该根据实际的权限系统来实现
  // 例如：return useUserStore().hasPermission(permission)
  return true
}

// 计算可见的操作按钮
const visibleActions = computed(() => {
  return props.actions.filter(action => {
    // 检查可见性
    if (action.visible === false) return false
    
    // 检查权限
    if (!hasPermission(action.permission)) return false
    
    return true
  })
})

// 处理按钮点击
const handleAction = (action: ActionConfig, type: string) => {
  // 如果有自定义处理函数，优先执行
  if (action.handler) {
    action.handler(props.row)
  }
  
  // 触发事件
  emit('action', type, props.row, action)
}

// 处理下拉菜单点击
const handleDropdownAction = (action: ActionConfig, command: string) => {
  const item = action.items?.find(item => item.command === command)
  if (item) {
    // 如果有自定义处理函数，优先执行
    if (item.handler) {
      item.handler(props.row)
    }
    
    // 触发事件
    emit('action', command, props.row, action)
  }
}

// 预设配置
export const createEditAction = (options?: Partial<ActionConfig>): ActionConfig => ({
  type: 'edit',
  tooltip: '编辑',
  ...options
})

export const createDeleteAction = (options?: Partial<ActionConfig>): ActionConfig => ({
  type: 'delete',
  tooltip: '删除',
  ...options
})

export const createViewAction = (options?: Partial<ActionConfig>): ActionConfig => ({
  type: 'view',
  tooltip: '查看详情',
  ...options
})

export const createCustomAction = (
  text: string,
  handler: (row: any) => void,
  options?: Partial<ActionConfig>
): ActionConfig => ({
  type: 'custom',
  text,
  handler,
  buttonType: 'primary',
  ...options
})

export const createDropdownAction = (
  items: ActionConfig['items'],
  options?: Partial<ActionConfig>
): ActionConfig => ({
  type: 'dropdown',
  items,
  ...options
})

// 常用操作配置组合
export const commonActions = {
  // 基础CRUD操作
  crud: [
    createEditAction(),
    createDeleteAction()
  ],
  
  // 带查看的CRUD操作
  crudWithView: [
    createViewAction(),
    createEditAction(),
    createDeleteAction()
  ],
  
  // 用户管理操作
  user: [
    createEditAction(),
    createCustomAction('重置密码', (row) => { /* TODO: 实现重置密码功能 */ }),
    createDeleteAction()
  ],
  
  // 角色管理操作
  role: [
    createCustomAction('权限设置', (row) => { /* TODO: 实现权限设置功能 */ }),
    createEditAction(),
    createDeleteAction()
  ]
}
</script>

<style scoped>
.common-action-buttons {
  display: inline-flex;
  gap: 8px;
  align-items: center;
}

.common-action-buttons.flex-layout {
  flex-wrap: wrap;
}

.mr-2 {
  margin-right: 8px;
}
</style>
