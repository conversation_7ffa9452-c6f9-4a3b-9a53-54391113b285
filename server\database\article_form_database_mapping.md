# 文章表单字段与数据库字段对应关系

## 📋 表单字段映射

### 🎯 当前表单字段（edit.vue）

| 表单字段 | 表单标签 | 输入类型 | 验证规则 | 数据库字段 | 说明 |
|---------|---------|---------|---------|-----------|------|
| `title` | 文章标题 | ElInput | required, max:200 | `title` | 文章标题，最大200字符 |
| `summary` | 文章简介 | ElInput(textarea) | max:500 | `summary` | 文章简介，建议150字以内 |
| `content` | 文章内容 | RichTextEditor | required | `content` | 富文本内容 |
| `category_id` | 文章分类 | ElSelect | - | `category_id` | 分类ID，关联分类表 |
| `author_name` | 作者姓名 | ElInput | - | `author_name` | 作者姓名 |
| `cover_image` | 封面图片 | MediaPicker | - | `cover_image` | 封面图片URL |
| `is_visible` | 显示状态 | ElRadioGroup | - | `is_visible` | 显示状态：1=显示，0=隐藏 |
| `sort` | 排序 | ElInputNumber | min:0, max:9999 | `sort` | 排序值，数值越大越靠前 |

### 🗄️ 数据库表结构调整

#### 原有字段保持
```sql
-- 基础字段
id              int(11) unsigned    NOT NULL AUTO_INCREMENT    -- 主键
title           varchar(200)        NOT NULL                   -- 文章标题
content         longtext                                       -- 文章内容
summary         text                                           -- 文章简介
category_id     int(11) unsigned    DEFAULT NULL               -- 分类ID
author_name     varchar(100)        DEFAULT NULL               -- 作者姓名
cover_image     varchar(500)        DEFAULT NULL               -- 封面图片URL
sort            int(11) unsigned    NOT NULL DEFAULT 0         -- 排序
created_at      datetime            DEFAULT NULL               -- 创建时间
updated_at      datetime            DEFAULT NULL               -- 更新时间
```

#### 新增字段
```sql
-- 新增显示状态字段
is_visible      tinyint(1)          NOT NULL DEFAULT 1         -- 显示状态：0=隐藏，1=显示
```

#### 保留但不在表单中的字段
```sql
-- 系统字段（后端自动处理）
slug            varchar(100)        NOT NULL                   -- 文章别名
author_id       int(11) unsigned    DEFAULT NULL               -- 作者ID
view_count      int(11) unsigned    NOT NULL DEFAULT 0         -- 浏览次数
like_count      int(11) unsigned    NOT NULL DEFAULT 0         -- 点赞次数
published_at    datetime            DEFAULT NULL               -- 发布时间
is_published    tinyint(1)          NOT NULL DEFAULT 0         -- 发布状态（保留兼容）
is_featured     tinyint(1)          NOT NULL DEFAULT 0         -- 推荐状态
status          tinyint(1)          NOT NULL DEFAULT 1         -- 状态
tags            varchar(500)        DEFAULT NULL               -- 标签
deleted_at      datetime            DEFAULT NULL               -- 删除时间
```

### 🔄 字段变更说明

#### 1. 新增字段
- **is_visible**: 替代复杂的发布状态，简化为显示/隐藏
- **索引**: 为 `is_visible` 和 `sort` 添加索引优化查询

#### 2. 字段类型调整
- **cover_image**: 从 `varchar(255)` 扩展到 `varchar(500)` 支持更长的URL
- **author_name**: 从 `varchar(50)` 扩展到 `varchar(100)` 支持更长的姓名
- **content**: 确保为 `longtext` 类型支持长文章

#### 3. 保留兼容性
- 保留原有的 `is_published` 字段，避免破坏现有数据
- 数据迁移时将 `is_published` 值复制到 `is_visible`

### 🎯 API接口调整建议

#### 保存文章接口
```php
// 表单提交的数据结构
$formData = [
    'title' => '文章标题',
    'summary' => '文章简介',
    'content' => '<p>富文本内容</p>',
    'category_id' => 1,
    'author_name' => '作者姓名',
    'cover_image' => '/uploads/cover.jpg',
    'is_visible' => 1,
    'sort' => 10
];

// 后端自动处理的字段
$autoFields = [
    'slug' => generateSlug($formData['title']),
    'author_id' => getCurrentUserId(),
    'published_at' => $formData['is_visible'] ? now() : null,
    'is_published' => $formData['is_visible'], // 兼容性
    'created_at' => now(),
    'updated_at' => now()
];
```

#### 查询文章接口
```php
// 列表查询条件
$where = [
    ['is_visible', '=', 1],  // 只显示可见文章
    ['status', '=', 1]       // 只显示启用文章
];

// 排序
$order = ['sort' => 'desc', 'created_at' => 'desc'];
```

### 📝 执行步骤

1. **备份数据库**
   ```bash
   mysqldump -u username -p database_name fs_articles > articles_backup.sql
   ```

2. **执行更新脚本**
   ```bash
   cd server
   php update_articles_table.php
   ```

3. **验证表结构**
   ```sql
   DESCRIBE fs_articles;
   SHOW INDEX FROM fs_articles;
   ```

4. **测试表单功能**
   - 创建新文章
   - 编辑现有文章
   - 验证字段保存和显示

### ⚠️ 注意事项

1. **数据兼容性**: 保留原有字段，确保现有数据不丢失
2. **索引优化**: 新增索引提升查询性能
3. **字段长度**: 适当增加字段长度支持更多内容
4. **默认值**: 合理设置默认值确保数据完整性

### 🎉 完成后效果

- ✅ 表单字段与数据库完全匹配
- ✅ 支持富文本编辑器
- ✅ 支持媒体文件选择
- ✅ 支持分类选择
- ✅ 支持显示状态控制
- ✅ 支持排序功能
- ✅ 保持数据兼容性
