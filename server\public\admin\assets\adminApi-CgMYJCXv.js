import{E as t}from"./index-1JOyfOxu.js";class a{static getAdminList(a){return t.get({url:"/admin/list",params:a})}static createAdmin(a){return t.post({url:"/admin/create",data:a})}static updateAdmin(a,e){return t.put({url:`/admin/update/${a}`,data:e})}static deleteAdmin(a){return t.del({url:`/admin/delete/${a}`})}static getRoles(){return t.get({url:"/admin/roles"})}static resetPassword(a,e){return t.post({url:`/admin/reset-password/${a}`,data:{password:e}})}static toggleStatus(a){return t.post({url:`/admin/toggle-status/${a}`})}}export{a as A};
