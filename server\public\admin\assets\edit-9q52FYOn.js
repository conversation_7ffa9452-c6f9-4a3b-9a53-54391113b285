var e=Object.defineProperty,l=Object.defineProperties,a=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,s=(l,a,t)=>a in l?e(l,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[a]=t,i=(e,l)=>{for(var a in l||(l={}))o.call(l,a)&&s(e,a,l[a]);if(t)for(var a of t(l))r.call(l,a)&&s(e,a,l[a]);return e},u=(e,l,a)=>new Promise(((t,o)=>{var r=e=>{try{i(a.next(e))}catch(l){o(l)}},s=e=>{try{i(a.throw(e))}catch(l){o(l)}},i=e=>e.done?t(e.value):Promise.resolve(e.value).then(r,s);i((a=a.apply(e,l)).next())}));import{A as d}from"./index-tBKMHRR1.js";/* empty css                *//* empty css                 *//* empty css                        *//* empty css                       *//* empty css                 *//* empty css                  *//* empty css                     */import"./el-form-item-l0sNRNKZ.js";/* empty css                *//* empty css                  */import{m,r as n,d as c,M as p,o as _,P as v,D as f,x as g,G as y,R as h,X as b,a6 as V,W as j,aT as w,a8 as x,a2 as O,a3 as U,ar as k,F as P,$ as A,u as C,aN as R,aW as q,aq as D,E,Y as I,aI as N,at as z,C as G,as as M}from"./vendor-8T3zXQLl.js";import{R as W}from"./index-CUnGyYEl.js";import{M as F}from"./index-CxM7mvjC.js";import{A as H}from"./articleApi-Bo5LYq5B.js";import{_ as J}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                   *//* empty css                  *//* empty css                      *//* empty css                    */const K={class:"article-edit-page"},L={class:"header-content"},S={class:"title-section"},T={class:"action-section"},X={class:"edit-layout"},Y={class:"left-panel"},Z={class:"cover-upload-area"},$={key:1,class:"uploaded-image"},B=["src"],Q={class:"image-overlay"},ee={class:"right-panel"},le={class:"editor-container"};var ae;const te=J(m((ae=i({},{name:"ArticleEdit"}),l(ae,a({__name:"edit",setup(e){const l=I(),a=z(),t=n(),o=n(!1),r=n(!1),s=c((()=>!!a.params.id)),m=c((()=>a.params.id)),J=n([{label:"技术文章",value:1},{label:"产品介绍",value:2},{label:"公司动态",value:3}]),ae=n(!1),te=e=>{e.length>0&&(oe.cover_image=e[0].file_path,E.success("封面图片设置成功"))},oe=p({title:"",summary:"",content:"",category_id:void 0,cover_image:"",author_name:"",is_featured:0,is_visible:1,sort:0}),re={title:[{required:!0,message:"请输入文章标题",trigger:"blur"},{max:200,message:"标题不能超过200个字符",trigger:"blur"}],content:[{required:!0,message:"请输入文章内容",trigger:"blur"}]},se=()=>u(this,null,(function*(){var e,a;if(s.value)try{r.value=!0;const l=yield H.getArticleDetail(Number(m.value));Object.assign(oe,{title:l.title,summary:l.summary||"",content:l.content||"",category_id:l.category_id,cover_image:l.cover_image||"",author_name:l.author_name||"",is_featured:null!=(e=l.is_featured)?e:0,is_visible:null!=(a=l.is_visible)?a:1,sort:l.sort})}catch(t){E.error("加载文章数据失败"),l.push("/system/article/list")}finally{r.value=!1}})),ie=()=>u(this,null,(function*(){try{yield N.confirm("确定要取消吗？未保存的内容将丢失。","提示",{type:"warning"}),l.push("/system/article/list")}catch(e){}})),ue=()=>u(this,null,(function*(){if(t.value)try{yield t.value.validate(),o.value=!0;const e=i({},oe);s.value?(yield H.updateArticle(Number(m.value),e),E.success("文章更新成功")):(yield H.createArticle(e),E.success("文章保存成功")),l.push("/system/article/list")}catch(e){!1!==e&&E.error(s.value?"更新失败":"保存失败")}finally{o.value=!1}}));return _((()=>{se()})),(e,l)=>{const a=V,r=w,i=U,u=O,m=M,n=k,c=q,p=R,_=D,E=x;return f(),v("div",K,[g(r,{class:"page-header",shadow:"never"},{default:y((()=>[h("div",L,[h("div",S,[h("h2",null,b(s.value?"编辑文章":"新增文章"),1)]),h("div",T,[g(a,{onClick:ie},{default:y((()=>l[12]||(l[12]=[j("取消")]))),_:1,__:[12]}),g(a,{type:"primary",onClick:ue,loading:o.value},{default:y((()=>l[13]||(l[13]=[j("保存")]))),_:1,__:[13]},8,["loading"])])])])),_:1}),g(r,{class:"form-content",shadow:"never"},{default:y((()=>[h("div",X,[h("div",Y,[g(E,{ref_key:"formRef",ref:t,model:oe,rules:re,"label-width":"80px","label-position":"left"},{default:y((()=>[g(u,{label:"文章标题",prop:"title"},{default:y((()=>[g(i,{modelValue:oe.title,"onUpdate:modelValue":l[0]||(l[0]=e=>oe.title=e),placeholder:"请输入文章标题",maxlength:"200","show-word-limit":""},null,8,["modelValue"])])),_:1}),g(u,{label:"文章简介",prop:"summary"},{default:y((()=>[g(i,{modelValue:oe.summary,"onUpdate:modelValue":l[1]||(l[1]=e=>oe.summary=e),type:"textarea",rows:3,placeholder:"请输入文章简介，建议150字以内",maxlength:"150","show-word-limit":""},null,8,["modelValue"])])),_:1}),g(u,{label:"文章分类",prop:"category_id"},{default:y((()=>[g(n,{modelValue:oe.category_id,"onUpdate:modelValue":l[2]||(l[2]=e=>oe.category_id=e),placeholder:"请选择文章分类",style:{width:"100%"}},{default:y((()=>[(f(!0),v(P,null,A(J.value,(e=>(f(),G(m,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),g(u,{label:"作者姓名",prop:"author_name"},{default:y((()=>[g(i,{modelValue:oe.author_name,"onUpdate:modelValue":l[3]||(l[3]=e=>oe.author_name=e),placeholder:"请输入作者姓名"},null,8,["modelValue"])])),_:1}),g(u,{label:"封面图片",prop:"cover_image"},{default:y((()=>[h("div",Z,[oe.cover_image?(f(),v("div",$,[h("img",{src:C(d)(oe.cover_image),alt:"封面图片",class:"cover-preview"},null,8,B),h("div",Q,[g(a,{size:"small",onClick:l[5]||(l[5]=e=>ae.value=!0)},{default:y((()=>l[15]||(l[15]=[j("更换")]))),_:1,__:[15]}),g(a,{size:"small",type:"danger",onClick:l[6]||(l[6]=e=>oe.cover_image="")},{default:y((()=>l[16]||(l[16]=[j("删除")]))),_:1,__:[16]})])])):(f(),v("div",{key:0,class:"upload-placeholder",onClick:l[4]||(l[4]=e=>ae.value=!0)},l[14]||(l[14]=[h("div",{class:"upload-icon"},"+",-1)]))),l[17]||(l[17]=h("div",{class:"upload-hint"},"建议尺寸：240*240像素",-1)),g(F,{modelValue:ae.value,"onUpdate:modelValue":l[7]||(l[7]=e=>ae.value=e),multiple:!1,accept:"image/*",title:"选择封面图片",onConfirm:te},null,8,["modelValue"])])])),_:1}),g(u,{label:"推荐状态",prop:"is_featured"},{default:y((()=>[g(p,{modelValue:oe.is_featured,"onUpdate:modelValue":l[8]||(l[8]=e=>oe.is_featured=e)},{default:y((()=>[g(c,{value:0},{default:y((()=>l[18]||(l[18]=[j("不推荐")]))),_:1,__:[18]}),g(c,{value:1},{default:y((()=>l[19]||(l[19]=[j("推荐")]))),_:1,__:[19]})])),_:1},8,["modelValue"])])),_:1}),g(u,{label:"显示状态",prop:"is_visible"},{default:y((()=>[g(p,{modelValue:oe.is_visible,"onUpdate:modelValue":l[9]||(l[9]=e=>oe.is_visible=e)},{default:y((()=>[g(c,{value:1},{default:y((()=>l[20]||(l[20]=[j("显示")]))),_:1,__:[20]}),g(c,{value:0},{default:y((()=>l[21]||(l[21]=[j("隐藏")]))),_:1,__:[21]})])),_:1},8,["modelValue"])])),_:1}),g(u,{label:"排序",prop:"sort"},{default:y((()=>[g(_,{modelValue:oe.sort,"onUpdate:modelValue":l[10]||(l[10]=e=>oe.sort=e),min:0,max:9999,placeholder:"数值越大越靠前",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])]),h("div",ee,[l[22]||(l[22]=h("div",{class:"panel-title"},"文章内容",-1)),g(E,{model:oe,"label-width":"0"},{default:y((()=>[g(u,{prop:"content"},{default:y((()=>[h("div",le,[g(W,{modelValue:oe.content,"onUpdate:modelValue":l[11]||(l[11]=e=>oe.content=e),height:"600px",mode:"article",placeholder:"请输入文章内容..."},null,8,["modelValue"])])])),_:1})])),_:1},8,["model"])])])])),_:1})])}}})))),[["__scopeId","data-v-6265dee7"]]);export{te as default};
