<?php
declare(strict_types=1);

namespace app\common\model;

use think\Model;

/**
 * 菜单模型
 */
class Menu extends Model
{
    protected $table = 'fs_menu';
    protected $pk = 'id';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 字段类型转换
    protected $type = [
        'parent_id' => 'integer',
        'sort' => 'integer',
        'type' => 'integer',
        'status' => 'integer',
        'keep_alive' => 'integer',
        'is_iframe' => 'integer',
    ];

    /**
     * 关联权限
     */
    public function permission()
    {
        return $this->hasMany(Permission::class, 'menu_id');
    }

    /**
     * 关联角色
     */
    public function role()
    {
        return $this->belongsToMany(Role::class, 'role_menu', 'role_id', 'menu_id');
    }

    /**
     * 获取子菜单
     */
    public function children()
    {
        return $this->hasMany(self::class, 'parent_id')->order('sort', 'asc');
    }

    /**
     * 获取父菜单
     */
    public function parent()
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    /**
     * 类型获取器
     * @param $value
     * @return string
     */
    public function getTypeTextAttr($value): string
    {
        $type = [1 => '菜单', 2 => '按钮'];
        return $type[$this->type] ?? '未知';
    }

    /**
     * 状态获取器
     * @param $value
     * @return string
     */
    public function getStatusTextAttr($value): string
    {
        $status = [0 => '隐藏', 1 => '显示'];
        return $status[$this->status] ?? '未知';
    }
}
