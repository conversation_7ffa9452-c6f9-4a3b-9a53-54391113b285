import{_ as a}from"./index-Zs-Thxwm.js";import"./index-1JOyfOxu.js";/* empty css                    *//* empty css                    *//* empty css                  *//* empty css                       *//* empty css                        */import{b as e,c as r,d as t,e as s,a as o,f as l}from"./avatar6-C1kCQLrX.js";import{m as p,r as i,M as n,o as c,P as m,D as u,R as d,x as v,W as g,G as _,aM as f,i as b,u as x,aN as j,aO as w,X as h,aP as y}from"./vendor-8T3zXQLl.js";import{_ as U}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                   *//* empty css                      *//* empty css                     *//* empty css                 */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./index-Wowen7jt.js";const V={class:"card art-custom-card"},k={class:"card-header"},I={style:{display:"flex","align-items":"center"}},M=["src"],N={class:"user-name"},O={style:{display:"flex","align-items":"center"}},P={style:{"margin-left":"10px"}},X=U(p({__name:"NewUser",setup(p){const U=i("本月"),X=n([{username:"中小鱼",province:"北京",sex:0,age:22,percentage:60,pro:0,color:"rgb(var(--art-primary)) !important",avatar:e},{username:"何小荷",province:"深圳",sex:1,age:21,percentage:20,pro:0,color:"rgb(var(--art-secondary)) !important",avatar:r},{username:"誶誶淰",province:"上海",sex:1,age:23,percentage:60,pro:0,color:"rgb(var(--art-warning)) !important",avatar:t},{username:"发呆草",province:"长沙",sex:0,age:28,percentage:50,pro:0,color:"rgb(var(--art-info)) !important",avatar:s},{username:"甜筒",province:"浙江",sex:1,age:26,percentage:70,pro:0,color:"rgb(var(--art-error)) !important",avatar:o},{username:"冷月呆呆",province:"湖北",sex:1,age:25,percentage:90,pro:0,color:"rgb(var(--art-success)) !important",avatar:l}]);c((()=>{$()}));const $=()=>{setTimeout((()=>{for(let a=0;a<X.length;a++){let e=X[a];X[a].pro=e.percentage}}),100)};return(e,r)=>{const t=f,s=j,o=w,l=y,p=a;return u(),m("div",V,[d("div",k,[r[4]||(r[4]=d("div",{class:"title"},[d("h4",{class:"box-title"},"新用户"),d("p",{class:"subtitle"},[g("这个月增长"),d("span",{class:"text-success"},"+20%")])],-1)),v(s,{modelValue:x(U),"onUpdate:modelValue":r[0]||(r[0]=a=>b(U)?U.value=a:null)},{default:_((()=>[v(t,{value:"本月"},{default:_((()=>r[1]||(r[1]=[g("本月")]))),_:1,__:[1]}),v(t,{value:"上月"},{default:_((()=>r[2]||(r[2]=[g("上月")]))),_:1,__:[2]}),v(t,{value:"今年"},{default:_((()=>r[3]||(r[3]=[g("今年")]))),_:1,__:[3]})])),_:1},8,["modelValue"])]),v(p,{class:"table",data:x(X),"table-config":{size:"large"}},{default:_((()=>[v(o,{label:"头像",prop:"avatar",width:"150px"},{default:_((a=>[d("div",I,[d("img",{class:"avatar",src:a.row.avatar,alt:"avatar"},null,8,M),d("span",N,h(a.row.username),1)])])),_:1}),v(o,{label:"地区",prop:"province"}),v(o,{label:"性别",prop:"avatar"},{default:_((a=>[d("div",O,[d("span",P,h(1===a.row.sex?"男":"女"),1)])])),_:1}),v(o,{label:"进度",width:"240"},{default:_((a=>[v(l,{percentage:a.row.pro,color:a.row.color,"stroke-width":4,"aria-label":`${a.row.username}的完成进度: ${a.row.pro}%`},null,8,["percentage","color","aria-label"])])),_:1})])),_:1},8,["data"])])}}}),[["__scopeId","data-v-5e3a9f2a"]]);export{X as default};
