import{m as e,M as t,p as o,aQ as r,C as i,D as s,Y as p}from"./vendor-8T3zXQLl.js";import{C as l}from"./index-C8nK2eTm.js";import{N as a}from"./noticeApi-DW6ppHlN.js";import{A as n}from"./index-Bcx6y5fH.js";import{_ as m}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-DyBuW4xE.js";import"./index-D1l9fF2S.js";import"./index-Drb3fmHv.js";/* empty css                   *//* empty css                      *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./index-BPyeCy-r.js";/* empty css                *//* empty css               *//* empty css                  *//* empty css               */import"./el-form-item-l0sNRNKZ.js";/* empty css                       *//* empty css                 */import"./index-BgthDRX_.js";import"./index-7x-iptGQ.js";/* empty css                  *//* empty css                    */const c=m(e({__name:"index",setup(e){const m=p(),c=t({api:{list:a.getNoticeList,update:(e,t)=>a.updateNotice(Number(e),t),delete:e=>a.deleteNotice(Number(e))},columns:[{type:"selection"},{type:"index",label:"序号"},{prop:"cover_image",label:"封面",formatter:e=>o("div",{class:"notice-cover-cell"},[e.cover_image?o("img",{src:n(e.cover_image),alt:"封面",class:"cover-image",style:{width:"50px",height:"50px",objectFit:"cover",borderRadius:"6px",border:"1px solid #e5e7eb"}}):o("div",{class:"no-cover",style:{width:"50px",height:"50px",backgroundColor:"#f3f4f6",borderRadius:"6px",border:"1px solid #e5e7eb",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"12px",color:"#9ca3af"}},"无图")])},{prop:"title",label:"公告标题"},{prop:"is_top",label:"置顶",formatter:e=>{const t={0:{type:"info",text:"否"},1:{type:"warning",text:"是"}}[e.is_top]||{type:"info",text:"否"};return o(r,{type:t.type,size:"small"},(()=>t.text))}},{prop:"status",label:"状态",formatter:e=>{const t={0:{type:"danger",text:"禁用"},1:{type:"success",text:"启用"}}[e.status]||{type:"info",text:"未知"};return o(r,{type:t.type,size:"small"},(()=>t.text))}},{prop:"view_count",label:"浏览量"},{prop:"sort",label:"排序"},{prop:"created_at",label:"创建时间"}],search:{enabled:!1},actions:{enabled:!0,create:{enabled:!0,text:"新增公告",type:"primary"},edit:{enabled:!0},delete:{enabled:!0,confirmText:"确定要删除这条公告吗？"}},table:{rowKey:"id",stripe:!0,border:!1}}),d=()=>{m.push("/system/article/notice/edit")},x=e=>{m.push(`/system/article/notice/edit/${e}`)},j=e=>{};return(e,t)=>(s(),i(l,{config:c,onCreate:d,onUpdate:x,onDelete:j},null,8,["config"]))}}),[["__scopeId","data-v-6dc6ec19"]]);export{c as default};
