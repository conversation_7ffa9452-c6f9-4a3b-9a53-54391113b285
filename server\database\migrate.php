<?php
/**
 * 数据库迁移脚本
 */

require_once __DIR__ . '/../vendor/autoload.php';

use think\facade\Db;

// 初始化应用
$app = new \think\App();
$app->initialize();

try {
    echo "开始执行数据库迁移...\n";
    
    // 创建图片分类表
    $sql1 = "CREATE TABLE IF NOT EXISTS `fs_media_category` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `name` varchar(100) NOT NULL COMMENT '分类名称',
      `parent_id` int(11) DEFAULT 0 COMMENT '父分类ID',
      `sort` int(11) DEFAULT 0 COMMENT '排序',
      `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
      `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='媒体文件分类表'";
    
    Db::execute($sql1);
    echo "✓ 创建媒体分类表成功\n";
    
    // 检查并添加字段到 fs_upload 表
    try {
        // 添加 file_url 字段
        $sql2 = "ALTER TABLE `fs_upload` ADD COLUMN `file_url` varchar(500) DEFAULT NULL COMMENT '文件访问URL' AFTER `file_path`";
        Db::execute($sql2);
        echo "✓ 添加 file_url 字段成功\n";
    } catch (\Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "- file_url 字段已存在\n";
        } else {
            throw $e;
        }
    }
    
    try {
        // 添加 category_id 字段
        $sql3 = "ALTER TABLE `fs_upload` ADD COLUMN `category_id` int(11) DEFAULT 0 COMMENT '分类ID' AFTER `mime_type`";
        Db::execute($sql3);
        echo "✓ 添加 category_id 字段成功\n";
    } catch (\Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "- category_id 字段已存在\n";
        } else {
            throw $e;
        }
    }
    
    try {
        // 添加 updated_at 字段
        $sql4 = "ALTER TABLE `fs_upload` ADD COLUMN `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `created_at`";
        Db::execute($sql4);
        echo "✓ 添加 updated_at 字段成功\n";
    } catch (\Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "- updated_at 字段已存在\n";
        } else {
            throw $e;
        }
    }
    
    // 添加索引
    try {
        $sql5 = "ALTER TABLE `fs_upload` ADD INDEX `idx_category_id` (`category_id`)";
        Db::execute($sql5);
        echo "✓ 添加 category_id 索引成功\n";
    } catch (\Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "- category_id 索引已存在\n";
        } else {
            throw $e;
        }
    }
    
    try {
        $sql6 = "ALTER TABLE `fs_upload` ADD INDEX `idx_file_type` (`file_type`)";
        Db::execute($sql6);
        echo "✓ 添加 file_type 索引成功\n";
    } catch (\Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "- file_type 索引已存在\n";
        } else {
            throw $e;
        }
    }
    
    try {
        $sql7 = "ALTER TABLE `fs_upload` ADD INDEX `idx_upload_user` (`upload_user_id`, `upload_user_type`)";
        Db::execute($sql7);
        echo "✓ 添加 upload_user 索引成功\n";
    } catch (\Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "- upload_user 索引已存在\n";
        } else {
            throw $e;
        }
    }
    
    // 插入默认分类数据
    $categories = [
        ['id' => 1, 'name' => 'logo', 'parent_id' => 0, 'sort' => 1],
        ['id' => 2, 'name' => '商品图片', 'parent_id' => 0, 'sort' => 2],
        ['id' => 3, 'name' => '清报', 'parent_id' => 0, 'sort' => 3],
        ['id' => 4, 'name' => '系统图片', 'parent_id' => 0, 'sort' => 4],
    ];
    
    foreach ($categories as $category) {
        $exists = Db::table('fs_media_category')->where('id', $category['id'])->find();
        if (!$exists) {
            Db::table('fs_media_category')->insert($category);
            echo "✓ 插入分类: {$category['name']}\n";
        } else {
            echo "- 分类已存在: {$category['name']}\n";
        }
    }
    
    // 注意：file_url字段现在由后端动态生成，不再存储在数据库中
    echo "✓ 文件URL现在由后端动态生成\n";
    
    echo "\n数据库迁移完成！\n";
    
} catch (\Exception $e) {
    echo "❌ 迁移失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}
