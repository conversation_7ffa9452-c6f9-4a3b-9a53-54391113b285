var e=Object.defineProperty,r=Object.defineProperties,a=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,o=(r,a,t)=>a in r?e(r,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[a]=t,i=(e,r,a)=>new Promise(((t,l)=>{var s=e=>{try{i(a.next(e))}catch(r){l(r)}},o=e=>{try{i(a.throw(e))}catch(r){l(r)}},i=e=>e.done?t(e.value):Promise.resolve(e.value).then(s,o);i((a=a.apply(e,r)).next())}));import{C as p}from"./index-C8nK2eTm.js";import n from"./user-add-dialog-BuoCzWmk.js";import d from"./user-edit-dialog-Cmo37nx_.js";import{U as u,A as c}from"./index-Bcx6y5fH.js";import{m,r as b,P as g,D as y,x as f,G as v,C as h,V as j,W as x,X as w,u as P,a6 as _,E as U,aI as O,p as k,aQ as C}from"./vendor-8T3zXQLl.js";import{_ as q}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-DyBuW4xE.js";import"./index-D1l9fF2S.js";import"./index-Drb3fmHv.js";/* empty css                   *//* empty css                      *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./index-BPyeCy-r.js";/* empty css                *//* empty css               *//* empty css                  *//* empty css               */import"./el-form-item-l0sNRNKZ.js";/* empty css                       *//* empty css                 */import"./index-BgthDRX_.js";import"./index-7x-iptGQ.js";/* empty css                  *//* empty css                    */const S=m((D=((e,r)=>{for(var a in r||(r={}))l.call(r,a)&&o(e,a,r[a]);if(t)for(var a of t(r))s.call(r,a)&&o(e,a,r[a]);return e})({},{name:"User"}),r(D,a({__name:"index",setup(e){const r=b(),a=b([]),t=b(!1),l=b(!1),s=b(null),o={api:{list:u.getUserList,create:u.createUser,update:u.updateUser,delete:u.deleteUser},columns:[{type:"selection"},{type:"index",label:"序号"},{prop:"avatar",label:"头像",width:80,formatter:e=>k("div",{class:"user-avatar-cell"},[e.avatar?k("img",{src:c(e.avatar),alt:"头像",class:"avatar-image",style:{width:"40px",height:"40px",objectFit:"cover",borderRadius:"50%",border:"1px solid #ddd"}}):k("div",{class:"no-avatar",style:{width:"40px",height:"40px",borderRadius:"50%",backgroundColor:"#f5f5f5",display:"flex",alignItems:"center",justifyContent:"center",color:"#999",fontSize:"12px",border:"1px solid #ddd"}},"无")])},{prop:"nickName",label:"昵称"},{prop:"userPhone",label:"电话"},{prop:"referrer",label:"推荐人"},{prop:"userStatus",label:"状态",formatter:e=>{const r={1:{type:"success",text:"正常"},2:{type:"danger",text:"禁用"},3:{type:"info",text:"注销"}}[e.userStatus]||{type:"info",text:"未知"};return k(C,{type:r.type},(()=>r.text))}},{prop:"createTime",label:"创建时间",sortable:!0}],search:{enabled:!0,defaultParams:{name:"",phone:"",status:"",level:"normal"},items:[{label:"用户名",prop:"name",type:"input",config:{clearable:!0}},{label:"手机号",prop:"phone",type:"input",config:{clearable:!0}},{label:"状态",prop:"status",type:"select",config:{clearable:!0},options:[{label:"正常",value:1},{label:"禁用",value:2},{label:"注销",value:3}]}]},actions:{enabled:!0,width:120,create:{enabled:!0,text:"新增用户",type:"primary"},edit:{enabled:!0},delete:{enabled:!0,confirmText:"确定要删除这个用户吗？"}},dialog:{enabled:!1,width:"600px",titles:{create:"新增用户",edit:"编辑用户"},formConfig:[{prop:"avatar",label:"头像",type:"upload",placeholder:"请选择头像（可选）",span:24,config:{pickerTitle:"选择用户头像",accept:"image/*",uploadText:"选择头像",multiple:!1}},{prop:"userPhone",label:"手机号",type:"input",required:!0,placeholder:"请输入手机号",span:24,rules:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}]},{prop:"nickName",label:"昵称",type:"input",required:!0,placeholder:"请输入昵称",span:24,rules:[{required:!0,message:"请输入昵称",trigger:"blur"},{min:2,max:20,message:"昵称长度必须在2-20位之间",trigger:"blur"}]},{prop:"password",label:"密码",type:"password",required:!1,placeholder:"不填则不修改",span:12,rules:[{min:6,max:32,message:"密码长度必须在6-32位之间",trigger:"blur"}]},{prop:"confirmPassword",label:"确认密码",type:"password",required:!1,placeholder:"不填则不修改",span:12,rules:[{validator:(e,r,a)=>{a()},trigger:"blur"}]},{prop:"payPassword",label:"支付密码",type:"password",required:!1,placeholder:"不填则不修改",span:12,rules:[{pattern:/^\d{6}$/,message:"支付密码必须是6位数字",trigger:"blur"}]},{prop:"confirmPayPassword",label:"确认支付密码",type:"password",required:!1,placeholder:"不填则不修改",span:12,rules:[{validator:(e,r,a)=>{a()},trigger:"blur"}]},{prop:"referrer",label:"推荐人",type:"input",placeholder:"请输入推荐人手机号或用户名（可选）",span:24}]},table:{rowKey:"id",stripe:!0,border:!1}},m=()=>{t.value=!0},q=(e,r)=>{s.value=r,l.value=!0},S=e=>i(this,null,(function*(){try{yield O.confirm("确定要删除这个用户吗？","删除确认",{type:"warning"}),yield u.deleteUser(Number(e)),U.success("删除成功")}catch(r){"cancel"!==r&&U.error("删除失败")}})),D=()=>{t.value=!1},I=()=>{l.value=!1},R=e=>{a.value=e},T=()=>i(this,null,(function*(){try{yield O.confirm(`确定要删除选中的 ${a.value.length} 个用户吗？`,"批量删除",{type:"warning"}),U.success("批量删除成功")}catch(e){"cancel"!==e&&U.error("批量删除失败")}})),$=()=>{U.success("导出功能开发中...")};return(e,a)=>(y(),g("div",null,[f(p,{ref_key:"crudListPageRef",ref:r,config:o,onCreate:m,onUpdate:q,onDelete:S,onSelectionChange:R},{"header-actions":v((({selectedRows:e})=>[e.length>0?(y(),h(P(_),{key:0,type:"danger",onClick:T},{default:v((()=>[x(" 批量删除 ("+w(e.length)+") ",1)])),_:2},1024)):j("",!0),f(P(_),{onClick:$},{default:v((()=>a[2]||(a[2]=[x(" 导出用户 ")]))),_:1,__:[2]})])),_:1},512),f(n,{visible:t.value,"onUpdate:visible":a[0]||(a[0]=e=>t.value=e),onSubmit:D},null,8,["visible"]),f(d,{visible:l.value,"onUpdate:visible":a[1]||(a[1]=e=>l.value=e),userData:s.value,onSubmit:I},null,8,["visible","userData"])]))}}))));var D;const I=q(S,[["__scopeId","data-v-dc9bd467"]]);export{I as default};
