<?php
declare(strict_types=1);

namespace app\common\controller;

use think\App;
use think\Response;

/**
 * 管理后台API基础控制器
 */
class AdminApiController extends BaseController
{
    /**
     * 当前登录的管理员信息
     * @var array
     */
    protected $admin;

    /**
     * 构造方法
     * @param App $app
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        
        // 从中间件中获取管理员信息
        $this->admin = $this->request->adminInfo ?? [];
    }

    /**
     * 获取当前管理员ID
     * @return int
     */
    protected function getAdminId(): int
    {
        return $this->admin['id'] ?? 0;
    }

    /**
     * 获取当前管理员信息
     * @return array
     */
    protected function getAdminInfo(): array
    {
        return $this->admin;
    }

    /**
     * 检查权限
     * @param string $permission 权限标识
     * @return bool
     */
    protected function checkPermission(string $permission): bool
    {
        // 超级管理员拥有所有权限
        if (isset($this->admin['is_super']) && $this->admin['is_super']) {
            return true;
        }

        // 检查用户权限
        $permissions = $this->admin['permissions'] ?? [];
        return in_array($permission, $permissions);
    }

    /**
     * 权限验证失败响应
     * @param string $msg
     * @return Response
     */
    protected function noPermission(string $msg = '权限不足'): Response
    {
        return $this->error($msg, 403);
    }

    /**
     * 参数验证失败响应
     * @param string $msg
     * @return Response
     */
    protected function validateFail(string $msg = '参数验证失败'): Response
    {
        return $this->error($msg, 422);
    }

    /**
     * 资源不存在响应
     * @param string $msg
     * @return Response
     */
    protected function notFound(string $msg = '资源不存在'): Response
    {
        return $this->error($msg, 404);
    }
}
