<template>
  <ArtSearchBar
    v-model:filter="searchFormState"
    :items="formItems"
    @reset="handleReset"
    @search="handleSearch"
  />
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { SearchChangeParams, SearchFormItem } from '@/types'

  interface Emits {
    (e: 'update:modelValue', value: any): void
    (e: 'search', params: any): void
    (e: 'reset'): void
  }

  const emit = defineEmits<Emits>()

  // 定义表单搜索初始值
  const initialSearchState = {
    username: '',
    nickname: '',
    status: ''
  }

  // 响应式表单数据 - 使用ref而不是reactive
  const searchFormState = ref({ ...initialSearchState })

  // 表单配置
  const formItems: SearchFormItem[] = [
    {
      type: 'input',
      prop: 'username',
      label: '用户名',
      placeholder: '请输入用户名'
    },
    {
      type: 'input',
      prop: 'nickname',
      label: '昵称',
      placeholder: '请输入昵称'
    },
    {
      type: 'select',
      prop: 'status',
      label: '状态',
      placeholder: '请选择状态',
      options: [
        { label: '正常', value: 1 },
        { label: '禁用', value: 2 }
      ]
    }
  ]

  // 搜索处理
  const handleSearch = () => {
    console.log('管理员搜索参数:', searchFormState.value)
    // 直接调用父组件的搜索方法，传递搜索参数
    emit('search', searchFormState.value)
  }

  // 重置处理
  const handleReset = () => {
    searchFormState.value = { ...initialSearchState }
    emit('update:modelValue', { ...initialSearchState })
    emit('reset')
  }
</script>

<style scoped>
.search-card {
  margin-bottom: 20px;
}
</style>
