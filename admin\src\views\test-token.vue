<template>
  <div class="test-token-page">
    <ElCard>
      <template #header>
        <h3>Token过期测试页面</h3>
      </template>
      
      <div class="test-buttons">
        <ElButton @click="testSingleRequest" type="primary">
          测试单个请求
        </ElButton>
        
        <ElButton @click="testMultipleRequests" type="warning">
          测试多个并发请求
        </ElButton>
        
        <ElButton @click="simulateTokenExpiry" type="danger">
          模拟Token过期
        </ElButton>
        
        <ElButton @click="clearToken" type="info">
          清空Token
        </ElButton>
      </div>
      
      <div class="test-results">
        <h4>测试结果：</h4>
        <div class="result-item" v-for="(result, index) in testResults" :key="index">
          <span class="timestamp">{{ result.timestamp }}</span>
          <span class="message">{{ result.message }}</span>
        </div>
      </div>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElCard, ElButton, ElMessage } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import request from '@/utils/http'

defineOptions({ name: 'TestToken' })

const userStore = useUserStore()
const testResults = ref<Array<{ timestamp: string; message: string }>>([])

const addResult = (message: string) => {
  testResults.value.unshift({
    timestamp: new Date().toLocaleTimeString(),
    message
  })
}

const testSingleRequest = async () => {
  addResult('开始测试单个请求...')
  try {
    await request.get({ url: '/user/list' })
    addResult('✅ 单个请求成功')
  } catch (error) {
    addResult(`❌ 单个请求失败: ${error}`)
  }
}

const testMultipleRequests = async () => {
  addResult('开始测试多个并发请求...')
  
  const requests = [
    request.get({ url: '/user/list' }),
    request.get({ url: '/role/list' }),
    request.get({ url: '/menu/list' }),
    request.get({ url: '/system/config' }),
    request.get({ url: '/admin/list' })
  ]
  
  try {
    await Promise.allSettled(requests)
    addResult('✅ 并发请求完成')
  } catch (error) {
    addResult(`❌ 并发请求失败: ${error}`)
  }
}

const simulateTokenExpiry = () => {
  addResult('模拟Token过期...')
  // 设置一个无效的token
  userStore.setToken('invalid_token_12345')
  addResult('⚠️ Token已设置为无效值，下次请求将触发401错误')
}

const clearToken = () => {
  addResult('清空Token...')
  userStore.setToken('')
  addResult('🗑️ Token已清空')
}
</script>

<style scoped>
.test-token-page {
  padding: 20px;
}

.test-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.test-results {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
}

.result-item {
  display: flex;
  margin-bottom: 8px;
  font-family: monospace;
  font-size: 12px;
}

.timestamp {
  color: #909399;
  margin-right: 10px;
  min-width: 80px;
}

.message {
  color: #303133;
}
</style>
