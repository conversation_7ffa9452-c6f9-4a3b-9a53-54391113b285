var e=Object.defineProperty,l=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,t=(l,a,r)=>a in l?e(l,a,{enumerable:!0,configurable:!0,writable:!0,value:r}):l[a]=r,o=(e,o)=>{for(var p in o||(o={}))a.call(o,p)&&t(e,p,o[p]);if(l)for(var p of l(o))r.call(o,p)&&t(e,p,o[p]);return e};import{_ as p}from"./index-B5FHwYQA.js";import{m as n,r as i,C as s,D as m,i as u,u as c}from"./vendor-8T3zXQLl.js";import"./index-1JOyfOxu.js";/* empty css                *//* empty css               *//* empty css                  *//* empty css               */import"./el-form-item-l0sNRNKZ.js";import"./index-Wowen7jt.js";/* empty css                 *//* empty css                  *//* empty css                     *//* empty css                       *//* empty css                 */import"./_plugin-vue_export-helper-BCo6x5W8.js";const b=n({__name:"user-search",emits:["update:modelValue","search","reset"],setup(e,{emit:l}){const a=l,r={name:"",phone:"",address:"",level:"normal",email:"",date:"2025-01-05",daterange:["2025-01-01","2025-02-10"],status:"1"},t=i(o({},r)),n=()=>{a("update:modelValue",o({},r)),a("reset")},b=()=>{a("search")},d=e=>{},f=[{label:"用户名",prop:"name",type:"input",config:{clearable:!0},onChange:d},{label:"电话",prop:"phone",type:"input",config:{clearable:!0},onChange:d},{label:"用户等级",prop:"level",type:"select",config:{clearable:!0},options:()=>[{label:"普通用户",value:"normal"},{label:"VIP用户",value:"vip"},{label:"高级VIP",value:"svip"},{label:"企业用户",value:"enterprise",disabled:!0}],onChange:d},{label:"地址",prop:"address",type:"input",config:{clearable:!0},onChange:d},{label:"邮箱",prop:"email",type:"input",config:{clearable:!0},onChange:d},{prop:"date",label:"日期",type:"date",config:{type:"date",placeholder:"请选择日期"}},{prop:"daterange",label:"日期范围",type:"daterange",config:{type:"daterange",startPlaceholder:"开始时间",endPlaceholder:"结束时间"}},{label:"状态",prop:"status",type:"radio",options:[{label:"在线",value:"1"},{label:"离线",value:"2"}],onChange:d}];return(e,l)=>{const a=p;return m(),s(a,{filter:c(t),"onUpdate:filter":l[0]||(l[0]=e=>u(t)?t.value=e:null),items:f,onReset:n,onSearch:b},null,8,["filter"])}}});export{b as default};
