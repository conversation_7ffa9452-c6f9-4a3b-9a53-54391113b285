var e=Object.defineProperty,l=Object.defineProperties,a=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,s=(l,a,o)=>a in l?e(l,a,{enumerable:!0,configurable:!0,writable:!0,value:o}):l[a]=o,r=(e,l)=>{for(var a in l||(l={}))t.call(l,a)&&s(e,a,l[a]);if(o)for(var a of o(l))i.call(l,a)&&s(e,a,l[a]);return e},p=(e,o)=>l(e,a(o)),n=(e,l,a)=>new Promise(((o,t)=>{var i=e=>{try{r(a.next(e))}catch(l){t(l)}},s=e=>{try{r(a.throw(e))}catch(l){t(l)}},r=e=>e.done?o(e.value):Promise.resolve(e.value).then(i,s);r((a=a.apply(e,l)).next())}));import{c as d,A as u,V as c}from"./index-Bcx6y5fH.js";/* empty css                   *//* empty css                *//* empty css                 *//* empty css                        *//* empty css                  */import"./el-form-item-l0sNRNKZ.js";import{m as _,r as m,M as g,o as f,P as v,D as x,a5 as h,x as y,aJ as w,C as b,G as V,W as j,bf as z,a3 as k,a2 as C,R as O,V as P,u as U,aa as L,b2 as M,a6 as B,aq as I,a8 as S,E as q,aI as E}from"./vendor-8T3zXQLl.js";import{M as R}from"./index-7x-iptGQ.js";import{_ as D}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                  *//* empty css                      *//* empty css                  *//* empty css                     *//* empty css                    */const G={class:"page-content"},J={class:"upload-container"},T={class:"image-preview-container"},A=["src"],F={key:1,class:"placeholder"},H={key:0,class:"image-actions"},K={class:"upload-container"},W={class:"image-preview-container"},Z=["src"],N={key:1,class:"placeholder"},Q={key:0,class:"image-actions"},X=D(_(p(r({},{name:"SystemSettings"}),{__name:"index",setup(e){const l=m(),a=m(!1),o=m(!1),t=d(),i=m(!1),s=m("logo"),_=g({site_name:"",site_logo:"",site_description:"",site_keywords:"",site_favicon:"",site_copyright:"",site_icp:"",upload_max_size:10,upload_allowed_ext:"jpg,jpeg,png,gif,webp,pdf,doc,docx,xls,xlsx",logo_size:48}),D=g({site_name:[{required:!0,message:"请输入网站名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],upload_max_size:[{required:!0,message:"请设置最大上传大小",trigger:"blur"}]}),X=()=>n(this,null,(function*(){a.value=!0;try{const e=yield c.getConfig();e&&(Object.keys(_).forEach((e=>{"upload_max_size"!==e&&"logo_size"!==e&&(_[e]="")})),Object.assign(_,e),e.upload_max_size&&(_.upload_max_size=Math.round(e.upload_max_size/1024/1024)),e.logo_size||(_.logo_size=48),t.updateSystemConfig(_))}catch(e){q.error("获取配置失败")}finally{a.value=!1}})),Y=e=>{s.value=e,i.value=!0},$=e=>{"logo"===e?_.site_logo="":_.site_favicon=""},ee=e=>{if(e.length>0){const l=e[0];"logo"===s.value?(_.site_logo=l.file_path,q.success("Logo设置成功")):(_.site_favicon=l.file_path,q.success("网站图标设置成功"))}},le=()=>n(this,null,(function*(){if(l.value)try{yield l.value.validate(),o.value=!0;const e=p(r({},_),{upload_max_size:1024*(_.upload_max_size||10)*1024});yield c.setConfig(e),t.updateSystemConfig(_),q.success("保存成功")}catch(e){!1!==e&&q.error("保存失败")}finally{o.value=!1}})),ae=()=>{E.confirm("确定要重置表单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{var e;null==(e=l.value)||e.resetFields(),X()}))};return f((()=>{X()})),(e,t)=>{const r=z,p=k,n=C,d=L,c=B,m=I,g=S,f=w;return x(),v("div",G,[h((x(),b(g,{ref_key:"formRef",ref:l,model:_,rules:D,"label-width":"120px"},{default:V((()=>[y(r,{"content-position":"left"},{default:V((()=>t[13]||(t[13]=[j("基础信息")]))),_:1,__:[13]}),y(n,{label:"网站名称",prop:"site_name"},{default:V((()=>[y(p,{modelValue:_.site_name,"onUpdate:modelValue":t[0]||(t[0]=e=>_.site_name=e),placeholder:"请输入网站名称",maxlength:"50","show-word-limit":"",style:{width:"400px"}},null,8,["modelValue"])])),_:1}),y(n,{label:"网站描述",prop:"site_description"},{default:V((()=>[y(p,{modelValue:_.site_description,"onUpdate:modelValue":t[1]||(t[1]=e=>_.site_description=e),type:"textarea",rows:3,placeholder:"请输入网站描述",maxlength:"200","show-word-limit":"",style:{width:"500px"}},null,8,["modelValue"])])),_:1}),y(n,{label:"网站关键词",prop:"site_keywords"},{default:V((()=>[y(p,{modelValue:_.site_keywords,"onUpdate:modelValue":t[2]||(t[2]=e=>_.site_keywords=e),placeholder:"请输入网站关键词，多个关键词用逗号分隔",maxlength:"100","show-word-limit":"",style:{width:"450px"}},null,8,["modelValue"])])),_:1}),y(n,{label:"网站Logo",prop:"site_logo"},{default:V((()=>[O("div",J,[O("div",T,[O("div",{class:"image-preview",onClick:t[3]||(t[3]=e=>Y("logo"))},[_.site_logo?(x(),v("img",{key:0,src:U(u)(_.site_logo),class:"logo"},null,8,A)):(x(),v("div",F,[y(d,{class:"placeholder-icon"},{default:V((()=>[y(U(M))])),_:1})]))]),_.site_logo?(x(),v("div",H,[y(c,{size:"small",type:"danger",onClick:t[4]||(t[4]=e=>$("logo"))},{default:V((()=>t[14]||(t[14]=[j("清除")]))),_:1,__:[14]})])):P("",!0)]),t[15]||(t[15]=O("div",{class:"upload-tip"},[O("p",null,"建议尺寸：200x60px，支持jpg、png格式，大小不超过2MB")],-1))])])),_:1}),y(n,{label:"网站图标",prop:"site_favicon"},{default:V((()=>[O("div",K,[O("div",W,[O("div",{class:"image-preview favicon-preview",onClick:t[5]||(t[5]=e=>Y("favicon"))},[_.site_favicon?(x(),v("img",{key:0,src:U(u)(_.site_favicon),class:"favicon"},null,8,Z)):(x(),v("div",N,[y(d,{class:"placeholder-icon"},{default:V((()=>[y(U(M))])),_:1})]))]),_.site_favicon?(x(),v("div",Q,[y(c,{size:"small",type:"danger",onClick:t[6]||(t[6]=e=>$("favicon"))},{default:V((()=>t[16]||(t[16]=[j("清除")]))),_:1,__:[16]})])):P("",!0)]),t[17]||(t[17]=O("div",{class:"upload-tip"},[O("p",null,"建议尺寸：32x32px，支持ico、png格式，大小不超过1MB")],-1))])])),_:1}),y(r,{"content-position":"left"},{default:V((()=>t[18]||(t[18]=[j("其他设置")]))),_:1,__:[18]}),y(n,{label:"版权信息",prop:"site_copyright"},{default:V((()=>[y(p,{modelValue:_.site_copyright,"onUpdate:modelValue":t[7]||(t[7]=e=>_.site_copyright=e),placeholder:"请输入版权信息",maxlength:"100","show-word-limit":"",style:{width:"450px"}},null,8,["modelValue"])])),_:1}),y(n,{label:"ICP备案号",prop:"site_icp"},{default:V((()=>[y(p,{modelValue:_.site_icp,"onUpdate:modelValue":t[8]||(t[8]=e=>_.site_icp=e),placeholder:"请输入ICP备案号",maxlength:"50",style:{width:"300px"}},null,8,["modelValue"])])),_:1}),y(n,{label:"Logo尺寸",prop:"logo_size"},{default:V((()=>[y(m,{modelValue:_.logo_size,"onUpdate:modelValue":t[9]||(t[9]=e=>_.logo_size=e),min:24,max:100,step:4,"controls-position":"right",placeholder:"Logo显示尺寸（像素）",style:{width:"200px"}},null,8,["modelValue"]),t[19]||(t[19]=O("span",{style:{"margin-left":"10px",color:"#999"}},"像素（建议24-100px）",-1))])),_:1,__:[19]}),y(r,{"content-position":"left"},{default:V((()=>t[20]||(t[20]=[j("上传设置")]))),_:1,__:[20]}),y(n,{label:"最大上传大小",prop:"upload_max_size"},{default:V((()=>[y(m,{modelValue:_.upload_max_size,"onUpdate:modelValue":t[10]||(t[10]=e=>_.upload_max_size=e),min:1,max:100,step:1,"controls-position":"right",style:{width:"200px"}},null,8,["modelValue"]),t[21]||(t[21]=O("span",{class:"input-suffix"},"MB",-1))])),_:1,__:[21]}),y(n,{label:"允许上传类型",prop:"upload_allowed_ext"},{default:V((()=>[y(p,{modelValue:_.upload_allowed_ext,"onUpdate:modelValue":t[11]||(t[11]=e=>_.upload_allowed_ext=e),placeholder:"请输入允许上传的文件扩展名，用逗号分隔",maxlength:"200",style:{width:"500px"}},null,8,["modelValue"])])),_:1}),y(n,null,{default:V((()=>[y(c,{type:"primary",onClick:le,loading:o.value},{default:V((()=>t[22]||(t[22]=[j(" 保存设置 ")]))),_:1,__:[22]},8,["loading"]),y(c,{onClick:ae},{default:V((()=>t[23]||(t[23]=[j("重置")]))),_:1,__:[23]})])),_:1})])),_:1},8,["model","rules"])),[[f,a.value]]),y(R,{modelValue:i.value,"onUpdate:modelValue":t[12]||(t[12]=e=>i.value=e),title:"logo"===s.value?"选择网站Logo":"选择网站图标",multiple:!1,accept:"image/*","max-size":5,"default-category":4,width:"60%",onConfirm:ee},null,8,["modelValue","title"])])}}})),[["__scopeId","data-v-4e5b55de"]]);export{X as default};
