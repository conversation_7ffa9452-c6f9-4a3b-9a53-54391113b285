-- 清理数据库中的完整URL，只保留相对路径
-- 执行前请备份数据库！

-- 1. 清理 fs_upload 表的 file_url 字段
-- 将完整URL转换为相对路径，或者直接清空（因为现在由后端动态生成）
UPDATE `fs_upload` 
SET `file_url` = NULL 
WHERE `file_url` IS NOT NULL;

-- 如果你想保留相对路径而不是清空，可以使用下面的语句：
-- UPDATE `fs_upload` 
-- SET `file_url` = REPLACE(REPLACE(REPLACE(`file_url`, 'http://localhost:8000/', ''), 'http://fanshop.gg/', ''), 'https://fanshop.gg/', '')
-- WHERE `file_url` LIKE 'http%';

-- 2. 清理 fs_system_config 表中的 site_logo 字段
-- 将完整URL转换为相对路径
UPDATE `fs_system_config` 
SET `config_value` = REPLACE(REPLACE(REPLACE(`config_value`, 'http://localhost:8000/', ''), 'http://fanshop.gg/', ''), 'https://fanshop.gg/', '')
WHERE `config_key` = 'site_logo' AND `config_value` LIKE 'http%';

-- 3. 清理 fs_system_config 表中的 site_favicon 字段
UPDATE `fs_system_config` 
SET `config_value` = REPLACE(REPLACE(REPLACE(`config_value`, 'http://localhost:8000/', ''), 'http://fanshop.gg/', ''), 'https://fanshop.gg/', '')
WHERE `config_key` = 'site_favicon' AND `config_value` LIKE 'http%';

-- 4. 清理用户头像字段（如果有的话）
UPDATE `fs_admin` 
SET `avatar` = REPLACE(REPLACE(REPLACE(`avatar`, 'http://localhost:8000/', ''), 'http://fanshop.gg/', ''), 'https://fanshop.gg/', '')
WHERE `avatar` LIKE 'http%';

-- 5. 清理其他可能包含完整URL的字段
-- 根据实际情况添加更多清理语句

-- 查看清理结果
SELECT 'fs_upload表清理结果' as '表名', COUNT(*) as '总记录数', 
       SUM(CASE WHEN file_url IS NULL THEN 1 ELSE 0 END) as '已清理记录数'
FROM `fs_upload`;

SELECT 'fs_system_config表清理结果' as '表名', config_key, config_value 
FROM `fs_system_config` 
WHERE config_key IN ('site_logo', 'site_favicon');

SELECT 'fs_admin表清理结果' as '表名', COUNT(*) as '总记录数',
       SUM(CASE WHEN avatar NOT LIKE 'http%' OR avatar IS NULL THEN 1 ELSE 0 END) as '已清理记录数'
FROM `fs_admin`;
