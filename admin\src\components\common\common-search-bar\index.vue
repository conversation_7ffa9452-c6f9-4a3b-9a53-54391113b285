<!-- 通用搜索栏组件 -->
<template>
  <div class="common-search-bar">
    <ArtSearchBar
      v-model:filter="internalFilter"
      :items="computedItems"
      :label-width="config?.labelWidth || '80px'"
      :gutter="config?.gutter || 20"
      :el-col-span="config?.span || 6"
      @reset="handleReset"
      @search="handleSearch"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import ArtSearchBar from '@/components/core/forms/art-search-bar/index.vue'
import type { SearchFormItem } from '@/types'

interface SearchConfig {
  labelWidth?: string
  gutter?: number
  span?: number
}

interface Props {
  filter: Record<string, any>
  items: SearchFormItem[]
  config?: SearchConfig
  defaultValues?: Record<string, any>
}

interface Emits {
  (e: 'update:filter', value: Record<string, any>): void
  (e: 'search', params: Record<string, any>): void
  (e: 'reset'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 内部过滤器状态
const internalFilter = ref({ ...props.filter })

// 计算搜索项配置
const computedItems = computed(() => {
  return props.items.map(item => ({
    ...item,
    onChange: (params: any) => {
      // 触发搜索项变更事件
      if (item.onChange) {
        item.onChange(params)
      }
      // 实时更新过滤器
      emit('update:filter', internalFilter.value)
    }
  }))
})

// 监听外部filter变化
watch(
  () => props.filter,
  (newFilter) => {
    internalFilter.value = { ...newFilter }
  },
  { deep: true }
)

// 监听内部filter变化
watch(
  internalFilter,
  (newFilter) => {
    emit('update:filter', newFilter)
  },
  { deep: true }
)

// 重置处理
const handleReset = () => {
  const defaultValues = props.defaultValues || {}
  internalFilter.value = { ...defaultValues }
  emit('update:filter', internalFilter.value)
  emit('reset')
}

// 搜索处理
const handleSearch = () => {
  emit('search', internalFilter.value)
}

// 暴露方法
defineExpose({
  reset: handleReset,
  search: handleSearch,
  getFilter: () => internalFilter.value
})
</script>

<style scoped>
.common-search-bar {
  /* 移除 margin-bottom，由父组件的 gap 控制间距 */
}
</style>
