# MediaPicker 媒体文件选择器

一个功能完整的媒体文件选择器组件，支持图片上传、分类管理、文件选择等功能。

## 功能特性

- 🖼️ **图片预览**：支持图片文件的预览显示
- 📁 **分类管理**：支持文件分类，可创建、删除分类
- 🔍 **搜索功能**：支持按文件名搜索
- 📤 **文件上传**：支持拖拽上传和点击上传
- 🗑️ **删除功能**：支持单个删除和批量删除
- ✅ **多选支持**：可配置单选或多选模式
- 📱 **响应式设计**：适配不同屏幕尺寸

## 基本用法

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <el-button @click="showPicker = true">选择图片</el-button>
    
    <!-- 媒体选择器 -->
    <MediaPicker
      v-model="showPicker"
      @confirm="handleConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import MediaPicker from '@/components/MediaPicker/index.vue'
import type { MediaFile } from '@/api/mediaApi'

const showPicker = ref(false)

const handleConfirm = (files: MediaFile[]) => {
  console.log('选中的文件:', files)
  // 处理选中的文件
}
</script>
```

## 高级用法

### 多选模式

```vue
<MediaPicker
  v-model="showPicker"
  :multiple="true"
  @confirm="handleMultipleConfirm"
/>
```

### 自定义配置

```vue
<MediaPicker
  v-model="showPicker"
  title="选择商品图片"
  width="80%"
  :multiple="true"
  accept="image/jpeg,image/png"
  :max-size="5"
  :default-category="2"
  @confirm="handleConfirm"
/>
```

### 在表单中使用

```vue
<template>
  <el-form>
    <el-form-item label="商品图片">
      <div class="image-upload">
        <div v-if="selectedImage" class="image-preview">
          <img :src="selectedImage.file_url" alt="预览图" />
          <el-button size="small" @click="removeImage">移除</el-button>
        </div>
        <el-button v-else @click="showPicker = true">选择图片</el-button>
      </div>
    </el-form-item>
  </el-form>

  <MediaPicker
    v-model="showPicker"
    title="选择商品图片"
    :default-category="2"
    @confirm="handleImageSelect"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import MediaPicker from '@/components/MediaPicker/index.vue'
import type { MediaFile } from '@/api/mediaApi'

const showPicker = ref(false)
const selectedImage = ref<MediaFile | null>(null)

const handleImageSelect = (files: MediaFile[]) => {
  if (files.length > 0) {
    selectedImage.value = files[0]
  }
}

const removeImage = () => {
  selectedImage.value = null
}
</script>
```

## Props 属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | boolean | - | 控制组件显示/隐藏 |
| multiple | boolean | false | 是否支持多选 |
| accept | string | 'image/*' | 接受的文件类型 |
| maxSize | number | 10 | 最大文件大小(MB) |
| title | string | '选择图片' | 对话框标题 |
| width | string | '60%' | 对话框宽度 |
| defaultCategory | number | 0 | 默认选中的分类ID |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | (value: boolean) | 更新显示状态 |
| confirm | (files: MediaFile[]) | 确认选择文件时触发 |

## MediaFile 类型

```typescript
interface MediaFile {
  id: number
  original_name: string
  file_name: string
  file_path: string
  file_url: string
  file_size: number
  file_type: string
  mime_type: string
  category_id: number
  is_image: boolean
  file_size_formatted: string
  created_at: string
}
```

## 样式定制

组件使用了 `media-picker-dialog` 类名，可以通过CSS进行样式定制：

```css
.media-picker-dialog .el-dialog__body {
  padding: 0;
}

.media-picker-dialog .media-picker {
  height: 500px; /* 自定义高度 */
}
```

## 注意事项

1. 确保后端API已正确配置媒体文件管理接口
2. 组件依赖Element Plus，确保已正确安装和配置
3. 文件上传需要配置正确的上传接口和存储路径
4. 建议在使用前先创建好文件分类
