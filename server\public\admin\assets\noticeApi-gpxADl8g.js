var t=(t,e,r)=>new Promise(((n,i)=>{var o=t=>{try{a(r.next(t))}catch(e){i(e)}},u=t=>{try{a(r.throw(t))}catch(e){i(e)}},a=t=>t.done?n(t.value):Promise.resolve(t.value).then(o,u);a((r=r.apply(t,e)).next())}));import{E as e}from"./index-1JOyfOxu.js";class r{static getNoticeList(r){return t(this,null,(function*(){return e.get({url:"/notice/list",params:r})}))}static createNotice(r){return t(this,null,(function*(){return e.post({url:"/notice/create",data:r})}))}static updateNotice(r,n){return t(this,null,(function*(){if(!r||r<=0)throw new Error("无效的公告ID");return e.put({url:`/notice/update/${r}`,data:n})}))}static getNoticeDetail(r){return t(this,null,(function*(){if(!r||r<=0)throw new Error("无效的公告ID");return e.get({url:`/notice/detail/${r}`})}))}static deleteNotice(r){return t(this,null,(function*(){if(!r||r<=0)throw new Error("无效的公告ID");return e.del({url:`/notice/delete/${r}`})}))}}export{r as N};
