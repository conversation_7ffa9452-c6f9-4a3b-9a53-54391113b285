<?php
declare(strict_types=1);

namespace app\common\service;

use app\common\model\Category;

class CategoryService
{
    /**
     * 获取列表
     */
    public static function getList(array $params = []): array
    {
        $page = $params['current'] ?? 1;
        $limit = $params['size'] ?? 20;

        $query = Category::order('sort desc, id desc');

        // 搜索条件
        if (!empty($params['name'])) {
            $query->whereLike('name', '%' . $params['name'] . '%');
        }

        if (isset($params['status']) && $params['status'] !== '') {
            $query->where('status', $params['status']);
        }

        $result = $query->paginate([
            'list_rows' => $limit,
            'page' => $page,
        ]);

        // 手动计算文章数量
        $items = $result->items();
        foreach ($items as $item) {
            $item->article_count = \app\common\model\Article::where('category_id', $item->id)
                ->where('status', 1)
                ->count();
        }

        return [
            'list' => $items,
            'total' => $result->total(),
            'current' => $page,
            'size' => $limit,
        ];
    }

    /**
     * 创建
     */
    public static function create(array $data): Category
    {
        // 为数据库中的必需字段设置默认值
        $data['slug'] = $data['slug'] ?? '';
        $data['description'] = $data['description'] ?? '';

        return Category::create($data);
    }

    /**
     * 更新
     */
    public static function update(int $id, array $data): bool
    {
        $category = Category::find($id);
        if (!$category) {
            throw new \Exception('分类不存在');
        }
        
        return $category->save($data);
    }
}
