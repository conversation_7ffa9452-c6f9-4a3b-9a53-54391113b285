<?php
declare(strict_types=1);

namespace app\adminapi\controller;

use app\common\service\AuthService;

/**
 * 独立登录控制器 - 不继承任何基类
 */
class Login
{
    public function index()
    {
        try {
            $request = request();
            $data = $request->post();
            
            $userName = $data['userName'] ?? '';
            $password = $data['password'] ?? '';
            
            if (empty($userName) || empty($password)) {
                return json([
                    'code' => 400,
                    'msg' => '用户名和密码不能为空',
                    'data' => []
                ]);
            }
            
            // 使用AuthService进行登录
            $result = AuthService::adminLogin($userName, $password);
            
            return json([
                'code' => 200,
                'msg' => '登录成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => $e->getMessage(),
                'data' => []
            ]);
        }
    }
}
