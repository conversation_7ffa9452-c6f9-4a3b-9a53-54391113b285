<?php
// JWT配置文件
return [
    // JWT密钥
    'key' => env('JWT_KEY', 'fanshop_secret_key_2025_very_secure'),
    
    // JWT算法
    'alg' => 'HS256',
    
    // Token过期时间（秒）
    'expire' => env('JWT_EXPIRE', 7200), // 默认2小时
    
    // 刷新Token过期时间（秒）
    'refresh_expire' => env('JWT_REFRESH_EXPIRE', 604800), // 默认7天
    
    // Token发行者
    'iss' => 'fanshop',
    
    // Token受众
    'aud' => 'fanshop-admin',
    
    // 允许的时钟偏差（秒）
    'leeway' => 60,
    
    // Token黑名单缓存前缀
    'blacklist_prefix' => 'jwt_blacklist:',
    
    // Token黑名单缓存时间（秒）
    'blacklist_expire' => 86400, // 24小时
];
