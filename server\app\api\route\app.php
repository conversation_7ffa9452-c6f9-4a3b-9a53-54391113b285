<?php
use think\facade\Route;

// 移动端API路由

// 用户认证
Route::group('auth', function () {
    Route::post('login', 'Auth/login');
    Route::post('register', 'Auth/register');
    Route::post('logout', 'Auth/logout')->middleware(['api_auth']);
    Route::post('refresh', 'Auth/refresh');
});

// 用户相关
Route::group('user', function () {
    Route::get('profile', 'User/profile');
    Route::post('update', 'User/update');
    Route::post('avatar', 'User/avatar');
})->middleware(['api_auth']);

// 公共接口
Route::group('common', function () {
    Route::get('config', 'Common/config');
    Route::post('feedback', 'Common/feedback');
});
