<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
      label-position="right"
    >
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="用户名" prop="username">
            <ElInput
              v-model="formData.username"
              placeholder="请输入用户名"
              clearable
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="昵称" prop="nickname">
            <ElInput
              v-model="formData.nickname"
              placeholder="请输入昵称"
              clearable
            />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="邮箱" prop="email">
            <ElInput
              v-model="formData.email"
              placeholder="请输入邮箱"
              clearable
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="手机号" prop="phone">
            <ElInput
              v-model="formData.phone"
              placeholder="请输入手机号"
              clearable
            />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="密码" prop="password">
            <ElInput
              v-model="formData.password"
              type="password"
              :placeholder="type === 'add' ? '请输入密码' : '留空则不修改密码'"
              show-password
              clearable
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="状态" prop="status">
            <ElSelect v-model="formData.status" placeholder="请选择状态" style="width: 100%">
              <ElOption label="正常" :value="1" />
              <ElOption label="禁用" :value="2" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElFormItem label="分配角色" prop="roleIds">
        <ElSelect
          v-model="formData.roleIds"
          multiple
          placeholder="请选择角色"
          style="width: 100%"
          clearable
        >
          <ElOption
            v-for="role in roleOptions"
            :key="role.id"
            :label="role.name"
            :value="role.id"
          />
        </ElSelect>
      </ElFormItem>

      <ElFormItem label="头像" prop="avatar">
        <ElInput
          v-model="formData.avatar"
          placeholder="请输入头像URL"
          clearable
        />
      </ElFormItem>
    </ElForm>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleClose">取消</ElButton>
        <ElButton type="primary" :loading="submitLoading" @click="handleSubmit">
          确定
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch, onMounted } from 'vue'
  import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
  import { AdminService, type AdminInfo, type AdminFormData, type RoleOption } from '@/api/adminApi'

  // 定义 props
  interface Props {
    visible: boolean
    type: 'add' | 'edit'
    adminData?: AdminInfo | null
  }

  const props = withDefaults(defineProps<Props>(), {
    adminData: null
  })

  // 定义事件
  const emit = defineEmits<{
    'update:visible': [visible: boolean]
    submit: []
  }>()

  // 响应式数据
  const formRef = ref<FormInstance>()
  const submitLoading = ref(false)
  const roleOptions = ref<RoleOption[]>([])

  // 表单数据
  const formData = reactive<AdminFormData>({
    username: '',
    password: '',
    nickname: '',
    email: '',
    phone: '',
    avatar: '',
    status: 1,
    roleIds: []
  })

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })

  const dialogTitle = computed(() => {
    return props.type === 'add' ? '新增管理员' : '编辑管理员'
  })

  // 表单验证规则
  const formRules: FormRules = {
    username: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
    ],
    password: [
      {
        validator: (rule: any, value: string, callback: any) => {
          // 编辑模式下，如果密码为空则跳过验证
          if (props.type === 'edit' && (!value || value.trim() === '')) {
            callback()
            return
          }

          // 新增模式下，密码不能为空
          if (props.type === 'add' && (!value || value.trim() === '')) {
            callback(new Error('请输入密码'))
            return
          }

          // 如果有值，则验证长度
          if (value && value.trim() && (value.length < 6 || value.length > 32)) {
            callback(new Error('密码长度在 6 到 32 个字符'))
            return
          }

          callback()
        },
        trigger: 'blur'
      }
    ],
    nickname: [
      { min: 2, max: 50, message: '昵称长度在 2 到 50 个字符', trigger: 'blur' }
    ],
    email: [
      { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
    ],
    status: [
      { required: true, message: '请选择状态', trigger: 'change' }
    ]
  }

  // 监听弹窗显示状态
  watch(() => props.visible, (visible) => {
    if (visible) {
      resetForm()
      if (props.type === 'edit' && props.adminData) {
        // 编辑模式，回填数据
        Object.assign(formData, {
          username: props.adminData.username,
          nickname: props.adminData.nickname,
          email: props.adminData.email,
          phone: props.adminData.phone,
          avatar: props.adminData.avatar,
          status: props.adminData.status,
          roleIds: props.adminData.roles.map(role => role.id),
          password: '' // 编辑时密码为空
        })
      }
    }
  })

  // 重置表单
  const resetForm = () => {
    Object.assign(formData, {
      username: '',
      password: '',
      nickname: '',
      email: '',
      phone: '',
      avatar: '',
      status: 1,
      roleIds: []
    })
    formRef.value?.clearValidate()
  }

  // 关闭弹窗
  const handleClose = () => {
    dialogVisible.value = false
  }

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      submitLoading.value = true

      if (props.type === 'add') {
        await AdminService.createAdmin(formData)
        ElMessage.success('创建成功')
      } else {
        const updateData = { ...formData }
        // 如果密码为空，则不更新密码
        if (!updateData.password || updateData.password.trim() === '') {
          delete updateData.password
        }
        await AdminService.updateAdmin(props.adminData!.id, updateData)
        ElMessage.success('更新成功')
      }

      emit('submit')
      handleClose()
    } catch (error: any) {
      console.error('提交失败:', error)
      if (error.response?.data?.message) {
        ElMessage.error(`提交失败: ${error.response.data.message}`)
      } else if (error.message) {
        ElMessage.error(`提交失败: ${error.message}`)
      } else {
        ElMessage.error('提交失败')
      }
    } finally {
      submitLoading.value = false
    }
  }

  // 获取角色列表
  const getRoleOptions = async () => {
    try {
      roleOptions.value = await AdminService.getRoles()
    } catch (error) {
      console.error('获取角色列表失败:', error)
      ElMessage.error('获取角色列表失败')
    }
  }

  onMounted(() => {
    getRoleOptions()
  })
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
