import request from '@/utils/http'

// 管理员相关接口类型定义
export interface AdminInfo {
  id: number
  username: string
  nickname: string
  email: string
  phone: string
  avatar: string
  status: number
  statusText: string
  roles: Array<{
    id: number
    name: string
    code: string
  }>
  lastLoginTime: string
  lastLoginIp: string
  createdAt: string
  updatedAt: string
}

export interface AdminListParams {
  current?: number
  size?: number
  username?: string
  nickname?: string
  status?: string | number
}

export interface AdminListResponse {
  records: AdminInfo[]
  total: number
  current: number
  size: number
}

export interface AdminFormData {
  username: string
  password?: string
  nickname?: string
  email?: string
  phone?: string
  avatar?: string
  status?: number
  roleIds?: number[]
}

export interface RoleOption {
  id: number
  name: string
  code: string
  description: string
}

// 管理员服务类
export class AdminService {
  // 获取管理员列表
  static getAdminList(params: AdminListParams) {
    return request.get<AdminListResponse>({
      url: '/admin/list',
      params
    })
  }

  // 创建管理员
  static createAdmin(data: AdminFormData) {
    return request.post<AdminInfo>({
      url: '/admin/create',
      data
    })
  }

  // 更新管理员
  static updateAdmin(id: number, data: Partial<AdminFormData>) {
    return request.put<AdminInfo>({
      url: `/admin/update/${id}`,
      data
    })
  }

  // 删除管理员
  static deleteAdmin(id: number) {
    return request.del({
      url: `/admin/delete/${id}`
    })
  }

  // 获取角色列表（用于分配角色）
  static getRoles() {
    return request.get<RoleOption[]>({
      url: '/admin/roles'
    })
  }

  // 重置密码
  static resetPassword(id: number, password: string) {
    return request.post({
      url: `/admin/reset-password/${id}`,
      data: { password }
    })
  }

  // 切换状态
  static toggleStatus(id: number) {
    return request.post({
      url: `/admin/toggle-status/${id}`
    })
  }
}
