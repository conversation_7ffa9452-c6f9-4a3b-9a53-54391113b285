<?php
declare(strict_types=1);

namespace app\index\controller;

use app\common\controller\BaseController;

/**
 * 前台首页控制器
 */
class Index extends BaseController
{
    /**
     * 首页
     */
    public function index()
    {
        $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>FanShop API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .status { color: #28a745; font-weight: bold; }
        .api-list { margin: 20px 0; }
        .api-item { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛍️ FanShop API</h1>
            <p>电商管理系统API服务</p>
            <p class="status">✅ 服务运行正常</p>
        </div>

        <div class="api-list">
            <div class="api-item">
                <h3>🔐 后台管理API</h3>
                <p>管理员登录、用户管理、角色权限、菜单管理等</p>
                <p><strong>地址:</strong> /adminapi</p>
            </div>

            <div class="api-item">
                <h3>📱 移动端API</h3>
                <p>用户注册登录、个人资料管理等</p>
                <p><strong>地址:</strong> /api</p>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="/test_api.html" class="btn">📋 API测试页面</a>
            <a href="/docs" class="btn">📖 API文档</a>
        </div>

        <div style="text-align: center; margin-top: 20px; color: #666;">
            <p>版本: v1.0.0 | 时间: ' . date('Y-m-d H:i:s') . '</p>
        </div>
    </div>
</body>
</html>';

        return response($html)->contentType('text/html; charset=utf-8');
    }

    /**
     * API文档
     */
    public function docs()
    {
        $html = '
        <!DOCTYPE html>
        <html>
        <head>
            <title>FanShop API 文档</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
                .header { background: #f4f4f4; padding: 20px; border-radius: 5px; margin-bottom: 30px; }
                .api-group { margin: 20px 0; }
                .api-item { background: #f9f9f9; padding: 15px; margin: 10px 0; border-left: 4px solid #007cba; }
                .method { display: inline-block; padding: 2px 8px; border-radius: 3px; color: white; font-size: 12px; }
                .get { background: #61affe; }
                .post { background: #49cc90; }
                .put { background: #fca130; }
                .delete { background: #f93e3e; }
                code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🛍️ FanShop API 文档</h1>
                <p>基于 ThinkPHP 8 + Art Design Pro 的电商管理系统</p>
                <p><strong>服务状态:</strong> <span style="color: green;">运行中</span></p>
                <p><strong>API版本:</strong> v1.0.0</p>
            </div>

            <div class="api-group">
                <h2>🔐 后台管理API (/adminapi)</h2>
                
                <div class="api-item">
                    <h3>认证相关</h3>
                    <p><span class="method post">POST</span> <code>/adminapi/auth/login</code> - 管理员登录</p>
                    <p><span class="method get">GET</span> <code>/adminapi/auth/info</code> - 获取用户信息</p>
                    <p><span class="method post">POST</span> <code>/adminapi/auth/logout</code> - 退出登录</p>
                </div>

                <div class="api-item">
                    <h3>用户管理</h3>
                    <p><span class="method get">GET</span> <code>/adminapi/user/list</code> - 获取用户列表</p>
                    <p><span class="method post">POST</span> <code>/adminapi/user/create</code> - 创建用户</p>
                    <p><span class="method put">PUT</span> <code>/adminapi/user/update/:id</code> - 更新用户</p>
                    <p><span class="method delete">DELETE</span> <code>/adminapi/user/delete/:id</code> - 删除用户</p>
                </div>

                <div class="api-item">
                    <h3>角色管理</h3>
                    <p><span class="method get">GET</span> <code>/adminapi/role/list</code> - 获取角色列表</p>
                    <p><span class="method post">POST</span> <code>/adminapi/role/create</code> - 创建角色</p>
                    <p><span class="method put">PUT</span> <code>/adminapi/role/update/:id</code> - 更新角色</p>
                    <p><span class="method delete">DELETE</span> <code>/adminapi/role/delete/:id</code> - 删除角色</p>
                </div>

                <div class="api-item">
                    <h3>菜单管理</h3>
                    <p><span class="method get">GET</span> <code>/adminapi/menu/list</code> - 获取菜单列表(动态路由)</p>
                    <p><span class="method get">GET</span> <code>/adminapi/menu/tree</code> - 获取菜单树形结构</p>
                    <p><span class="method post">POST</span> <code>/adminapi/menu/create</code> - 创建菜单</p>
                    <p><span class="method put">PUT</span> <code>/adminapi/menu/update/:id</code> - 更新菜单</p>
                    <p><span class="method delete">DELETE</span> <code>/adminapi/menu/delete/:id</code> - 删除菜单</p>
                </div>
            </div>

            <div class="api-group">
                <h2>📱 移动端API (/api)</h2>
                
                <div class="api-item">
                    <h3>用户认证</h3>
                    <p><span class="method post">POST</span> <code>/api/auth/login</code> - 用户登录</p>
                    <p><span class="method post">POST</span> <code>/api/auth/register</code> - 用户注册</p>
                    <p><span class="method post">POST</span> <code>/api/auth/logout</code> - 退出登录</p>
                </div>

                <div class="api-item">
                    <h3>用户资料</h3>
                    <p><span class="method get">GET</span> <code>/api/user/profile</code> - 获取用户资料</p>
                    <p><span class="method post">POST</span> <code>/api/user/update</code> - 更新用户资料</p>
                    <p><span class="method post">POST</span> <code>/api/user/avatar</code> - 上传头像</p>
                </div>
            </div>

            <div class="api-group">
                <h2>🔧 测试工具</h2>
                <p><a href="/test_api.html" target="_blank">📋 API测试页面</a> - 在线测试API接口</p>
            </div>

            <div class="api-group">
                <h2>📝 使用说明</h2>
                <div class="api-item">
                    <h3>认证方式</h3>
                    <p>使用 JWT Token 认证，请在请求头中添加：</p>
                    <code>Authorization: Bearer {token}</code>
                </div>
                
                <div class="api-item">
                    <h3>默认管理员账号</h3>
                    <p>用户名: <code>admin</code></p>
                    <p>密码: <code>password</code></p>
                </div>
            </div>
        </body>
        </html>';
        
        return response($html)->contentType('text/html');
    }
}
