var e=Object.defineProperty,r=Object.defineProperties,t=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable,s=(r,t,a)=>t in r?e(r,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):r[t]=a;import"./index-1JOyfOxu.js";import{m as p,r as l,P as i,D as c,R as m}from"./vendor-8T3zXQLl.js";import{_ as b}from"./_plugin-vue_export-helper-BCo6x5W8.js";const u={class:"article-management"},f=p((d=((e,r)=>{for(var t in r||(r={}))o.call(r,t)&&s(e,t,r[t]);if(a)for(var t of a(r))n.call(r,t)&&s(e,t,r[t]);return e})({},{name:"ArticleList"}),r(d,t({__name:"index",setup:e=>(l([]),(e,r)=>(c(),i("div",u,r[0]||(r[0]=[m("h2",null,"文章管理",-1),m("p",null,"请选择左侧菜单进行相应的操作",-1)]))))}))));var d;const j=b(f,[["__scopeId","data-v-5e177b87"]]);export{j as default};
