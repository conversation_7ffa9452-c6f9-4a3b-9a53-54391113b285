var e=Object.defineProperty,a=Object.defineProperties,r=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,i=(a,r,t)=>r in a?e(a,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[r]=t;import{U as n}from"./index-DG9-1w7X.js";/* empty css                */import{_ as l}from"./index-CGmIUeDt.js";import{_ as p,A as c}from"./useTableColumns-BGewnWEG.js";/* empty css                  */import{u}from"./useTable-ORdxa8Sm.js";import{_ as m}from"./user-search.vue_vue_type_script_setup_true_lang-BXGU9db4.js";import{_ as d}from"./user-dialog.vue_vue_type_script_setup_true_lang-ChbDNXb1.js";import{a as g}from"./index-syD2yfUn.js";import{k as f,r as v,P as y,D as j,x as b,u as h,G as I,i as _,a6 as x,W as P,aQ as C,n as M,l as D,E as A,aR as N,aI as z}from"./vendor-84Inc-Pt.js";import{_ as w}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                   *//* empty css                      *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";import"./index-DkLmvGl4.js";/* empty css                *//* empty css               *//* empty css               */import"./el-form-item-l0sNRNKZ.js";/* empty css                       *//* empty css                  */const S={class:"user-page art-full-height"},O=f((G=((e,a)=>{for(var r in a||(a={}))o.call(a,r)&&i(e,r,a[r]);if(t)for(var r of t(a))s.call(a,r)&&i(e,r,a[r]);return e})({},{name:"User"}),a(G,r({__name:"index",setup(e){const{width:a}=g(),{getUserList:r}=n,t=v("add"),o=v(!1),s=v({}),i=v([]),f={1:{type:"success",text:"在线"},2:{type:"info",text:"离线"},3:{type:"warning",text:"异常"},4:{type:"danger",text:"注销"}},{columns:w,columnChecks:O,tableData:G,isLoading:k,paginationState:T,searchData:Z,resetSearch:R,onPageSizeChange:J,onCurrentPageChange:U,refreshAll:B,refreshAfterCreate:E,refreshAfterUpdate:L,refreshAfterRemove:W}=u({core:{apiFn:r,apiParams:{current:1,size:20,name:"",phone:"",address:void 0},columnsFactory:()=>[{type:"selection"},{type:"index",width:60,label:"序号"},{prop:"avatar",label:"用户名",minWidth:a.value<500?220:"",formatter:e=>{const a="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzIgMzJDMzIgMjYuNDc3MiAyNy41MjI4IDIyIDIyIDIySDE4QzEyLjQ3NzIgMjIgOCAyNi40NzcyIDggMzJWMzJIMzJWMzJaIiBmaWxsPSIjOTk5OTk5Ii8+Cjwvc3ZnPgo=",r=e.avatar&&""!==e.avatar.trim()?e.avatar:a;return D("div",{class:"user",style:"display: flex; align-items: center"},[D("img",{class:"avatar",src:r,onError:e=>{const r=e.target;r&&(r.src=a)}}),D("div",{},[D("p",{class:"user-name"},e.userName),D("p",{class:"email"},e.userEmail)])])}},{prop:"userGender",label:"性别",sortable:!0,formatter:e=>e.userGender},{prop:"userPhone",label:"手机号"},{prop:"status",label:"状态",formatter:e=>{const a=(r=e.status,f[r]||{type:"info",text:"未知"});var r;return D(N,{type:a.type},(()=>a.text))}},{prop:"createTime",label:"创建日期",sortable:!0},{prop:"operation",label:"操作",width:120,fixed:"right",formatter:e=>D("div",[D(c,{type:"edit",onClick:()=>F("edit",e)}),D(c,{type:"delete",onClick:()=>Q(e)})])}]},transform:{dataTransformer:e=>Array.isArray(e)?e:[]},performance:{enableCache:!0,cacheTime:6e5},hooks:{onError:e=>A.error(e.message)},debug:{enableLog:!0}}),F=(e,a)=>{t.value=e,s.value=a||{},M((()=>{o.value=!0}))},Q=e=>{z.confirm("确定要注销该用户吗？","注销用户",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}).then((()=>{A.success("注销成功"),W()}))},H=()=>{return e=this,a=null,r=function*(){try{o.value=!1,yield"add"===t.value?E():L(),s.value={}}catch(e){}},new Promise(((t,o)=>{var s=e=>{try{n(r.next(e))}catch(a){o(a)}},i=e=>{try{n(r.throw(e))}catch(a){o(a)}},n=e=>e.done?t(e.value):Promise.resolve(e.value).then(s,i);n((r=r.apply(e,a)).next())}));var e,a,r},K=e=>{i.value=e};return(e,a)=>{const r=x,i=p,n=l,c=C;return j(),y("div",S,[b(m,{onReset:h(R),onSearch:h(Z)},null,8,["onReset","onSearch"]),b(c,{class:"art-table-card",shadow:"never"},{default:I((()=>[b(i,{columns:h(O),"onUpdate:columns":a[1]||(a[1]=e=>_(O)?O.value=e:null),onRefresh:h(B)},{left:I((()=>[b(r,{onClick:a[0]||(a[0]=e=>F("add"))},{default:I((()=>a[3]||(a[3]=[P("新增用户")]))),_:1,__:[3]})])),_:1},8,["columns","onRefresh"]),b(n,{loading:h(k),data:h(G),columns:h(w),pagination:h(T),"table-config":{rowKey:"id"},layout:{marginTop:10},"onRow:selectionChange":K,"onPagination:sizeChange":h(J),"onPagination:currentChange":h(U)},null,8,["loading","data","columns","pagination","onPagination:sizeChange","onPagination:currentChange"]),b(d,{visible:h(o),"onUpdate:visible":a[2]||(a[2]=e=>_(o)?o.value=e:null),type:h(t),"user-data":h(s),onSubmit:H},null,8,["visible","type","user-data"])])),_:1})])}}}))));var G;const k=w(O,[["__scopeId","data-v-df27bbcd"]]);export{k as default};
