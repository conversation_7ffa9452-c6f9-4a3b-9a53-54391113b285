-- FanShop 数据库结构
-- 使用 fs_ 前缀

-- 删除现有表（注意顺序，先删除有外键关系的表）
DROP TABLE IF EXISTS `fs_role_permission`;
DROP TABLE IF EXISTS `fs_role_menu`;
DROP TABLE IF EXISTS `fs_admin_role`;
DROP TABLE IF EXISTS `fs_permission`;
DROP TABLE IF EXISTS `fs_upload`;
DROP TABLE IF EXISTS `fs_system_config`;
DROP TABLE IF EXISTS `fs_menu`;
DROP TABLE IF EXISTS `fs_role`;
DROP TABLE IF EXISTS `fs_user`;
DROP TABLE IF EXISTS `fs_admin`;

-- 管理员表
CREATE TABLE `fs_admin` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:1正常,2禁用',
  `last_login_time` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- 用户表
CREATE TABLE `fs_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `gender` tinyint(1) DEFAULT '0' COMMENT '性别:0未知,1男,2女',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:1正常,2禁用,3注销',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 角色表
CREATE TABLE `fs_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_name` varchar(50) NOT NULL COMMENT '角色名称',
  `role_code` varchar(50) NOT NULL COMMENT '角色编码',
  `description` text COMMENT '角色描述',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:1启用,0禁用',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `role_code` (`role_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 菜单表
CREATE TABLE `fs_menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parent_id` int(11) DEFAULT '0' COMMENT '父级ID',
  `name` varchar(50) NOT NULL COMMENT '菜单名称',
  `path` varchar(255) DEFAULT NULL COMMENT '路由路径',
  `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
  `title` varchar(50) NOT NULL COMMENT '菜单标题',
  `icon` varchar(100) DEFAULT NULL COMMENT '菜单图标',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `type` tinyint(1) DEFAULT '1' COMMENT '类型:1菜单,2按钮',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:1显示,0隐藏',
  `keep_alive` tinyint(1) DEFAULT '0' COMMENT '是否缓存',
  `external_link` varchar(255) DEFAULT NULL COMMENT '外部链接',
  `is_iframe` tinyint(1) DEFAULT '0' COMMENT '是否iframe',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜单表';

-- 权限表
CREATE TABLE `fs_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `menu_id` int(11) NOT NULL COMMENT '菜单ID',
  `name` varchar(50) NOT NULL COMMENT '权限名称',
  `code` varchar(50) NOT NULL COMMENT '权限编码',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `menu_id` (`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';

-- 管理员角色关联表
CREATE TABLE `fs_admin_role` (
  `admin_id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  PRIMARY KEY (`admin_id`,`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员角色关联表';

-- 角色菜单关联表
CREATE TABLE `fs_role_menu` (
  `role_id` int(11) NOT NULL,
  `menu_id` int(11) NOT NULL,
  PRIMARY KEY (`role_id`,`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色菜单关联表';

-- 角色权限关联表
CREATE TABLE `fs_role_permission` (
  `role_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`role_id`,`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- 系统配置表
CREATE TABLE `fs_system_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_desc` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `config_type` varchar(20) DEFAULT 'string' COMMENT '配置类型',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 文件上传表
-- 图片分类表
CREATE TABLE `fs_media_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `parent_id` int(11) DEFAULT 0 COMMENT '父分类ID',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='媒体文件分类表';

CREATE TABLE `fs_upload` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `file_name` varchar(255) NOT NULL COMMENT '存储文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_url` varchar(500) DEFAULT NULL COMMENT '文件访问URL',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型',
  `mime_type` varchar(100) DEFAULT NULL COMMENT 'MIME类型',
  `category_id` int(11) DEFAULT 0 COMMENT '分类ID',
  `upload_user_id` int(11) DEFAULT NULL COMMENT '上传用户ID',
  `upload_user_type` varchar(20) DEFAULT 'admin' COMMENT '上传用户类型:admin,user',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_upload_user` (`upload_user_id`, `upload_user_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件上传表';

-- 插入初始数据

-- 插入超级管理员
INSERT INTO `fs_admin` (`id`, `username`, `password`, `nickname`, `avatar`, `email`, `status`) VALUES
(1, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '超级管理员', 'admin_avatar.jpg', '<EMAIL>', 1);

-- 插入角色
INSERT INTO `fs_role` (`id`, `role_name`, `role_code`, `description`, `status`) VALUES
(1, '超级管理员', 'R_SUPER', '拥有所有权限的超级管理员', 1),
(2, '系统管理员', 'R_ADMIN', '系统管理员角色', 1),
(3, '普通管理员', 'R_USER', '普通管理员角色', 1);

-- 插入菜单
INSERT INTO `fs_menu` (`id`, `parent_id`, `name`, `path`, `component`, `title`, `icon`, `sort`, `type`, `status`) VALUES
(1, 0, 'Dashboard', '/dashboard', '/index/index', '仪表盘', 'ep:home-filled', 1, 1, 1),
(2, 0, 'System', '/system', '/index/index', '系统管理', 'ep:setting', 2, 1, 1),
(3, 2, 'UserManage', '/system/user', '/system/user/index', '用户管理', 'ep:user', 1, 1, 1),
(4, 2, 'RoleManage', '/system/role', '/system/role/index', '角色管理', 'ep:lock', 2, 1, 1),
(5, 2, 'MenuManage', '/system/menu', '/system/menu/index', '菜单管理', 'ep:menu', 3, 1, 1),
(6, 2, 'SystemConfig', '/system/config', '/system/config/index', '系统配置', 'ep:tools', 4, 1, 1);

-- 插入权限
INSERT INTO `fs_permission` (`id`, `menu_id`, `name`, `code`) VALUES
(1, 3, '查看用户', 'user:view'),
(2, 3, '添加用户', 'user:add'),
(3, 3, '编辑用户', 'user:edit'),
(4, 3, '删除用户', 'user:delete'),
(5, 4, '查看角色', 'role:view'),
(6, 4, '添加角色', 'role:add'),
(7, 4, '编辑角色', 'role:edit'),
(8, 4, '删除角色', 'role:delete'),
(9, 5, '查看菜单', 'menu:view'),
(10, 5, '添加菜单', 'menu:add'),
(11, 5, '编辑菜单', 'menu:edit'),
(12, 5, '删除菜单', 'menu:delete');

-- 关联超级管理员角色
INSERT INTO `fs_admin_role` (`admin_id`, `role_id`) VALUES (1, 1);

-- 关联超级管理员所有菜单
INSERT INTO `fs_role_menu` (`role_id`, `menu_id`) VALUES
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5), (1, 6);

-- 关联超级管理员所有权限
INSERT INTO `fs_role_permission` (`role_id`, `permission_id`) VALUES
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5), (1, 6), (1, 7), (1, 8), (1, 9), (1, 10), (1, 11), (1, 12);

-- 插入媒体分类
INSERT INTO `fs_media_category` (`id`, `name`, `parent_id`, `sort`) VALUES
(1, 'logo', 0, 1),
(2, '商品图片', 0, 2),
(3, '清报', 0, 3),
(4, '系统图片', 0, 4);

-- 插入系统配置
INSERT INTO `fs_system_config` (`config_key`, `config_value`, `config_desc`, `config_type`) VALUES
('site_name', 'FanShop管理系统', '网站名称', 'string'),
('site_logo', '', '网站Logo', 'string'),
('site_description', '一个功能强大的商城管理系统', '网站描述', 'string'),
('site_keywords', '商城,管理系统,电商,购物', '网站关键词', 'string'),
('site_favicon', '', '网站图标', 'string'),
('site_copyright', 'Copyright © 2024 FanShop. All rights reserved.', '版权信息', 'string'),
('site_icp', '', 'ICP备案号', 'string'),
('site_phone', '', '联系电话', 'string'),
('site_email', '', '联系邮箱', 'string'),
('site_address', '', '联系地址', 'string'),
('upload_max_size', '10485760', '上传文件最大大小(字节)', 'number'),
('upload_allowed_ext', 'jpg,jpeg,png,gif,webp,pdf,doc,docx,xls,xlsx', '允许上传的文件扩展名', 'string');
