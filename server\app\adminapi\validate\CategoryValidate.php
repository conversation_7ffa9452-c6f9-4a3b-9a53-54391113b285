<?php
declare(strict_types=1);

namespace app\adminapi\validate;

use think\Validate;

class CategoryValidate extends Validate
{
    protected $rule = [
        'name' => 'require|max:50',
        'parent_id' => 'integer|egt:0',
        'status' => 'in:0,1',
        'sort' => 'integer|egt:0',
    ];

    protected $message = [
        'name.require' => '分类名称不能为空',
        'name.max' => '分类名称不能超过50个字符',
        'parent_id.integer' => '上级分类ID必须是整数',
        'parent_id.egt' => '上级分类ID不能小于0',
        'status.in' => '状态值错误',
        'sort.integer' => '排序必须是整数',
        'sort.egt' => '排序不能小于0',
    ];

    protected $scene = [
        'create' => ['name', 'parent_id', 'status', 'sort'],
        'update' => ['name', 'parent_id', 'status', 'sort'],
    ];
}
