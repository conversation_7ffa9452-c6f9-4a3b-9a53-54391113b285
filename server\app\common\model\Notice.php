<?php
declare(strict_types=1);

namespace app\common\model;

use think\Model;

/**
 * 公告模型
 *
 * @property int $id 公告ID
 * @property string $title 公告标题
 * @property string $content 公告内容
 * @property string $cover_image 封面图片
 * @property int $type 公告类型
 * @property int $is_published 是否发布
 * @property int $is_top 是否置顶
 * @property int $status 状态
 * @property int $sort 排序
 * @property int $view_count 浏览量
 * @property string $published_at 发布时间
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class Notice extends Model
{
    /** @var string 表名 */
    protected $table = 'fs_notices';

    /** @var string 主键 */
    protected $pk = 'id';

    /** @var bool 自动时间戳 */
    protected $autoWriteTimestamp = true;

    /** @var string 创建时间字段 */
    protected $createTime = 'created_at';

    /** @var string 更新时间字段 */
    protected $updateTime = 'updated_at';

    /** @var array 字段类型转换 */
    protected $type = [
        'id' => 'integer',
        'type' => 'integer',
        'is_published' => 'integer',
        'is_top' => 'integer',
        'status' => 'integer',
        'sort' => 'integer',
        'view_count' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'published_at' => 'datetime',
    ];

    /** @var array 允许批量赋值的字段 */
    protected $fillable = [
        'title',
        'content',
        'cover_image',
        'type',
        'is_published',
        'is_top',
        'status',
        'sort',
        'view_count',
        'published_at'
    ];

    /** @var array 隐藏字段 */
    protected $hidden = [];

    /** @var array 追加字段 */
    protected $append = [
        'type_text',
        'published_text',
        'top_text',
        'status_text'
    ];

    /**
     * 获取器 - 公告类型文本
     *
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getTypeTextAttr($value, array $data): string
    {
        $types = [
            1 => '系统通知',
            2 => '功能更新',
            3 => '服务公告'
        ];
        return $types[$data['type']] ?? '其他';
    }

    /**
     * 获取器 - 发布状态文本
     *
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getPublishedTextAttr($value, array $data): string
    {
        $status = [0 => '草稿', 1 => '已发布'];
        return $status[$data['is_published']] ?? '未知';
    }

    /**
     * 获取器 - 置顶状态文本
     *
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getTopTextAttr($value, array $data): string
    {
        $status = [0 => '否', 1 => '是'];
        return $status[$data['is_top']] ?? '否';
    }

    /**
     * 获取器 - 状态文本
     *
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getStatusTextAttr($value, array $data): string
    {
        $status = [0 => '禁用', 1 => '启用'];
        return $status[$data['status']] ?? '未知';
    }

    /**
     * 搜索器 - 标题搜索
     *
     * @param \think\db\Query $query
     * @param string $value
     * @return void
     */
    public function searchTitleAttr($query, string $value): void
    {
        if (!empty($value)) {
            $query->whereLike('title', '%' . $value . '%');
        }
    }

    /**
     * 搜索器 - 状态搜索
     *
     * @param \think\db\Query $query
     * @param int $value
     * @return void
     */
    public function searchStatusAttr($query, int $value): void
    {
        $query->where('status', $value);
    }

    /**
     * 搜索器 - 置顶搜索
     *
     * @param \think\db\Query $query
     * @param int $value
     * @return void
     */
    public function searchIsTopAttr($query, int $value): void
    {
        $query->where('is_top', $value);
    }
}
