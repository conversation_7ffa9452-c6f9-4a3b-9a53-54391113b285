<?php
declare(strict_types=1);

namespace app\adminapi\controller;

use app\common\controller\AdminApiController;
use app\common\service\MediaService;
use think\Request;
use think\Response;

/**
 * 媒体文件管理控制器
 */
class Media extends AdminApiController
{
    /**
     * 获取媒体文件列表
     */
    public function index(Request $request): Response
    {
        $params = $request->param();

        $page = (int)($params['page'] ?? 1);
        $limit = (int)($params['limit'] ?? 20);
        $categoryId = isset($params['category_id']) && $params['category_id'] !== '' ? (int)$params['category_id'] : null;
        $keyword = $params['keyword'] ?? '';
        $fileType = $params['file_type'] ?? '';

        $result = MediaService::getMediaList($page, $limit, $categoryId, $keyword, $fileType);

        return $this->success($result);
    }
    
    /**
     * 上传媒体文件
     */
    public function upload(Request $request): Response
    {
        $file = $request->file('file');
        $categoryId = (int)($request->param('category_id', 0));
        
        if (!$file) {
            return $this->error('请选择要上传的文件');
        }
        
        try {
            $result = MediaService::uploadMedia($file, $categoryId);
            return $this->success($result, '上传成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 删除媒体文件
     */
    public function delete(Request $request): Response
    {
        $id = (int)$request->param('id');
        
        if (!$id) {
            return $this->error('参数错误');
        }
        
        try {
            MediaService::deleteMedia($id);
            return $this->success([], '删除成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 批量删除媒体文件
     */
    public function batchDelete(Request $request): Response
    {
        $ids = $request->param('ids', []);
        
        if (empty($ids) || !is_array($ids)) {
            return $this->error('请选择要删除的文件');
        }
        
        try {
            MediaService::batchDeleteMedia($ids);
            return $this->success([], '批量删除成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 移动文件到分类
     */
    public function moveToCategory(Request $request): Response
    {
        $ids = $request->param('ids', []);
        $categoryId = (int)$request->param('category_id', 0);
        
        if (empty($ids) || !is_array($ids)) {
            return $this->error('请选择要移动的文件');
        }
        
        try {
            MediaService::moveToCategory($ids, $categoryId);
            return $this->success([], '移动成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 获取分类列表
     */
    public function categories(): Response
    {
        $categories = MediaService::getCategories();
        return $this->success($categories);
    }
    
    /**
     * 创建分类
     */
    public function createCategory(Request $request): Response
    {
        $name = $request->param('name', '');
        $parentId = (int)$request->param('parent_id', 0);
        
        if (empty($name)) {
            return $this->error('分类名称不能为空');
        }
        
        try {
            $category = MediaService::createCategory($name, $parentId);
            return $this->success($category, '创建成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 更新分类
     */
    public function updateCategory(Request $request): Response
    {
        $id = (int)$request->param('id');
        $name = $request->param('name', '');
        
        if (!$id || empty($name)) {
            return $this->error('参数错误');
        }
        
        try {
            MediaService::updateCategory($id, $name);
            return $this->success([], '更新成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 删除分类
     */
    public function deleteCategory(Request $request): Response
    {
        $id = (int)$request->param('id');
        
        if (!$id) {
            return $this->error('参数错误');
        }
        
        try {
            MediaService::deleteCategory($id);
            return $this->success([], '删除成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
