var e=Object.defineProperty,a=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,r=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t,s=(e,a,l)=>new Promise(((t,r)=>{var s=e=>{try{d(l.next(e))}catch(a){r(a)}},o=e=>{try{d(l.throw(e))}catch(a){r(a)}},d=e=>e.done?t(e.value):Promise.resolve(e.value).then(s,o);d((l=l.apply(e,a)).next())}));import{B as o}from"./index-TSQrMSQp.js";/* empty css                  *//* empty css                  *//* empty css                *//* empty css                  *//* empty css                     *//* empty css               *//* empty css               */import"./el-form-item-l0sNRNKZ.js";/* empty css                 */import{k as d,r as u,M as i,c as n,w as m,d as p,C as c,D as b,G as f,x as g,a8 as v,aL as _,aK as y,a2 as h,a3 as w,ar as V,as as j,P as k,F as D,$ as x,R as O,a6 as U,W as P,ak as A,E as I}from"./vendor-84Inc-Pt.js";import{_ as $}from"./_plugin-vue_export-helper-BCo6x5W8.js";class R{static getAdminList(e){return o.get({url:"/admin/list",params:e})}static createAdmin(e){return o.post({url:"/admin/create",data:e})}static updateAdmin(e,a){return o.put({url:`/admin/update/${e}`,data:a})}static deleteAdmin(e){return o.del({url:`/admin/delete/${e}`})}static getRoles(){return o.get({url:"/admin/roles"})}static resetPassword(e,a){return o.post({url:`/admin/reset-password/${e}`,data:{password:a}})}static toggleStatus(e){return o.post({url:`/admin/toggle-status/${e}`})}}const C={class:"dialog-footer"},E=$(d({__name:"admin-dialog",props:{visible:{type:Boolean},type:{},adminData:{default:null}},emits:["update:visible","submit"],setup(e,{emit:o}){const d=e,$=o,E=u(),S=u(!1),L=u([]),q=i({username:"",password:"",nickname:"",email:"",phone:"",avatar:"",status:1,roleIds:[]}),z=n({get:()=>d.visible,set:e=>$("update:visible",e)}),B=n((()=>"add"===d.type?"新增管理员":"编辑管理员")),G={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:50,message:"用户名长度在 3 到 50 个字符",trigger:"blur"}],password:[{validator:(e,a,l)=>{"edit"!==d.type||a&&""!==a.trim()?"add"!==d.type||a&&""!==a.trim()?a&&a.trim()&&(a.length<6||a.length>32)?l(new Error("密码长度在 6 到 32 个字符")):l():l(new Error("请输入密码")):l()},trigger:"blur"}],nickname:[{min:2,max:50,message:"昵称长度在 2 到 50 个字符",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]};m((()=>d.visible),(e=>{e&&(M(),"edit"===d.type&&d.adminData&&Object.assign(q,{username:d.adminData.username,nickname:d.adminData.nickname,email:d.adminData.email,phone:d.adminData.phone,avatar:d.adminData.avatar,status:d.adminData.status,roleIds:d.adminData.roles.map((e=>e.id)),password:""}))}));const M=()=>{var e;Object.assign(q,{username:"",password:"",nickname:"",email:"",phone:"",avatar:"",status:1,roleIds:[]}),null==(e=E.value)||e.clearValidate()},F=()=>{z.value=!1},J=()=>s(this,null,(function*(){var e,s;if(E.value)try{if(yield E.value.validate(),S.value=!0,"add"===d.type)yield R.createAdmin(q),I.success("创建成功");else{const e=((e,s)=>{for(var o in s||(s={}))l.call(s,o)&&r(e,o,s[o]);if(a)for(var o of a(s))t.call(s,o)&&r(e,o,s[o]);return e})({},q);e.password&&""!==e.password.trim()||delete e.password,yield R.updateAdmin(d.adminData.id,e),I.success("更新成功")}$("submit"),F()}catch(o){(null==(s=null==(e=o.response)?void 0:e.data)?void 0:s.message)?I.error(`提交失败: ${o.response.data.message}`):o.message?I.error(`提交失败: ${o.message}`):I.error("提交失败")}finally{S.value=!1}})),K=()=>s(this,null,(function*(){try{L.value=yield R.getRoles()}catch(e){I.error("获取角色列表失败")}}));return p((()=>{K()})),(e,a)=>{const l=w,t=h,r=y,s=_,o=j,d=V,u=v,i=U,n=A;return b(),c(n,{modelValue:z.value,"onUpdate:modelValue":a[8]||(a[8]=e=>z.value=e),title:B.value,width:"600px","close-on-click-modal":!1,onClose:F},{footer:f((()=>[O("div",C,[g(i,{onClick:F},{default:f((()=>a[9]||(a[9]=[P("取消")]))),_:1,__:[9]}),g(i,{type:"primary",loading:S.value,onClick:J},{default:f((()=>a[10]||(a[10]=[P(" 确定 ")]))),_:1,__:[10]},8,["loading"])])])),default:f((()=>[g(u,{ref_key:"formRef",ref:E,model:q,rules:G,"label-width":"80px","label-position":"right"},{default:f((()=>[g(s,{gutter:20},{default:f((()=>[g(r,{span:12},{default:f((()=>[g(t,{label:"用户名",prop:"username"},{default:f((()=>[g(l,{modelValue:q.username,"onUpdate:modelValue":a[0]||(a[0]=e=>q.username=e),placeholder:"请输入用户名",clearable:""},null,8,["modelValue"])])),_:1})])),_:1}),g(r,{span:12},{default:f((()=>[g(t,{label:"昵称",prop:"nickname"},{default:f((()=>[g(l,{modelValue:q.nickname,"onUpdate:modelValue":a[1]||(a[1]=e=>q.nickname=e),placeholder:"请输入昵称",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),g(s,{gutter:20},{default:f((()=>[g(r,{span:12},{default:f((()=>[g(t,{label:"邮箱",prop:"email"},{default:f((()=>[g(l,{modelValue:q.email,"onUpdate:modelValue":a[2]||(a[2]=e=>q.email=e),placeholder:"请输入邮箱",clearable:""},null,8,["modelValue"])])),_:1})])),_:1}),g(r,{span:12},{default:f((()=>[g(t,{label:"手机号",prop:"phone"},{default:f((()=>[g(l,{modelValue:q.phone,"onUpdate:modelValue":a[3]||(a[3]=e=>q.phone=e),placeholder:"请输入手机号",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),g(s,{gutter:20},{default:f((()=>[g(r,{span:12},{default:f((()=>[g(t,{label:"密码",prop:"password"},{default:f((()=>[g(l,{modelValue:q.password,"onUpdate:modelValue":a[4]||(a[4]=e=>q.password=e),type:"password",placeholder:"add"===e.type?"请输入密码":"留空则不修改密码","show-password":"",clearable:""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),g(r,{span:12},{default:f((()=>[g(t,{label:"状态",prop:"status"},{default:f((()=>[g(d,{modelValue:q.status,"onUpdate:modelValue":a[5]||(a[5]=e=>q.status=e),placeholder:"请选择状态",style:{width:"100%"}},{default:f((()=>[g(o,{label:"正常",value:1}),g(o,{label:"禁用",value:2})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),g(t,{label:"分配角色",prop:"roleIds"},{default:f((()=>[g(d,{modelValue:q.roleIds,"onUpdate:modelValue":a[6]||(a[6]=e=>q.roleIds=e),multiple:"",placeholder:"请选择角色",style:{width:"100%"},clearable:""},{default:f((()=>[(b(!0),k(D,null,x(L.value,(e=>(b(),c(o,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),g(t,{label:"头像",prop:"avatar"},{default:f((()=>[g(l,{modelValue:q.avatar,"onUpdate:modelValue":a[7]||(a[7]=e=>q.avatar=e),placeholder:"请输入头像URL",clearable:""},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-bf6600ba"]]),S=Object.freeze(Object.defineProperty({__proto__:null,default:E},Symbol.toStringTag,{value:"Module"}));export{E as A,R as a,S as b};
