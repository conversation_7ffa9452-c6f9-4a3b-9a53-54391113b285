<?php
declare(strict_types=1);

namespace app\api\controller;

use app\common\controller\BaseController;
use app\common\model\User;
use app\common\service\AuthService;
use app\api\validate\UserValidate;

/**
 * 移动端用户认证控制器
 */
class Auth extends BaseController
{
    /**
     * 用户登录
     */
    public function login()
    {
        $data = $this->request->post();
        
        // 验证参数
        $validate = new UserValidate();
        if (!$validate->scene('login')->check($data)) {
            return $this->error($validate->getError());
        }

        try {
            $result = AuthService::userLogin($data['username'], $data['password']);
            return $this->success($result, '登录成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 用户注册
     */
    public function register()
    {
        $data = $this->request->post();
        
        // 验证参数
        $validate = new UserValidate();
        if (!$validate->scene('register')->check($data)) {
            return $this->error($validate->getError());
        }

        try {
            $user = new User();
            $user->save([
                'username' => $data['username'],
                'password' => $data['password'],
                'nickname' => $data['nickname'] ?? $data['username'],
                'email' => $data['email'] ?? '',
                'phone' => $data['phone'] ?? '',
            ]);
            
            return $this->success([], '注册成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 退出登录
     */
    public function logout()
    {
        return $this->success([], '退出成功');
    }

    /**
     * 刷新Token
     */
    public function refresh()
    {
        $refreshToken = $this->request->post('refreshToken');
        
        if (!$refreshToken) {
            return $this->error('刷新Token不能为空');
        }

        try {
            $result = AuthService::refreshToken($refreshToken);
            return $this->success($result, '刷新成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
