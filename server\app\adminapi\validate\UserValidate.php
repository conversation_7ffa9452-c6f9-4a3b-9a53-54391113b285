<?php
declare(strict_types=1);

namespace app\adminapi\validate;

use think\Validate;

/**
 * 用户验证器
 */
class UserValidate extends Validate
{
    protected $rule = [
        // 前端字段名
        'nickName' => 'length:2,20',
        'password' => 'length:6,32',
        'userPhone' => 'mobile|unique:user,phone',
        'payPassword' => 'regex:/^\d{6}$/',
        'referrer' => 'max:50',
        'avatar' => 'max:255',
        'userEmail' => 'email',
        'userStatus' => 'in:1,2',
        // 后端字段名（用于唯一性验证）
        'username' => 'length:3,50|unique:user',
        'nickname' => 'length:2,20',
        'phone' => 'mobile|unique:user',
        'email' => 'email',
        'status' => 'in:1,2',
    ];

    /**
     * 设置编辑时的验证规则（排除当前用户）
     * @param int $userId 当前用户ID
     * @return $this
     */
    public function setUpdateRules(int $userId): self
    {
        // 编辑时排除当前用户的手机号
        $this->rule['userPhone'] = "mobile|unique:user,phone,{$userId}";
        $this->rule['phone'] = "mobile|unique:user,phone,{$userId}";
        return $this;
    }

    protected $message = [
        'nickName.require' => '昵称不能为空',
        'nickName.length' => '昵称长度必须在2-20个字符之间',
        'password.require' => '密码不能为空',
        'password.length' => '密码长度必须在6-32个字符之间',
        'userPhone.require' => '手机号不能为空',
        'userPhone.mobile' => '手机号格式不正确',
        'userPhone.unique' => '手机号已存在',
        'payPassword.regex' => '支付密码必须是6位数字',
        'referrer.max' => '推荐人信息不能超过50个字符',
        'avatar.max' => '头像路径不能超过255个字符',
        'userEmail.email' => '邮箱格式不正确',
        'userStatus.in' => '状态值不正确',
        // 编辑时的错误消息
        'nickName_edit.length' => '昵称长度必须在2-20个字符之间',
        'password_edit.length' => '密码长度必须在6-32个字符之间',
        'userPhone_edit.mobile' => '手机号格式不正确',
        'payPassword_edit.regex' => '支付密码必须是6位数字',
        // 后端字段名错误消息
        'username.length' => '用户名长度必须在3-50个字符之间',
        'username.unique' => '用户名已存在',
        'nickname.require' => '昵称不能为空',
        'nickname.length' => '昵称长度必须在2-20个字符之间',
        'phone.require' => '手机号不能为空',
        'phone.mobile' => '手机号格式不正确',
        'phone.unique' => '手机号已存在',
        'email.email' => '邮箱格式不正确',
        'status.in' => '状态值不正确',
    ];

    protected $scene = [
        'create' => ['nickName', 'password', 'userPhone', 'payPassword', 'referrer', 'avatar', 'userEmail'],
        'update' => ['nickName', 'password', 'userPhone', 'payPassword', 'referrer', 'avatar', 'userEmail'],
    ];
}
