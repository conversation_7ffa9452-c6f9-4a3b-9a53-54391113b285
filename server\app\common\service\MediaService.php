<?php
declare(strict_types=1);

namespace app\common\service;

use think\facade\Db;
use think\file\UploadedFile;
use think\facade\Filesystem;

/**
 * 媒体文件服务类
 */
class MediaService
{
    /**
     * 获取媒体文件列表
     */
    public static function getMediaList(int $page = 1, int $limit = 20, ?int $categoryId = null, string $keyword = '', string $fileType = ''): array
    {
        $query = Db::table('fs_upload')
            ->alias('u')
            ->leftJoin('fs_media_category c', 'u.category_id = c.id')
            ->field('u.*, c.name as category_name')
            ->order('u.created_at desc');
        
        // 分类筛选
        if ($categoryId !== null) {
            if ($categoryId === 0) {
                // 未分组
                $query->where('u.category_id', 0);
            } else {
                $query->where('u.category_id', $categoryId);
            }
        }
        
        // 关键词搜索
        if (!empty($keyword)) {
            $query->where('u.original_name', 'like', '%' . $keyword . '%');
        }
        
        // 文件类型筛选
        if (!empty($fileType)) {
            if ($fileType === 'image') {
                $query->where('u.mime_type', 'like', 'image/%');
            } else {
                $query->where('u.file_type', $fileType);
            }
        }
        
        $total = $query->count();
        $list = $query->page($page, $limit)->select()->toArray();
        
        // 处理文件URL - 动态生成
        foreach ($list as &$item) {
            $item['file_url'] = self::generateFileUrl($item['file_path']);
            $item['is_image'] = strpos($item['mime_type'] ?? '', 'image/') === 0;
            $item['file_size_formatted'] = self::formatFileSize($item['file_size']);
        }
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }
    
    /**
     * 上传媒体文件
     */
    public static function uploadMedia(UploadedFile $file, int $categoryId = 0): array
    {
        // 验证文件
        $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'doc', 'docx', 'xls', 'xlsx'];
        $extension = strtolower($file->getOriginalExtension());
        
        if (!in_array($extension, $allowedTypes)) {
            throw new \Exception('不支持的文件类型');
        }
        
        // 检查文件大小
        $maxSize = 10 * 1024 * 1024; // 10MB
        if ($file->getSize() > $maxSize) {
            throw new \Exception('文件大小超过限制');
        }
        
        // 保存文件
        $saveName = Filesystem::disk('public')->putFile('uploads', $file);

        if (!$saveName) {
            throw new \Exception('文件保存失败');
        }

        // 修复路径，确保不重复uploads，统一使用正斜杠
        $filePath = 'storage/' . str_replace('\\', '/', $saveName);

        // 保存到数据库 - 只保存路径，不保存URL
        $data = [
            'original_name' => $file->getOriginalName(),
            'file_name' => basename($saveName),
            'file_path' => $filePath,
            'file_size' => $file->getSize(),
            'file_type' => $extension,
            'mime_type' => $file->getOriginalMime(),
            'category_id' => $categoryId,
            'upload_user_id' => self::getCurrentUserId(), // 获取当前用户ID
            'upload_user_type' => 'admin',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $id = Db::table('fs_upload')->insertGetId($data);
        $data['id'] = $id;
        $data['file_url'] = self::generateFileUrl($data['file_path']); // 动态生成URL
        $data['is_image'] = strpos($data['mime_type'], 'image/') === 0;
        $data['file_size_formatted'] = self::formatFileSize($data['file_size']);

        return $data;
    }
    
    /**
     * 删除媒体文件
     */
    public static function deleteMedia(int $id): void
    {
        $media = Db::table('fs_upload')->where('id', $id)->find();
        
        if (!$media) {
            throw new \Exception('文件不存在');
        }
        
        // 删除物理文件
        $fullPath = public_path() . '/' . $media['file_path'];
        if (file_exists($fullPath)) {
            unlink($fullPath);
        }
        
        // 删除数据库记录
        Db::table('fs_upload')->where('id', $id)->delete();
    }
    
    /**
     * 批量删除媒体文件
     */
    public static function batchDeleteMedia(array $ids): void
    {
        $mediaList = Db::table('fs_upload')->whereIn('id', $ids)->select();
        
        foreach ($mediaList as $media) {
            // 删除物理文件
            $fullPath = public_path() . '/' . $media['file_path'];
            if (file_exists($fullPath)) {
                unlink($fullPath);
            }
        }
        
        // 删除数据库记录
        Db::table('fs_upload')->whereIn('id', $ids)->delete();
    }
    
    /**
     * 移动文件到分类
     */
    public static function moveToCategory(array $ids, int $categoryId): void
    {
        Db::table('fs_upload')
            ->whereIn('id', $ids)
            ->update([
                'category_id' => $categoryId,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
    }
    
    /**
     * 获取分类列表
     */
    public static function getCategories(): array
    {
        $categories = Db::table('fs_media_category')
            ->order('sort asc, id asc')
            ->select()
            ->toArray();
        
        // 添加文件数量统计
        foreach ($categories as &$category) {
            $category['file_count'] = Db::table('fs_upload')
                ->where('category_id', $category['id'])
                ->count();
        }
        
        // 添加未分组统计
        $uncategorizedCount = Db::table('fs_upload')->where('category_id', 0)->count();
        
        array_unshift($categories, [
            'id' => 0,
            'name' => '未分组',
            'parent_id' => 0,
            'sort' => -1,
            'file_count' => $uncategorizedCount,
            'created_at' => '',
            'updated_at' => ''
        ]);
        
        return $categories;
    }
    
    /**
     * 创建分类
     */
    public static function createCategory(string $name, int $parentId = 0): array
    {
        $data = [
            'name' => $name,
            'parent_id' => $parentId,
            'sort' => 0,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $id = Db::table('fs_media_category')->insertGetId($data);
        $data['id'] = $id;
        $data['file_count'] = 0;
        
        return $data;
    }
    
    /**
     * 更新分类
     */
    public static function updateCategory(int $id, string $name): void
    {
        Db::table('fs_media_category')
            ->where('id', $id)
            ->update([
                'name' => $name,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
    }
    
    /**
     * 删除分类
     */
    public static function deleteCategory(int $id): void
    {
        // 检查是否有文件使用此分类
        $fileCount = Db::table('fs_upload')->where('category_id', $id)->count();
        
        if ($fileCount > 0) {
            throw new \Exception('该分类下还有文件，无法删除');
        }
        
        Db::table('fs_media_category')->where('id', $id)->delete();
    }
    
    /**
     * 生成文件访问URL
     */
    private static function generateFileUrl(string $filePath): string
    {
        // 确保文件路径以正确的格式开始
        $filePath = ltrim($filePath, '/');

        // 统一使用正斜杠
        $filePath = str_replace('\\', '/', $filePath);

        // 默认返回相对路径，前端会根据需要自动拼接域名
        return '/' . $filePath;
    }
    
    /**
     * 格式化文件大小
     */
    private static function formatFileSize(?int $size): string
    {
        if (!$size) return '0 B';
        
        $units = ['B', 'KB', 'MB', 'GB'];
        $i = 0;
        
        while ($size >= 1024 && $i < count($units) - 1) {
            $size /= 1024;
            $i++;
        }
        
        return round($size, 2) . ' ' . $units[$i];
    }

    /**
     * 获取当前用户ID
     *
     * @return int
     */
    private static function getCurrentUserId(): int
    {
        try {
            // 尝试从请求头获取token并解析用户ID
            $token = request()->header('Authorization');
            if ($token && str_starts_with($token, 'Bearer ')) {
                $token = substr($token, 7);
                $payload = \app\common\service\AuthService::verifyToken($token);
                return (int)($payload['id'] ?? 1);
            }
        } catch (\Exception $e) {
            // 如果获取失败，返回默认用户ID
        }

        // 默认返回管理员用户ID
        return 1;
    }
}
