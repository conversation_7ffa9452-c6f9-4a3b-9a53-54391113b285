var e=Object.defineProperty,r=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,l=(r,t,o)=>t in r?e(r,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[t]=o,a=(e,a)=>{for(var p in a||(a={}))t.call(a,p)&&l(e,p,a[p]);if(r)for(var p of r(a))o.call(a,p)&&l(e,p,a[p]);return e};import{_ as p}from"./index-DYv3Gk6_.js";import{m as s,r as i,C as m,D as n}from"./vendor-8T3zXQLl.js";import{_ as u}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-tBKMHRR1.js";/* empty css                *//* empty css               *//* empty css                  *//* empty css               */import"./el-form-item-l0sNRNKZ.js";import"./index-CDOnAZ6j.js";/* empty css                 *//* empty css                  *//* empty css                     *//* empty css                       *//* empty css                 */const c=u(s({__name:"admin-search",emits:["update:modelValue","search","reset"],setup(e,{emit:r}){const t=r,o={username:"",nickname:"",status:""},l=i(a({},o)),s=[{type:"input",prop:"username",label:"用户名",placeholder:"请输入用户名"},{type:"input",prop:"nickname",label:"昵称",placeholder:"请输入昵称"},{type:"select",prop:"status",label:"状态",placeholder:"请选择状态",options:[{label:"正常",value:1},{label:"禁用",value:2}]}],u=()=>{t("search",l.value)},c=()=>{l.value=a({},o),t("update:modelValue",a({},o)),t("reset")};return(e,r)=>{const t=p;return n(),m(t,{filter:l.value,"onUpdate:filter":r[0]||(r[0]=e=>l.value=e),items:s,onReset:c,onSearch:u},null,8,["filter"])}}}),[["__scopeId","data-v-725fa489"]]);export{c as default};
