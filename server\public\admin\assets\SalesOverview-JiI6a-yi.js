var e=Object.defineProperty,a=Object.defineProperties,t=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,s=(a,t,o)=>t in a?e(a,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):a[t]=o,i=(e,a)=>{for(var t in a||(a={}))l.call(a,t)&&s(e,t,a[t]);if(o)for(var t of o(a))r.call(a,t)&&s(e,t,a[t]);return e},n=(e,o)=>a(e,t(o));import{g as d,D as u}from"./index-1JOyfOxu.js";/* empty css                   */import{u as c,b as h,_ as f,g as v,L as y}from"./useChart-DbiZ5Fzz.js";import{m as p,r as m,d as g,w as b,o as x,O as w,a5 as A,aJ as L,P as S,D as _,Q as O,C as j,V as D,u as B,ai as C,x as P}from"./vendor-8T3zXQLl.js";import{_ as T}from"./_plugin-vue_export-helper-BCo6x5W8.js";const W=T(p(n(i({},{name:"ArtLineChart"}),{__name:"index",props:{data:{default:()=>[0,0,0,0,0,0,0]},xAxisData:{default:()=>[]},lineWidth:{default:2.5},showAreaColor:{type:Boolean,default:!1},smooth:{type:Boolean,default:!0},symbol:{default:"none"},symbolSize:{default:6},animationDelay:{default:200},height:{default:c().chartHeight},loading:{type:Boolean,default:!1},isEmpty:{type:Boolean,default:!1},colors:{default:()=>c().colors},showAxisLabel:{type:Boolean,default:!0},showAxisLine:{type:Boolean,default:!0},showSplitLine:{type:Boolean,default:!0},showTooltip:{type:Boolean,default:!0},showLegend:{type:Boolean,default:!1},legendPosition:{default:"bottom"}},setup(e){const a=e,{chartRef:t,isDark:o,initChart:l,getAxisLineStyle:r,getAxisLabelStyle:s,getAxisTickStyle:c,getSplitLineStyle:p,getTooltipStyle:C,getLegendStyle:P,getGridWithLegend:T}=h(),W=m(!1),E=m(),k=m([]),z=()=>{E.value&&(clearTimeout(E.value),E.value=void 0)},I=g((()=>{if(a.isEmpty)return!0;if(Array.isArray(a.data)&&"number"==typeof a.data[0]){const e=a.data;return!e.length||e.every((e=>0===e))}if(Array.isArray(a.data)&&"object"==typeof a.data[0]){const e=a.data;return!e.length||e.every((e=>{var a;return!(null==(a=e.data)?void 0:a.length)||e.data.every((e=>0===e))}))}return!0})),M=g((()=>Array.isArray(a.data)&&a.data.length>0&&"object"==typeof a.data[0]&&"name"in a.data[0])),V=g((()=>{if(M.value){return a.data.reduce(((e,a)=>{var t;if(null==(t=a.data)?void 0:t.length){const t=Math.max(...a.data);return Math.max(e,t)}return e}),0)}{const e=a.data;return(null==e?void 0:e.length)?Math.max(...e):0}})),G=()=>{if(M.value){return a.data.map((e=>n(i({},e),{data:new Array(e.data.length).fill(0)})))}{const e=a.data;return new Array(e.length).fill(0)}},J=()=>(M.value,[...a.data]),R=(e,t)=>e||(void 0!==t?a.colors[t%a.colors.length]:d("--el-color-primary")),H=e=>{var t,o,l,r,s;return{name:e.name,data:e.data,type:"line",color:e.color,smooth:null!=(t=e.smooth)?t:a.smooth,symbol:null!=(o=e.symbol)?o:a.symbol,symbolSize:null!=(l=e.symbolSize)?l:a.symbolSize,lineStyle:{width:null!=(r=e.lineWidth)?r:a.lineWidth,color:e.color},areaStyle:e.areaStyle,emphasis:{focus:"series",lineStyle:{width:(null!=(s=e.lineWidth)?s:a.lineWidth)+1}}}},Q=(e=!1)=>{const t={animation:!0,animationDuration:e?0:1300,animationDurationUpdate:e?0:1300,grid:T(a.showLegend&&M.value,a.legendPosition,{top:15,right:15,left:0}),tooltip:a.showTooltip?C():void 0,xAxis:{type:"category",boundaryGap:!1,data:a.xAxisData,axisTick:c(),axisLine:r(a.showAxisLine),axisLabel:s(a.showAxisLabel)},yAxis:{type:"value",min:0,max:V.value,axisLabel:s(a.showAxisLabel),axisLine:r(a.showAxisLine),splitLine:p(a.showSplitLine)}};if(a.showLegend&&M.value&&(t.legend=P(a.legendPosition)),M.value){const e=k.value;t.series=e.map(((e,t)=>{const o=R(a.colors[t],t),l=((e,t)=>{if(!e.areaStyle&&!e.showAreaColor&&!a.showAreaColor)return;const o=e.areaStyle||{};return o.custom?o.custom:{color:new y(0,0,0,1,[{offset:0,color:u(t,o.startOpacity||.2).rgba},{offset:1,color:u(t,o.endOpacity||.02).rgba}])}})(e,o);return H({name:e.name,data:e.data,color:o,smooth:e.smooth,symbol:e.symbol,lineWidth:e.lineWidth,areaStyle:l})}))}else{const e=k.value,o=R(a.colors[0]),l=(()=>{if(!a.showAreaColor)return;const e=R(a.colors[0]);return{color:new y(0,0,0,1,[{offset:0,color:u(e,.2).rgba},{offset:1,color:u(e,.02).rgba}])}})();t.series=[H({data:e,color:o,areaStyle:l})]}return t},U=e=>{I.value||l(e)},Z=()=>{if(I.value)k.value=J(),U(Q(!1));else if(z(),W.value=!0,M.value){const e=a.data;k.value=G(),U(Q(!0)),e.forEach(((e,t)=>{setTimeout((()=>{const a=k.value;a[t]=i({},e),k.value=[...a],U(Q(!1))}),t*a.animationDelay+100)}));const t=(e.length-1)*a.animationDelay+1500;setTimeout((()=>{W.value=!1}),t)}else k.value=G(),U(Q(!0)),E.value=setTimeout((()=>{k.value=J(),U(Q(!1)),W.value=!1}),100)},q=()=>{Z()},F=()=>{Z()};return b([()=>a.data,()=>a.xAxisData,()=>a.colors],(()=>{W.value||q()}),{deep:!0}),b(o,(()=>{var e;const a=(null==(e=t.value)?void 0:e.__echart__)||v(t.value);if(a&&!I.value){const e=Q(!1);a.setOption(e)}})),x((()=>{q(),t.value&&t.value.addEventListener("chartVisible",F)})),w((()=>{z(),t.value&&t.value.removeEventListener("chartVisible",F)})),(e,o)=>{const l=f,r=L;return A((_(),S("div",{ref_key:"chartRef",ref:t,class:"art-line-chart",style:O({height:a.height})},[B(I)?(_(),j(l,{key:0})):D("",!0)],4)),[[r,a.loading]])}}})),[["__scopeId","data-v-79c89510"]]),E={class:"card art-custom-card"},k=T(p({__name:"SalesOverview",setup(e){const a=[50,25,40,20,70,35,65,30,35,20,40,44],t=["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"];return(e,o)=>{const l=W;return _(),S("div",E,[o[0]||(o[0]=C('<div class="card-header" data-v-ff2928a7><div class="title" data-v-ff2928a7><h4 class="box-title" data-v-ff2928a7>访问量</h4><p class="subtitle" data-v-ff2928a7>今年增长<span class="text-success" data-v-ff2928a7>+15%</span></p></div></div>',1)),P(l,{class:"chart",height:"calc(100% - 40px)",data:a,xAxisData:t,showAreaColor:!0,showAxisLine:!1})])}}}),[["__scopeId","data-v-ff2928a7"]]);export{k as default};
