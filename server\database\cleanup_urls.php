<?php
/**
 * 清理数据库中的完整URL，只保留相对路径
 * 执行前请备份数据库！
 */

// 简单的数据库连接方式
$host = '127.0.0.1';
$dbname = 'fanshop';
$username = 'root';
$password = '123456';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
    exit(1);
}

try {
    echo "开始清理数据库URL...\n";

    // 1. 清理 fs_upload 表的 file_url 字段
    echo "\n1. 清理 fs_upload 表...\n";
    $stmt = $pdo->prepare("UPDATE fs_upload SET file_url = NULL WHERE file_url IS NOT NULL");
    $stmt->execute();
    $uploadCount = $stmt->rowCount();
    echo "✓ 清理了 {$uploadCount} 条上传文件记录\n";

    // 2. 清理 fs_system_config 表中的 site_logo 字段
    echo "\n2. 清理系统配置中的logo...\n";
    $stmt = $pdo->prepare("SELECT config_value FROM fs_system_config WHERE config_key = 'site_logo' AND config_value LIKE 'http%'");
    $stmt->execute();
    $logoRecord = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($logoRecord) {
        $oldValue = $logoRecord['config_value'];
        $newValue = preg_replace('/^https?:\/\/[^\/]+\//', '', $oldValue);

        $stmt = $pdo->prepare("UPDATE fs_system_config SET config_value = ? WHERE config_key = 'site_logo'");
        $stmt->execute([$newValue]);

        echo "✓ Logo路径: {$oldValue} -> {$newValue}\n";
    } else {
        echo "✓ Logo配置无需清理\n";
    }
    
    // 3. 清理 fs_system_config 表中的 site_favicon 字段
    echo "\n3. 清理系统配置中的favicon...\n";
    $stmt = $pdo->prepare("SELECT config_value FROM fs_system_config WHERE config_key = 'site_favicon' AND config_value LIKE 'http%'");
    $stmt->execute();
    $faviconRecord = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($faviconRecord) {
        $oldValue = $faviconRecord['config_value'];
        $newValue = preg_replace('/^https?:\/\/[^\/]+\//', '', $oldValue);

        $stmt = $pdo->prepare("UPDATE fs_system_config SET config_value = ? WHERE config_key = 'site_favicon'");
        $stmt->execute([$newValue]);

        echo "✓ Favicon路径: {$oldValue} -> {$newValue}\n";
    } else {
        echo "✓ Favicon配置无需清理\n";
    }

    // 4. 清理管理员头像字段
    echo "\n4. 清理管理员头像...\n";
    $stmt = $pdo->prepare("UPDATE fs_admin SET avatar = REPLACE(REPLACE(REPLACE(avatar, 'http://localhost:8000/', ''), 'http://fanshop.gg/', ''), 'https://fanshop.gg/', '') WHERE avatar LIKE 'http%'");
    $stmt->execute();
    $adminCount = $stmt->rowCount();
    echo "✓ 清理了 {$adminCount} 个管理员头像\n";
    
    // 5. 显示清理结果
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "清理结果统计:\n";
    echo str_repeat("=", 50) . "\n";

    // fs_upload 表统计
    $stmt = $pdo->prepare("SELECT COUNT(*) as total, SUM(CASE WHEN file_url IS NULL THEN 1 ELSE 0 END) as cleaned FROM fs_upload");
    $stmt->execute();
    $uploadStats = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "fs_upload表: 总记录 {$uploadStats['total']} 条, 已清理 {$uploadStats['cleaned']} 条\n";

    // 系统配置统计
    $stmt = $pdo->prepare("SELECT config_key, config_value FROM fs_system_config WHERE config_key IN ('site_logo', 'site_favicon')");
    $stmt->execute();
    $systemConfigs = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($systemConfigs as $config) {
        $status = strpos($config['config_value'], 'http') === 0 ? '❌ 仍包含完整URL' : '✅ 已清理为相对路径';
        echo "系统配置 {$config['config_key']}: {$config['config_value']} {$status}\n";
    }

    // 管理员头像统计
    $stmt = $pdo->prepare("SELECT COUNT(*) as total, SUM(CASE WHEN avatar NOT LIKE 'http%' OR avatar IS NULL THEN 1 ELSE 0 END) as cleaned FROM fs_admin");
    $stmt->execute();
    $adminStats = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "fs_admin表: 总记录 {$adminStats['total']} 条, 已清理 {$adminStats['cleaned']} 条\n";
    
    echo "\n✅ 数据库URL清理完成！\n";
    echo "💡 提示: 现在所有文件URL都将由后端动态生成，自动适应当前域名。\n";
    
} catch (Exception $e) {
    echo "❌ 清理过程中出现错误: " . $e->getMessage() . "\n";
    echo "请检查数据库连接和权限设置。\n";
    exit(1);
}
