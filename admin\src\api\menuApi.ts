import { asyncRoutes } from '@/router/routes/asyncRoutes'
import { menuDataToRouter } from '@/router/utils/menuToRouter'
import { AppRouteRecord } from '@/types/router'
import api from '@/utils/http'

interface MenuResponse {
  menuList: AppRouteRecord[]
}

// 菜单表单数据接口 - 匹配数据库字段
interface MenuFormData {
  name: string              // 路由名称
  title: string             // 菜单标题
  path: string              // 路由路径
  component?: string        // 组件路径
  icon?: string             // 图标
  sort?: number             // 排序
  type?: number             // 类型：1菜单，2按钮
  status?: number           // 状态：1显示，0隐藏
  keep_alive?: number       // 是否缓存
  external_link?: string    // 外部链接
  is_iframe?: number        // 是否iframe
  parent_id?: number        // 父级ID
  // 权限相关
  authName?: string
  authLabel?: string
  authIcon?: string
  authSort?: number
}

// 菜单接口
export const menuService = {
  async getMenuList(delay = 300): Promise<MenuResponse> {
    try {
      // 检查权限模式
      const accessMode = import.meta.env.VITE_ACCESS_MODE

      if (accessMode === 'backend') {
        // 后端控制模式：调用真实的后端接口
        const response = await api.get<MenuResponse>({ url: '/menu/list' })
        return response
      } else {
        // 前端控制模式：使用本地路由配置
        const menuData = asyncRoutes
        // 处理菜单数据
        const menuList = menuData.map((route) => menuDataToRouter(route))
        // 模拟接口延迟
        await new Promise((resolve) => setTimeout(resolve, delay))

        return { menuList }
      }
    } catch (error) {
      console.error('获取菜单失败:', error)
      throw error instanceof Error ? error : new Error('获取菜单失败')
    }
  },

  // 创建菜单
  async createMenu(data: MenuFormData): Promise<AppRouteRecord> {
    try {
      const response = await api.post<AppRouteRecord>({
        url: '/menu/create',
        data
      })
      return response
    } catch (error) {
      console.error('创建菜单失败:', error)
      throw error instanceof Error ? error : new Error('创建菜单失败')
    }
  },

  // 更新菜单
  async updateMenu(id: number, data: Partial<MenuFormData>): Promise<AppRouteRecord> {
    try {
      const response = await api.put<AppRouteRecord>({
        url: `/menu/update/${id}`,
        data
      })
      return response
    } catch (error) {
      console.error('更新菜单失败:', error)
      throw error instanceof Error ? error : new Error('更新菜单失败')
    }
  },

  // 删除菜单
  async deleteMenu(id: number): Promise<void> {
    try {
      await api.del<void>({
        url: `/menu/delete/${id}`
      })
    } catch (error) {
      console.error('删除菜单失败:', error)
      throw error instanceof Error ? error : new Error('删除菜单失败')
    }
  }
}
