var e=(e,a,l)=>new Promise(((t,i)=>{var s=e=>{try{o(l.next(e))}catch(a){i(a)}},r=e=>{try{o(l.throw(e))}catch(a){i(a)}},o=e=>e.done?t(e.value):Promise.resolve(e.value).then(s,r);o((l=l.apply(e,a)).next())}));import{E as a,b as l,A as t}from"./index-tBKMHRR1.js";/* empty css                   *//* empty css                  *//* empty css                */import"./el-form-item-l0sNRNKZ.js";/* empty css                      *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                    *//* empty css                  */import{m as i,d as s,r,M as o,w as u,o as n,C as d,D as c,G as p,R as m,x as v,a6 as f,W as g,P as y,F as _,$ as h,a5 as C,V as k,ba as b,aa as x,u as w,bb as V,a3 as z,am as j,X as M,aJ as B,a_ as S,ak as U,a8 as T,a2 as $,E as D,aI as A,S as I,bc as L,bd as P,ac as E,be as W}from"./vendor-8T3zXQLl.js";import{_ as F}from"./_plugin-vue_export-helper-BCo6x5W8.js";class G{static getMediaList(e={}){return a.get({url:"/media",params:e})}static uploadMedia(e,l=0){const t=new FormData;return t.append("file",e),t.append("category_id",l.toString()),a.post({url:"/media/upload",data:t,headers:{"Content-Type":"multipart/form-data"}})}static deleteMedia(e){return a.del({url:`/media/${e}`})}static batchDeleteMedia(e){return a.post({url:"/media/batch-delete",data:{ids:e}})}static moveToCategory(e,l){return a.post({url:"/media/move-to-category",data:{ids:e,category_id:l}})}static getCategories(){return a.get({url:"/media/categories"})}static createCategory(e,l=0){return a.post({url:"/media/categories",data:{name:e,parent_id:l}})}static updateCategory(e,l){return a.put({url:`/media/categories/${e}`,data:{name:l}})}static deleteCategory(e){return a.del({url:`/media/categories/${e}`})}}const J={class:"media-picker"},R={class:"category-sidebar"},H={class:"category-header"},K={class:"category-list"},O=["onClick"],X={class:"category-name"},Z={class:"file-count"},q={class:"content-area"},N={class:"toolbar"},Q={class:"toolbar-left"},Y={class:"toolbar-right"},ee={class:"file-count-info"},ae={class:"file-grid"},le=["onClick"],te={class:"file-preview"},ie=["src","alt"],se={key:1,class:"file-icon"},re={class:"file-ext"},oe={class:"file-info"},ue=["title"],ne={class:"file-meta"},de={class:"file-actions"},ce={class:"pagination-wrapper"},pe={class:"dialog-footer"},me=F(i({__name:"index",props:{modelValue:{type:Boolean},multiple:{type:Boolean,default:!1},accept:{default:"image/*"},maxSize:{default:10},title:{default:"选择图片"},width:{default:"60%"},defaultCategory:{default:0}},emits:["update:modelValue","confirm"],setup(a,{emit:i}){const F=a,me=i,ve=l(),fe=s({get:()=>F.modelValue,set:e=>me("update:modelValue",e)}),ge=r(!1),ye=r([]),_e=r([]),he=r([]),Ce=r(null),ke=r(""),be=r(1),xe=r(20),we=r(0),Ve=r(!1),ze=o({name:""}),je=r("http://fanshop.gg/adminapi/media/upload"),Me=r({Authorization:`Bearer ${ve.accessToken}`}),Be=()=>e(this,null,(function*(){try{const e=yield G.getCategories();ye.value=Array.isArray(e)?e:[],ye.value.length>0&&(Ce.value=null)}catch(e){D.error("获取分类失败")}})),Se=()=>e(this,null,(function*(){ge.value=!0;try{const e=yield G.getMediaList({page:be.value,limit:xe.value,category_id:Ce.value,keyword:ke.value,file_type:F.accept.includes("image")?"image":""});_e.value=e.list||[],we.value=e.total||0}catch(e){D.error("获取文件列表失败")}finally{ge.value=!1}})),Ue=()=>{be.value=1,Se()},Te=()=>{Se()},$e=()=>{be.value=1,Se()},De=()=>{he.value=[]},Ae=()=>{fe.value=!1,he.value=[]},Ie=()=>{me("confirm",he.value),Ae()},Le=e=>{if(F.accept&&!F.accept.includes("*")){const a=F.accept.split(",").map((e=>e.trim())),l=e.type;if(!a.some((e=>e.endsWith("/*")?l.startsWith(e.replace("/*","/")):l===e)))return D.error("不支持的文件类型"),!1}return!!(e.size/1024/1024<F.maxSize)||(D.error(`文件大小不能超过 ${F.maxSize}MB`),!1)},Pe=e=>{200===e.code?(D.success("上传成功"),Se()):D.error(e.msg||e.message||"上传失败")},Ee=a=>e(this,null,(function*(){try{yield A.confirm("确定要删除这个文件吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield G.deleteMedia(a),D.success("删除成功"),Se(),he.value=he.value.filter((e=>e.id!==a))}catch(e){"cancel"!==e&&D.error("删除失败")}})),We=()=>e(this,null,(function*(){try{yield A.confirm(`确定要删除选中的 ${he.value.length} 个文件吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=he.value.map((e=>e.id));yield G.batchDeleteMedia(e),D.success("批量删除成功"),Se(),he.value=[]}catch(e){"cancel"!==e&&D.error("批量删除失败")}})),Fe=()=>e(this,null,(function*(){if(ze.name.trim())try{yield G.createCategory(ze.name),D.success("创建成功"),Ve.value=!1,ze.name="",Be()}catch(e){D.error("创建失败")}else D.error("请输入分类名称")})),Ge=e=>{e.target.style.display="none"};return u(fe,(e=>{e&&(Be(),Se())})),n((()=>{fe.value&&(Be(),Se())})),(e,a)=>{const l=f,i=x,s=b,r=z,o=S,u=$,n=T,D=U,A=B;return c(),d(D,{modelValue:fe.value,"onUpdate:modelValue":a[7]||(a[7]=e=>fe.value=e),title:F.title,width:F.width,"before-close":Ae,class:"media-picker-dialog"},{footer:p((()=>[m("div",pe,[v(l,{onClick:Ae},{default:p((()=>a[13]||(a[13]=[g("取消")]))),_:1,__:[13]}),v(l,{type:"primary",disabled:0===he.value.length,onClick:Ie},{default:p((()=>a[14]||(a[14]=[g(" 确定 ")]))),_:1,__:[14]},8,["disabled"])])])),default:p((()=>[m("div",J,[m("div",R,[m("div",H,[a[9]||(a[9]=m("span",null,"分类",-1)),v(l,{type:"primary",size:"small",onClick:a[0]||(a[0]=e=>Ve.value=!0)},{default:p((()=>a[8]||(a[8]=[g(" 新建 ")]))),_:1,__:[8]})]),m("div",K,[(c(!0),y(_,null,h(ye.value,(e=>(c(),y("div",{key:e.id,class:I(["category-item",{active:Ce.value===e.id}]),onClick:a=>{return l=e.id,Ce.value=l,be.value=1,void Se();var l}},[v(i,null,{default:p((()=>[v(w(L))])),_:1}),m("span",X,M(e.name),1),m("span",Z,"("+M(e.file_count)+")",1)],10,O)))),128))])]),m("div",q,[m("div",N,[m("div",Q,[v(s,{action:je.value,headers:Me.value,data:{category_id:Ce.value},"show-file-list":!1,"on-success":Pe,"before-upload":Le,multiple:""},{default:p((()=>[v(l,{type:"primary"},{default:p((()=>[v(i,null,{default:p((()=>[v(w(V))])),_:1}),a[10]||(a[10]=g(" 本地上传 "))])),_:1,__:[10]})])),_:1},8,["action","headers","data"]),he.value.length>0?(c(),d(l,{key:0,type:"danger",onClick:We},{default:p((()=>a[11]||(a[11]=[g(" 删除选中 ")]))),_:1,__:[11]})):k("",!0)]),m("div",Y,[v(r,{modelValue:ke.value,"onUpdate:modelValue":a[1]||(a[1]=e=>ke.value=e),placeholder:"搜索文件名",style:{width:"200px"},onInput:Ue},{suffix:p((()=>[v(i,null,{default:p((()=>[v(w(j))])),_:1})])),_:1},8,["modelValue"]),m("span",ee," 已选择 "+M(he.value.length)+" / "+M(e.multiple?"∞":1),1),v(l,{onClick:De},{default:p((()=>a[12]||(a[12]=[g("清空")]))),_:1,__:[12]})])]),C((c(),y("div",ae,[(c(!0),y(_,null,h(_e.value,(e=>{return c(),y("div",{key:e.id,class:I(["file-item",{selected:(s=e.id,he.value.some((e=>e.id===s)))}]),onClick:a=>(e=>{const a=he.value.findIndex((a=>a.id===e.id));a>-1?he.value.splice(a,1):F.multiple?he.value.push(e):he.value=[e]})(e)},[m("div",te,[e.is_image?(c(),y("img",{key:0,src:w(t)(e.file_url),alt:e.original_name,onError:Ge},null,40,ie)):(c(),y("div",se,[v(i,{size:"40"},{default:p((()=>[v(w(P))])),_:1}),m("span",re,M(e.file_type.toUpperCase()),1)]))]),m("div",oe,[m("div",{class:"file-name",title:e.original_name},M(e.original_name),9,ue),m("div",ne,[m("span",null,M(e.file_size_formatted),1),m("span",null,M((a=e.created_at,new Date(a).toLocaleDateString())),1)])]),m("div",de,[v(l,{type:"danger",size:"small",circle:"",onClick:E((a=>Ee(e.id)),["stop"])},{default:p((()=>[v(i,null,{default:p((()=>[v(w(W))])),_:1})])),_:2},1032,["onClick"])])],10,le);var a,s})),128))])),[[A,ge.value]]),m("div",ce,[v(o,{"current-page":be.value,"onUpdate:currentPage":a[2]||(a[2]=e=>be.value=e),"page-size":xe.value,"onUpdate:pageSize":a[3]||(a[3]=e=>xe.value=e),total:we.value,"page-sizes":[20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:$e,onCurrentChange:Te},null,8,["current-page","page-size","total"])])])]),v(D,{modelValue:Ve.value,"onUpdate:modelValue":a[6]||(a[6]=e=>Ve.value=e),title:"创建分类",width:"400px"},{footer:p((()=>[v(l,{onClick:a[5]||(a[5]=e=>Ve.value=!1)},{default:p((()=>a[15]||(a[15]=[g("取消")]))),_:1,__:[15]}),v(l,{type:"primary",onClick:Fe},{default:p((()=>a[16]||(a[16]=[g("确定")]))),_:1,__:[16]})])),default:p((()=>[v(n,{model:ze,"label-width":"80px"},{default:p((()=>[v(u,{label:"分类名称"},{default:p((()=>[v(r,{modelValue:ze.name,"onUpdate:modelValue":a[4]||(a[4]=e=>ze.name=e),placeholder:"请输入分类名称"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])),_:1},8,["modelValue","title","width"])}}}),[["__scopeId","data-v-98541981"]]);export{me as M};
