<?php
declare(strict_types=1);

namespace app\adminapi\controller;

use app\common\controller\BaseController;
use app\common\model\Admin;
use app\common\service\AuthService;
use app\adminapi\validate\AdminValidate;

/**
 * 管理员认证控制器
 */
class Auth extends BaseController
{
    // 定义中间件 - 使用完整类名
    protected $middleware = [
        \app\adminapi\middleware\Auth::class => ['except' => ['login', 'refresh']],
    ];
    /**
     * 管理员登录
     */
    public function login()
    {
        $data = $this->request->post();
        
        // 验证参数
        $validate = new AdminValidate();
        if (!$validate->scene('login')->check($data)) {
            return $this->error($validate->getError());
        }

        try {
            $result = AuthService::adminLogin($data['userName'], $data['password']);
            return $this->success($result, '登录成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取用户信息
     */
    public function info()
    {
        try {
            // 从请求中获取用户信息（由Auth中间件注入）
            $user = $this->request->user ?? null;

            if (!$user) {
                return $this->error('用户未登录', 401);
            }

            // 使用AuthService获取完整的用户信息
            $userInfo = AuthService::getCurrentUser($user['id'], $user['type'] ?? 'admin');

            return $this->success($userInfo);
        } catch (\Exception $e) {
            return $this->error('获取用户信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 退出登录
     */
    public function logout()
    {
        // 可以在这里处理token黑名单等逻辑
        return $this->success([], '退出成功');
    }

    /**
     * 刷新Token
     */
    public function refresh()
    {
        $refreshToken = $this->request->post('refreshToken');
        
        if (!$refreshToken) {
            return $this->error('刷新Token不能为空');
        }

        try {
            $result = AuthService::refreshToken($refreshToken);
            return $this->success($result, '刷新成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
