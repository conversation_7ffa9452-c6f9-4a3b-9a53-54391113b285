var e=Object.defineProperty,t=Object.defineProperties,r=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,a=Object.prototype.propertyIsEnumerable,n=(t,r,o)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[r]=o;import{_ as l}from"./ArtResultPage-Bt5HUkvI.js";import"./index-DG9-1w7X.js";/* empty css                  */import{k as p,Z as i,C as c,D as u,G as f,a5 as m,a6 as y,W as b,R as _}from"./vendor-84Inc-Pt.js";import"./_plugin-vue_export-helper-BCo6x5W8.js";const j=p((d=((e,t)=>{for(var r in t||(t={}))s.call(t,r)&&n(e,r,t[r]);if(o)for(var r of o(t))a.call(t,r)&&n(e,r,t[r]);return e})({},{name:"ResultFail"}),t(d,r({__name:"index",setup:e=>(e,t)=>{const r=y,o=l,s=i("ripple");return u(),c(o,{type:"fail",title:"提交失败",message:"请核对并修改以下信息后，再重新提交。",iconCode:""},{content:f((()=>t[0]||(t[0]=[_("p",null,"您提交的内容有如下错误：",-1),_("p",null,[_("i",{class:"icon iconfont-sys"},""),b("您的账户已被冻结")],-1),_("p",null,[_("i",{class:"icon iconfont-sys"},""),b("您的账户还不具备申请资格")],-1)]))),buttons:f((()=>[m((u(),c(r,{type:"primary"},{default:f((()=>t[1]||(t[1]=[b("返回修改")]))),_:1,__:[1]})),[[s]]),m((u(),c(r,null,{default:f((()=>t[2]||(t[2]=[b("查看")]))),_:1,__:[2]})),[[s]])])),_:1})}}))));var d;export{j as default};
