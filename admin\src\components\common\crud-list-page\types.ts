import type { ColumnOption } from '@/types/component'
import type { SearchFormItem } from '@/types'

// 基础数据类型
export type CrudFormData = Record<string, any>

// 表单字段配置
export interface FormFieldConfig {
  prop: string
  label: string
  type: 'input' | 'select' | 'radio' | 'checkbox' | 'date' | 'daterange' | 'textarea' | 'number' | 'switch' | 'editor' | 'upload' | 'password'
  required?: boolean
  placeholder?: string
  options?: Array<{ label: string; value: any; disabled?: boolean }> | (() => Array<{ label: string; value: any; disabled?: boolean }>)
  rules?: any[]
  span?: number // 栅格占位
  config?: Record<string, any> // 组件特定配置
}

// API配置
export interface CrudApiConfig<T = any> {
  list: (params: any) => Promise<any>
  create?: (data: CrudFormData) => Promise<any>
  update?: (id: string | number, data: CrudFormData) => Promise<any>
  delete?: (id: string | number) => Promise<any>
  detail?: (id: string | number) => Promise<T>
}

// 搜索配置
export interface SearchConfig {
  enabled: boolean
  items?: SearchFormItem[]
  defaultParams?: Record<string, any>
  layout?: {
    labelWidth?: string
    gutter?: number
    span?: number
  }
}

// 操作按钮配置
export interface ActionButtonConfig {
  type: 'edit' | 'delete' | 'view' | 'custom' | 'more' | 'add'
  text?: string
  icon?: string
  permission?: string
  handler?: (row: any) => void
}

// 操作配置
export interface ActionsConfig {
  enabled: boolean
  width?: number
  create?: {
    enabled: boolean
    text?: string
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
    permission?: string
  }
  edit?: {
    enabled: boolean
    permission?: string
  }
  delete?: {
    enabled: boolean
    permission?: string
    confirmText?: string
  }
  custom?: ActionButtonConfig[]
}

// 弹窗配置
export interface DialogConfig {
  enabled: boolean
  width?: string
  titles?: {
    create?: string
    edit?: string
  }
  formConfig: FormFieldConfig[]
  rules?: Record<string, any[]>
  layout?: {
    labelWidth?: string
    gutter?: number
  }
}

// 表格配置
export interface TableConfig {
  rowKey?: string
  stripe?: boolean
  border?: boolean
  size?: 'large' | 'default' | 'small'
  height?: string | number
  maxHeight?: string | number
  showHeader?: boolean
  highlightCurrentRow?: boolean
  emptyText?: string
}

// 主配置接口
export interface CrudListConfig<T = any> {
  // API配置
  api: CrudApiConfig<T>
  
  // 表格列配置
  columns: ColumnOption<T>[]
  
  // 搜索配置
  search?: SearchConfig
  
  // 操作配置
  actions?: ActionsConfig
  
  // 弹窗配置
  dialog?: DialogConfig
  
  // 表格配置
  table?: TableConfig
  
  // 其他配置
  autoLoad?: boolean // 是否自动加载数据
  pageSize?: number // 默认每页条数
}

// 预设配置模板
export const createUserListConfig = (): CrudListConfig => ({
  api: {
    list: () => Promise.resolve([]),
    create: () => Promise.resolve(),
    update: () => Promise.resolve(),
    delete: () => Promise.resolve()
  },
  columns: [
    { type: 'selection' },
    { type: 'index', width: 60, label: '序号' },
    { prop: 'username', label: '用户名', minWidth: 120 },
    { prop: 'phone', label: '手机号', width: 120 },
    { prop: 'status', label: '状态', width: 80 },
    { prop: 'createTime', label: '创建时间', width: 160 }
  ],
  search: {
    enabled: true,
    items: [
      {
        label: '用户名',
        prop: 'username',
        type: 'input',
        config: { clearable: true }
      },
      {
        label: '手机号',
        prop: 'phone',
        type: 'input',
        config: { clearable: true }
      },
      {
        label: '状态',
        prop: 'status',
        type: 'select',
        options: () => [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 }
        ]
      }
    ]
  },
  actions: {
    enabled: true,
    create: {
      enabled: true,
      text: '新增用户'
    },
    edit: {
      enabled: true
    },
    delete: {
      enabled: true,
      confirmText: '确定要删除这个用户吗？'
    }
  },
  dialog: {
    enabled: true,
    width: '600px',
    titles: {
      create: '新增用户',
      edit: '编辑用户'
    },
    formConfig: [
      {
        prop: 'username',
        label: '用户名',
        type: 'input',
        required: true,
        span: 12
      },
      {
        prop: 'phone',
        label: '手机号',
        type: 'input',
        required: true,
        span: 12
      },
      {
        prop: 'email',
        label: '邮箱',
        type: 'input',
        span: 12
      },
      {
        prop: 'status',
        label: '状态',
        type: 'switch',
        span: 12
      }
    ]
  }
})

// 角色管理配置模板
export const createRoleListConfig = (): CrudListConfig => ({
  api: {
    list: () => Promise.resolve([]),
    create: () => Promise.resolve(),
    update: () => Promise.resolve(),
    delete: () => Promise.resolve()
  },
  columns: [
    { type: 'selection' },
    { type: 'index', width: 60, label: '序号' },
    { prop: 'roleName', label: '角色名称', minWidth: 120 },
    { prop: 'roleCode', label: '角色编码', width: 120 },
    { prop: 'description', label: '描述', minWidth: 200 },
    { prop: 'status', label: '状态', width: 80 },
    { prop: 'createTime', label: '创建时间', width: 160 }
  ],
  search: {
    enabled: true,
    items: [
      {
        label: '角色名称',
        prop: 'roleName',
        type: 'input',
        config: { clearable: true }
      },
      {
        label: '角色编码',
        prop: 'roleCode',
        type: 'input',
        config: { clearable: true }
      }
    ]
  },
  actions: {
    enabled: true,
    create: {
      enabled: true,
      text: '新增角色'
    },
    edit: {
      enabled: true
    },
    delete: {
      enabled: true,
      confirmText: '确定要删除这个角色吗？'
    },
    custom: [
      {
        type: 'custom',
        text: '权限设置',
        handler: (row) => {
          console.log('设置权限:', row)
        }
      }
    ]
  },
  dialog: {
    enabled: true,
    width: '600px',
    titles: {
      create: '新增角色',
      edit: '编辑角色'
    },
    formConfig: [
      {
        prop: 'roleName',
        label: '角色名称',
        type: 'input',
        required: true,
        span: 12
      },
      {
        prop: 'roleCode',
        label: '角色编码',
        type: 'input',
        required: true,
        span: 12
      },
      {
        prop: 'description',
        label: '描述',
        type: 'textarea',
        span: 24
      },
      {
        prop: 'status',
        label: '状态',
        type: 'switch',
        span: 12
      }
    ]
  }
})
