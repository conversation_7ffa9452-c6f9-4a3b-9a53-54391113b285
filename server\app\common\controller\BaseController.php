<?php
declare(strict_types=1);

namespace app\common\controller;

use think\App;
use think\Response;

/**
 * 基础控制器类
 */
class BaseController
{
    /**
     * Request实例
     * @var \think\Request
     */
    protected $request;

    /**
     * 应用实例
     * @var \think\App
     */
    protected $app;

    /**
     * 构造方法
     * @param App $app
     */
    public function __construct(App $app)
    {
        $this->app = $app;
        $this->request = $this->app->request;
    }

    /**
     * 成功响应
     * @param array $data 响应数据
     * @param string $msg 响应消息
     * @param int $code 响应码
     * @return Response
     */
    protected function success($data = [], string $msg = '操作成功', int $code = 200): Response
    {
        return json([
            'code' => $code,
            'msg' => $msg,
            'data' => $data
        ]);
    }

    /**
     * 错误响应
     * @param string $msg 错误消息
     * @param int $code 错误码
     * @param array $data 响应数据
     * @return Response
     */
    protected function error(string $msg = '操作失败', int $code = 500, $data = []): Response
    {
        return json([
            'code' => $code,
            'msg' => $msg,
            'data' => $data
        ]);
    }

    /**
     * 分页响应
     * @param array $list 数据列表
     * @param int $total 总数
     * @param int $current 当前页
     * @param int $size 每页条数
     * @return Response
     */
    protected function paginate(array $list, int $total, int $current, int $size): Response
    {
        return $this->success([
            'records' => $list,
            'total' => $total,
            'current' => $current,
            'size' => $size
        ]);
    }
}
