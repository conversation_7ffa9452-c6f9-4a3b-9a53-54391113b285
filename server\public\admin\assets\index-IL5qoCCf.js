var r=Object.defineProperty,e=Object.defineProperties,o=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,m=Object.prototype.propertyIsEnumerable,i=(e,o,t)=>o in e?r(e,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[o]=t;import{p}from"./index-1JOyfOxu.js";/* empty css               *//* empty css               */import a from"./CardList-CS2SJjSt.js";import l from"./ActiveUser-Bx4FBypw.js";import j from"./SalesOverview-JiI6a-yi.js";import n from"./NewUser-CcdR9yRi.js";import d from"./Dynamic-CDPCzYha.js";import c from"./TodoList-BU0i9sEc.js";import f from"./AboutProject-BH8Aeug5.js";import{m as u,P as b,D as g,x as _,G as v,aK as O,aL as y}from"./vendor-8T3zXQLl.js";import{_ as x}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-Wowen7jt.js";/* empty css                   */import"./useChart-DbiZ5Fzz.js";import"./index-Zs-Thxwm.js";/* empty css                      *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 *//* empty css                    *//* empty css                       *//* empty css                        */import"./avatar6-C1kCQLrX.js";const w={class:"console"},P=u((h=((r,e)=>{for(var o in e||(e={}))s.call(e,o)&&i(r,o,e[o]);if(t)for(var o of t(e))m.call(e,o)&&i(r,o,e[o]);return r})({},{name:"Console"}),e(h,o({__name:"index",setup:r=>(p().scrollToTop(),(r,e)=>{const o=O,t=y;return g(),b("div",w,[_(a),_(t,{gutter:20},{default:v((()=>[_(o,{sm:24,md:12,lg:10},{default:v((()=>[_(l)])),_:1}),_(o,{sm:24,md:12,lg:14},{default:v((()=>[_(j)])),_:1})])),_:1}),_(t,{gutter:20},{default:v((()=>[_(o,{sm:24,md:24,lg:12},{default:v((()=>[_(n)])),_:1}),_(o,{sm:24,md:12,lg:6},{default:v((()=>[_(d)])),_:1}),_(o,{sm:24,md:12,lg:6},{default:v((()=>[_(c)])),_:1})])),_:1}),_(f)])})}))));var h;const L=x(P,[["__scopeId","data-v-699cd4c1"]]);export{L as default};
