# 自动路由系统说明

## 概述

自动路由系统可以根据文件结构自动生成路由配置，无需手动添加每个页面的路由。特别适用于编辑、详情、添加等功能页面。

## 工作原理

1. **扫描文件系统**：自动扫描 `src/views` 目录下的所有 `.vue` 文件
2. **生成路由配置**：根据文件路径和文件名自动生成路由配置
3. **应用路由参数**：根据页面类型自动添加路由参数（如 `:id?`）
4. **设置元数据**：根据页面类型自动设置路由元数据

## 支持的页面类型

### 1. 编辑页面 (`edit.vue`)
- **路由路径**：`/path/to/edit/:id?`
- **参数**：可选的 `id` 参数，支持新增和编辑两种模式
- **元数据**：`{ title: '编辑', isHideTab: true, requiresAuth: true }`

### 2. 详情页面 (`detail.vue`)
- **路由路径**：`/path/to/detail/:id`
- **参数**：必需的 `id` 参数
- **元数据**：`{ title: '详情', isHideTab: true, keepAlive: true }`

### 3. 添加页面 (`add.vue`)
- **路由路径**：`/path/to/add`
- **参数**：无参数
- **元数据**：`{ title: '添加', isHideTab: true, requiresAuth: true }`

### 4. 其他页面
- **路由路径**：`/path/to/pagename`
- **参数**：根据配置决定
- **元数据**：使用默认配置

## 文件结构示例

```
src/views/
├── system/
│   ├── article/
│   │   ├── list/
│   │   │   ├── index.vue      # 列表页面（菜单路由）
│   │   │   ├── edit.vue       # 编辑页面（自动路由）
│   │   │   ├── detail.vue     # 详情页面（自动路由）
│   │   │   └── add.vue        # 添加页面（自动路由）
│   │   └── category/
│   │       ├── index.vue      # 分类列表（菜单路由）
│   │       └── edit.vue       # 分类编辑（自动路由）
│   └── user/
│       ├── index.vue          # 用户列表（菜单路由）
│       ├── edit.vue           # 用户编辑（自动路由）
│       └── profile.vue        # 用户资料（自动路由）
```

## 生成的路由配置

基于上述文件结构，自动生成的路由：

```typescript
{
  path: '/system',
  component: () => import('@/views/index/index.vue'),
  name: 'SystemPages',
  children: [
    {
      path: '/system/article/list/edit/:id?',
      name: 'SystemArticleListEdit',
      component: () => import('@/views/system/article/list/edit.vue'),
      meta: { title: '编辑', isHideTab: true, requiresAuth: true }
    },
    {
      path: '/system/article/list/detail/:id',
      name: 'SystemArticleListDetail',
      component: () => import('@/views/system/article/list/detail.vue'),
      meta: { title: '详情', isHideTab: true, keepAlive: true }
    },
    // ... 其他路由
  ]
}
```

## 配置文件

### 基础配置 (`autoRouteConfig.ts`)

```typescript
export const autoRouteConfig = {
  enabled: true,                    // 启用自动路由
  excludePatterns: [               // 排除的文件模式
    '/index.vue',                  // 列表页面
    '/_',                          // 私有组件
    '/auth/',                      // 认证页面
    '/exception/',                 // 异常页面
  ],
  defaultMeta: {                   // 默认元数据
    isHideTab: true,
    requiresAuth: true,
    keepAlive: false,
  },
  specialPages: {                  // 特殊页面配置
    'edit': { title: '编辑', keepAlive: false },
    'detail': { title: '详情', keepAlive: true },
    'add': { title: '添加', keepAlive: false },
  }
}
```

## 使用方法

### 1. 创建新的功能页面

只需在相应目录下创建 `.vue` 文件，路由会自动生成：

```bash
# 创建文章编辑页面
touch src/views/system/article/list/edit.vue

# 创建用户详情页面  
touch src/views/system/user/detail.vue
```

### 2. 页面跳转

在列表页面中跳转到功能页面：

```typescript
// 跳转到编辑页面（新增）
router.push('/system/article/list/edit')

// 跳转到编辑页面（编辑）
router.push(`/system/article/list/edit/${id}`)

// 跳转到详情页面
router.push(`/system/article/list/detail/${id}`)
```

### 3. 页面内获取参数

在功能页面中获取路由参数：

```typescript
import { useRoute } from 'vue-router'

const route = useRoute()
const id = route.params.id as string

// 判断是新增还是编辑模式
const isEdit = !!id
```

## 调试信息

开发环境下，控制台会显示自动生成的路由信息：

```
🚀 自动路由生成信息
📊 总计生成路由: 5 个
📋 路由列表:
  1. /system/article/list/edit/:id?
  2. /system/article/list/detail/:id
  3. /system/user/edit/:id?
  4. /system/user/detail/:id
  5. /system/settings/config
```

## 注意事项

1. **列表页面**：`index.vue` 文件不会被自动路由处理，应该通过菜单系统配置
2. **私有组件**：以 `_` 开头的文件会被排除
3. **路由冲突**：避免创建与现有静态路由冲突的路径
4. **参数配置**：可以在配置文件中自定义参数规则
5. **元数据配置**：可以在配置文件中自定义页面元数据

## 扩展配置

如需添加新的页面类型或修改配置，编辑 `autoRouteConfig.ts` 文件：

```typescript
// 添加新的特殊页面类型
specialPages: {
  'preview': { title: '预览', keepAlive: false },
  'settings': { title: '设置', keepAlive: false },
}

// 添加新的参数规则
routeParamConfig: {
  optionalIdPages: ['edit', 'add', 'preview'],
  requiredIdPages: ['detail', 'view'],
}
```

这样就实现了一个灵活的自动路由系统，大大减少了手动配置路由的工作量！
