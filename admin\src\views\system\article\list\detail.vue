<template>
  <div class="article-detail-page">
    <ElCard class="page-header" shadow="never">
      <div class="header-content">
        <div class="title-section">
          <h2>文章详情</h2>
          <ElBreadcrumb separator="/">
            <ElBreadcrumbItem>系统管理</ElBreadcrumbItem>
            <ElBreadcrumbItem>文章管理</ElBreadcrumbItem>
            <ElBreadcrumbItem>文章详情</ElBreadcrumbItem>
          </ElBreadcrumb>
        </div>
        <div class="action-section">
          <ElButton @click="handleBack">返回</ElButton>
          <ElButton type="primary" @click="handleEdit">编辑</ElButton>
        </div>
      </div>
    </ElCard>

    <ElCard class="content" shadow="never">
      <div v-if="loading" class="loading">
        <ElSkeleton :rows="5" animated />
      </div>
      
      <div v-else-if="articleData" class="article-content">
        <h1>{{ articleData.title }}</h1>
        <div class="meta-info">
          <span>作者：{{ articleData.author }}</span>
          <span>发布时间：{{ articleData.created_at }}</span>
          <span>状态：{{ articleData.status === 1 ? '已发布' : '草稿' }}</span>
        </div>
        <div class="content-body" v-html="articleData.content"></div>
      </div>
      
      <div v-else class="empty">
        <ElEmpty description="文章不存在" />
      </div>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()

const loading = ref(true)
const articleData = ref<any>(null)

const articleId = route.params.id as string

onMounted(async () => {
  if (articleId) {
    await loadArticleDetail()
  } else {
    ElMessage.error('缺少文章ID参数')
    handleBack()
  }
})

const loadArticleDetail = async () => {
  try {
    loading.value = true
    // 这里应该调用API获取文章详情
    // const response = await ArticleService.getDetail(articleId)
    
    // 模拟数据
    setTimeout(() => {
      articleData.value = {
        id: articleId,
        title: '测试文章标题',
        author: '管理员',
        created_at: '2024-01-01 12:00:00',
        status: 1,
        content: '<p>这是文章内容...</p>'
      }
      loading.value = false
    }, 1000)
  } catch (error) {
    console.error('加载文章详情失败:', error)
    ElMessage.error('加载文章详情失败')
    loading.value = false
  }
}

const handleBack = () => {
  router.push('/system/article/list')
}

const handleEdit = () => {
  router.push(`/system/article/list/edit/${articleId}`)
}
</script>

<style scoped>
.article-detail-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section h2 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
}

.action-section {
  display: flex;
  gap: 12px;
}

.content {
  min-height: 400px;
}

.loading {
  padding: 20px;
}

.article-content h1 {
  margin: 0 0 16px 0;
  font-size: 24px;
  font-weight: 600;
}

.meta-info {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
  color: #666;
  font-size: 14px;
}

.content-body {
  line-height: 1.6;
}

.empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}
</style>
