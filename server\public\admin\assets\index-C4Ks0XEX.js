var e=Object.defineProperty,l=Object.defineProperties,a=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,s=(l,a,t)=>a in l?e(l,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[a]=t,n=(e,l)=>{for(var a in l||(l={}))r.call(l,a)&&s(e,a,l[a]);if(t)for(var a of t(l))o.call(l,a)&&s(e,a,l[a]);return e},i=(e,l,a)=>new Promise(((t,r)=>{var o=e=>{try{n(a.next(e))}catch(l){r(l)}},s=e=>{try{n(a.throw(e))}catch(l){r(l)}},n=e=>e.done?t(e.value):Promise.resolve(e.value).then(o,s);n((a=a.apply(e,l)).next())}));import{B as d,e as u,i as c}from"./index-DG9-1w7X.js";/* empty css                     *//* empty css                    *//* empty css                  *//* empty css                *//* empty css                  */import"./el-form-item-l0sNRNKZ.js";/* empty css                 *//* empty css                */import{_ as p}from"./index-CGmIUeDt.js";/* empty css                  */import{u as m,_ as h,A as f}from"./useTableColumns-BGewnWEG.js";import{k as g,r as v,s as y,c as b,M as _,d as k,E as x,Z as C,P as j,D as w,x as V,G as R,i as $,u as N,a5 as O,C as P,a6 as L,W as M,aQ as U,a8 as T,a2 as A,a3 as B,ap as D,R as K,ak as S,an as q,aT as E,X as I,l as z,aR as F,aB as G,aI as W}from"./vendor-84Inc-Pt.js";import{_ as J}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                   *//* empty css                      *//* empty css                  */import"./el-tooltip-l0sNRNKZ.js";import"./index-syD2yfUn.js";class Q{static getRoleList(){return d.get({url:"/role/list"})}static createRole(e){return d.post({url:"/role/create",data:e})}static updateRole(e,l){return d.put({url:`/role/update/${e}`,data:l})}static deleteRole(e){return d.del({url:`/role/delete/${e}`})}static setPermissions(e,l){return d.post({url:`/role/permissions/${e}`,data:l})}static getPermissions(e){return d.get({url:`/role/permissions/${e}`})}}const X={class:"role-page art-full-height"},Y={class:"dialog-footer"},Z={style:{display:"flex","align-items":"center"}},H={key:0},ee={key:1},le={class:"dialog-footer"};var ae;const te=J(g((ae=n({},{name:"Role"}),l(ae,a({__name:"index",setup(e){const l=v(!1),a=v(!1),{menuList:t}=y(u()),r=v(),o=v(!0),s=v(!1),d=v(!1),g=v([]),J=v(null),{columns:ae,columnChecks:te}=m((()=>[{type:"selection",width:50},{type:"index",label:"序号",width:80},{prop:"roleName",label:"角色名称",width:150},{prop:"roleCode",label:"角色编码",width:150},{prop:"des",label:"描述",minWidth:200},{prop:"enable",label:"状态",width:100,formatter:e=>z(F,{type:e.enable?"primary":"info"},(()=>e.enable?"启用":"禁用"))},{prop:"date",label:"创建时间",width:180,formatter:e=>_e(e.date)},{prop:"operation",label:"操作",width:180,fixed:"right",formatter:e=>{const l=[];return l.push(z(G,{content:"权限设置",placement:"top"},(()=>z(f,{type:"view",onClick:()=>ce(e)})))),l.push(z(G,{content:"编辑",placement:"top"},(()=>z(f,{type:"edit",onClick:()=>ue("edit",e)})))),l.push(z(G,{content:"删除",placement:"top"},(()=>z(f,{type:"delete",onClick:()=>me(e)})))),z("div",{class:"flex gap-2"},l)}}])),re=b((()=>{const e=l=>{const a=n({},l);if(l.meta&&l.meta.authList&&l.meta.authList.length){const e=l.meta.authList.map((e=>({id:`${l.id}_${e.authMark}`,name:`${l.name}_${e.authMark}`,label:e.title,authMark:e.authMark,isAuth:!0,checked:e.checked||!1})));a.children=a.children?[...a.children,...e]:e}return a.children&&(a.children=a.children.map(e)),a};return t.value.map(e)})),oe=v(),se=_({roleName:[{required:!0,message:"请输入角色名称",trigger:"blur"},{min:2,max:20,message:"长度在 2 到 20 个字符",trigger:"blur"}],roleCode:[{required:!0,message:"请输入角色编码",trigger:"blur"},{min:2,max:20,message:"长度在 2 到 20 个字符",trigger:"blur"}],des:[{required:!1,message:"请输入角色描述",trigger:"blur"}]}),ne=_({roleName:"",roleCode:"",des:"",enable:!0});k((()=>{ie()}));const ie=()=>i(this,null,(function*(){try{d.value=!0,g.value=yield Q.getRoleList()}catch(e){x.error("获取角色列表失败")}finally{d.value=!1}})),de=v("add"),ue=(e,a)=>{l.value=!0,de.value=e,"edit"===e&&a?(J.value=a,ne.roleName=a.roleName,ne.roleCode=a.roleCode,ne.des=a.des,ne.enable=a.enable):(J.value=null,ne.roleName="",ne.roleCode="",ne.des="",ne.enable=!0)},ce=e=>{J.value=e,a.value=!0},pe={children:"children",label:e=>{var l;return c(null==(l=e.meta)?void 0:l.title)||""}},me=e=>{W.confirm("确定删除该角色吗？","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}).then((()=>i(this,null,(function*(){var l,a;try{yield Q.deleteRole(e.id),x.success("删除成功"),ie()}catch(t){(null==(a=null==(l=t.response)?void 0:l.data)?void 0:a.message)?x.error(`删除失败: ${t.response.data.message}`):t.message?x.error(`删除失败: ${t.message}`):x.error("删除失败")}}))))},he=e=>i(this,null,(function*(){e&&(yield e.validate((a=>i(this,null,(function*(){if(a)try{"add"===de.value?(yield Q.createRole(ne),x.success("新增成功")):(yield Q.updateRole(J.value.id,ne),x.success("修改成功")),l.value=!1,e.resetFields(),ie()}catch(t){x.error("提交失败")}})))))})),fe=()=>{x.success("权限保存成功"),a.value=!1},ge=()=>{const e=r.value;if(!e)return;const l=e.store.nodesMap;for(const a in l)l[a].expanded=!o.value;o.value=!o.value},ve=()=>{const e=r.value;if(e){if(s.value)e.setCheckedKeys([]);else{const l=ye(re.value);e.setCheckedKeys(l)}s.value=!s.value}},ye=e=>{const l=[],a=e=>{e.forEach((e=>{e.name&&l.push(e.name),e.children&&e.children.length>0&&a(e.children)}))};return a(e),l},be=()=>{const e=r.value;if(!e)return;const l=e.getCheckedKeys(),a=ye(re.value);s.value=l.length===a.length&&a.length>0},_e=e=>new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}).replace(/\//g,"-");return(e,t)=>{const n=L,i=p,u=U,c=B,m=A,f=D,v=T,y=S,b=E,_=q,k=C("ripple");return w(),j("div",X,[V(u,{class:"art-table-card",shadow:"never"},{default:R((()=>[V(h,{columns:N(te),"onUpdate:columns":t[1]||(t[1]=e=>$(te)?te.value=e:null),onRefresh:ie},{left:R((()=>[O((w(),P(n,{onClick:t[0]||(t[0]=e=>ue("add")),type:"primary"},{default:R((()=>t[10]||(t[10]=[M("新增角色")]))),_:1,__:[10]})),[[k]])])),_:1},8,["columns"]),V(i,{loading:N(d),data:N(g),columns:N(ae),"table-config":{rowKey:"id"},layout:{marginTop:10}},null,8,["loading","data","columns"])])),_:1}),V(y,{modelValue:N(l),"onUpdate:modelValue":t[8]||(t[8]=e=>$(l)?l.value=e:null),title:"add"===N(de)?"新增角色":"编辑角色",width:"30%","align-center":""},{footer:R((()=>[K("div",Y,[V(n,{onClick:t[6]||(t[6]=e=>l.value=!1)},{default:R((()=>t[11]||(t[11]=[M("取消")]))),_:1,__:[11]}),V(n,{type:"primary",onClick:t[7]||(t[7]=e=>he(N(oe)))},{default:R((()=>t[12]||(t[12]=[M("提交")]))),_:1,__:[12]})])])),default:R((()=>[V(v,{ref_key:"formRef",ref:oe,model:N(ne),rules:N(se),"label-width":"120px"},{default:R((()=>[V(m,{label:"角色名称",prop:"roleName"},{default:R((()=>[V(c,{modelValue:N(ne).roleName,"onUpdate:modelValue":t[2]||(t[2]=e=>N(ne).roleName=e)},null,8,["modelValue"])])),_:1}),V(m,{label:"角色编码",prop:"roleCode"},{default:R((()=>[V(c,{modelValue:N(ne).roleCode,"onUpdate:modelValue":t[3]||(t[3]=e=>N(ne).roleCode=e)},null,8,["modelValue"])])),_:1}),V(m,{label:"描述",prop:"roleStatus"},{default:R((()=>[V(c,{modelValue:N(ne).des,"onUpdate:modelValue":t[4]||(t[4]=e=>N(ne).des=e),type:"textarea",rows:3},null,8,["modelValue"])])),_:1}),V(m,{label:"启用"},{default:R((()=>[V(f,{modelValue:N(ne).enable,"onUpdate:modelValue":t[5]||(t[5]=e=>N(ne).enable=e)},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"]),V(y,{modelValue:N(a),"onUpdate:modelValue":t[9]||(t[9]=e=>$(a)?a.value=e:null),title:"菜单权限",width:"520px","align-center":"",class:"el-dialog-border"},{footer:R((()=>[K("div",le,[V(n,{onClick:ge},{default:R((()=>[M(I(N(o)?"全部收起":"全部展开"),1)])),_:1}),V(n,{onClick:ve,style:{"margin-left":"8px"}},{default:R((()=>[M(I(N(s)?"取消全选":"全部选择"),1)])),_:1}),V(n,{type:"primary",onClick:fe},{default:R((()=>t[13]||(t[13]=[M("保存")]))),_:1,__:[13]})])])),default:R((()=>[V(_,{height:"70vh"},{default:R((()=>[V(b,{ref_key:"treeRef",ref:r,data:N(re),"show-checkbox":"","node-key":"name","default-expand-all":N(o),"default-checked-keys":[1,2,3],props:pe,onCheck:be},{default:R((({data:e})=>[K("div",Z,[e.isAuth?(w(),j("span",H,I(e.label),1)):(w(),j("span",ee,I(pe.label(e)),1))])])),_:1},8,["data","default-expand-all"])])),_:1})])),_:1},8,["modelValue"])])}}})))),[["__scopeId","data-v-84ecd1de"]]);export{te as default};
