/**
 * 自动路由配置
 * 可以根据项目需求调整自动路由的生成规则
 */

/**
 * 自动路由配置选项
 */
export interface AutoRouteOptions {
  /** 是否启用自动路由 */
  enabled: boolean
  /** 需要排除的路径模式 */
  excludePatterns: string[]
  /** 路由元数据默认配置 */
  defaultMeta: {
    isHideTab: boolean
    requiresAuth: boolean
    keepAlive: boolean
  }
  /** 特殊页面的路由配置 */
  specialPages: {
    [key: string]: {
      title: string
      keepAlive?: boolean
      requiresAuth?: boolean
      isHideTab?: boolean
    }
  }
}

/**
 * 默认自动路由配置
 */
export const autoRouteConfig: AutoRouteOptions = {
  enabled: true,
  
  // 排除的路径模式
  excludePatterns: [
    '/index.vue',     // 列表页面由菜单系统处理
    '/_',             // 私有组件
    '/auth/',         // 认证页面
    '/exception/',    // 异常页面
    '/outside/',      // 外部页面
    '/components/',   // 公共组件
  ],
  
  // 默认元数据
  defaultMeta: {
    isHideTab: true,      // 默认隐藏标签页
    requiresAuth: true,   // 默认需要认证
    keepAlive: false,     // 默认不保持活跃
  },
  
  // 特殊页面配置
  specialPages: {
    'edit': {
      title: '编辑',
      keepAlive: false,
      requiresAuth: true,
      isHideTab: true,
    },
    'add': {
      title: '添加',
      keepAlive: false,
      requiresAuth: true,
      isHideTab: true,
    },
    'detail': {
      title: '详情',
      keepAlive: true,
      requiresAuth: true,
      isHideTab: true,
    },
    'view': {
      title: '查看',
      keepAlive: true,
      requiresAuth: true,
      isHideTab: true,
    },
    'preview': {
      title: '预览',
      keepAlive: false,
      requiresAuth: true,
      isHideTab: true,
    },
    'settings': {
      title: '设置',
      keepAlive: false,
      requiresAuth: true,
      isHideTab: true,
    },
    'config': {
      title: '配置',
      keepAlive: false,
      requiresAuth: true,
      isHideTab: true,
    }
  }
}

/**
 * 路由参数配置
 * 定义哪些页面需要什么样的参数
 */
export const routeParamConfig = {
  // 需要可选 id 参数的页面
  optionalIdPages: ['edit', 'add'],
  
  // 需要必需 id 参数的页面
  requiredIdPages: ['detail', 'view'],
  
  // 自定义参数配置
  customParams: {
    // 例如：'user/profile': '/:userId/profile/:tab?'
  }
}

/**
 * 获取页面的路由参数配置
 */
export function getRouteParams(pageName: string): string {
  if (routeParamConfig.optionalIdPages.includes(pageName)) {
    return '/:id?'
  }
  
  if (routeParamConfig.requiredIdPages.includes(pageName)) {
    return '/:id'
  }
  
  return ''
}

/**
 * 获取页面的元数据配置
 */
export function getPageMeta(pageName: string) {
  const specialConfig = autoRouteConfig.specialPages[pageName]
  
  if (specialConfig) {
    return {
      ...autoRouteConfig.defaultMeta,
      ...specialConfig
    }
  }
  
  return {
    ...autoRouteConfig.defaultMeta,
    title: pageName
  }
}
