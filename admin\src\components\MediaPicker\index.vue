<template>
  <el-dialog
    v-model="visible"
    :title="props.title"
    :width="props.width"
    :before-close="handleClose"
    class="media-picker-dialog"
  >
    <div class="media-picker">
      <!-- 左侧分类 -->
      <div class="category-sidebar">
        <div class="category-header">
          <span>分类</span>
          <el-button
            type="primary"
            size="small"
            @click="showCreateCategory = true"
          >
            新建
          </el-button>
        </div>
        
        <div class="category-list">
          <div
            v-for="category in categories"
            :key="category.id"
            :class="['category-item', { active: selectedCategoryId === category.id }]"
            @click="selectCategory(category.id)"
          >
            <el-icon><Folder /></el-icon>
            <span class="category-name">{{ category.name }}</span>
            <span class="file-count">({{ category.file_count }})</span>
          </div>
        </div>
      </div>
      
      <!-- 右侧内容 -->
      <div class="content-area">
        <!-- 工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-upload
              :action="uploadAction"
              :headers="uploadHeaders"
              :data="{ category_id: selectedCategoryId }"
              :show-file-list="false"
              :on-success="handleUploadSuccess"
              :before-upload="beforeUpload"
              multiple
            >
              <el-button type="primary">
                <el-icon><Upload /></el-icon>
                本地上传
              </el-button>
            </el-upload>
            
            <el-button
              v-if="selectedFiles.length > 0"
              type="danger"
              @click="handleBatchDelete"
            >
              删除选中
            </el-button>
          </div>
          
          <div class="toolbar-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索文件名"
              style="width: 200px"
              @input="handleSearch"
            >
              <template #suffix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            
            <span class="file-count-info">
              已选择 {{ selectedFiles.length }} / {{ multiple ? '∞' : 1 }}
            </span>
            
            <el-button @click="handleClear">清空</el-button>
          </div>
        </div>
        
        <!-- 文件网格 -->
        <div class="file-grid" v-loading="loading">
          <div
            v-for="file in mediaList"
            :key="file.id"
            :class="['file-item', { selected: isSelected(file.id) }]"
            @click="toggleSelect(file)"
          >
            <div class="file-preview">
              <img
                v-if="file.is_image"
                :src="resolveFileUrl(file.file_url)"
                :alt="file.original_name"
                @error="handleImageError"
              />
              <div v-else class="file-icon">
                <el-icon size="40"><Document /></el-icon>
                <span class="file-ext">{{ file.file_type.toUpperCase() }}</span>
              </div>
            </div>
            
            <div class="file-info">
              <div class="file-name" :title="file.original_name">
                {{ file.original_name }}
              </div>
              <div class="file-meta">
                <span>{{ file.file_size_formatted }}</span>
                <span>{{ formatDate(file.created_at) }}</span>
              </div>
            </div>
            
            <div class="file-actions">
              <el-button
                type="danger"
                size="small"
                circle
                @click.stop="handleDeleteFile(file.id)"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>
    
    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :disabled="selectedFiles.length === 0"
          @click="handleConfirm"
        >
          确定
        </el-button>
      </div>
    </template>
    
    <!-- 创建分类对话框 -->
    <el-dialog
      v-model="showCreateCategory"
      title="创建分类"
      width="400px"
    >
      <el-form :model="categoryForm" label-width="80px">
        <el-form-item label="分类名称">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateCategory = false">取消</el-button>
        <el-button type="primary" @click="handleCreateCategory">确定</el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Folder,
  Upload,
  Search,
  Document,
  Delete
} from '@element-plus/icons-vue'
import { MediaService, type MediaFile, type MediaCategory } from '@/api/mediaApi'
import { useUserStore } from '@/store/modules/user'
import { resolveFileUrl } from '@/utils/url'

interface Props {
  modelValue: boolean
  multiple?: boolean
  accept?: string
  maxSize?: number // MB
  title?: string
  width?: string
  defaultCategory?: number
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', files: MediaFile[]): void
}

const props = withDefaults(defineProps<Props>(), {
  multiple: false,
  accept: 'image/*',
  maxSize: 10,
  title: '选择图片',
  width: '60%',
  defaultCategory: 0
})

const emit = defineEmits<Emits>()

const userStore = useUserStore()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const categories = ref<MediaCategory[]>([])
const mediaList = ref<MediaFile[]>([])
const selectedFiles = ref<MediaFile[]>([])
const selectedCategoryId = ref<number | null>(null)
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 创建分类
const showCreateCategory = ref(false)
const categoryForm = reactive({
  name: ''
})

// 上传配置
const uploadAction = ref(import.meta.env.VITE_API_URL + '/media/upload')
const uploadHeaders = ref({
  Authorization: `Bearer ${userStore.accessToken}`
})

// 方法
const loadCategories = async () => {
  try {
    const data = await MediaService.getCategories()
    categories.value = Array.isArray(data) ? data : []

    // 默认选择"全部"
    if (categories.value.length > 0) {
      selectedCategoryId.value = null // 全部
    }
  } catch (error) {
    ElMessage.error('获取分类失败')
    console.error(error)
  }
}

const loadMediaList = async () => {
  loading.value = true
  try {
    const data = await MediaService.getMediaList({
      page: currentPage.value,
      limit: pageSize.value,
      category_id: selectedCategoryId.value,
      keyword: searchKeyword.value,
      file_type: props.accept.includes('image') ? 'image' : ''
    })

    mediaList.value = data.list || []
    total.value = data.total || 0
  } catch (error) {
    ElMessage.error('获取文件列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const selectCategory = (categoryId: number | null) => {
  selectedCategoryId.value = categoryId
  currentPage.value = 1
  loadMediaList()
}

const isSelected = (fileId: number) => {
  return selectedFiles.value.some(file => file.id === fileId)
}

const toggleSelect = (file: MediaFile) => {
  const index = selectedFiles.value.findIndex(f => f.id === file.id)
  
  if (index > -1) {
    selectedFiles.value.splice(index, 1)
  } else {
    if (props.multiple) {
      selectedFiles.value.push(file)
    } else {
      selectedFiles.value = [file]
    }
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadMediaList()
}

const handlePageChange = () => {
  loadMediaList()
}

const handleSizeChange = () => {
  currentPage.value = 1
  loadMediaList()
}

const handleClear = () => {
  selectedFiles.value = []
}

const handleClose = () => {
  visible.value = false
  selectedFiles.value = []
}

const handleConfirm = () => {
  emit('confirm', selectedFiles.value)
  handleClose()
}

// 上传相关
const beforeUpload = (file: File) => {
  // 检查文件类型
  if (props.accept && !props.accept.includes('*')) {
    const allowedTypes = props.accept.split(',').map(type => type.trim())
    const fileType = file.type
    const isAllowed = allowedTypes.some(type => {
      if (type.endsWith('/*')) {
        return fileType.startsWith(type.replace('/*', '/'))
      }
      return fileType === type
    })

    if (!isAllowed) {
      ElMessage.error('不支持的文件类型')
      return false
    }
  }

  // 检查文件大小
  const isLtMaxSize = file.size / 1024 / 1024 < props.maxSize
  if (!isLtMaxSize) {
    ElMessage.error(`文件大小不能超过 ${props.maxSize}MB`)
    return false
  }

  return true
}

const handleUploadSuccess = (response: any) => {
  if (response.code === 200) {
    ElMessage.success('上传成功')
    loadMediaList()
  } else {
    ElMessage.error(response.msg || response.message || '上传失败')
  }
}

// 删除文件
const handleDeleteFile = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个文件吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await MediaService.deleteMedia(id)
    ElMessage.success('删除成功')
    loadMediaList()

    // 从选中列表中移除
    selectedFiles.value = selectedFiles.value.filter(file => file.id !== id)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error(error)
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedFiles.value.length} 个文件吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const ids = selectedFiles.value.map(file => file.id)
    await MediaService.batchDeleteMedia(ids)
    ElMessage.success('批量删除成功')
    loadMediaList()
    selectedFiles.value = []
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
      console.error(error)
    }
  }
}

// 创建分类
const handleCreateCategory = async () => {
  if (!categoryForm.name.trim()) {
    ElMessage.error('请输入分类名称')
    return
  }

  try {
    await MediaService.createCategory(categoryForm.name)
    ElMessage.success('创建成功')
    showCreateCategory.value = false
    categoryForm.name = ''
    loadCategories()
  } catch (error) {
    ElMessage.error('创建失败')
    console.error(error)
  }
}

// 工具方法
const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  return date.toLocaleDateString()
}

const handleImageError = (e: Event) => {
  const img = e.target as HTMLImageElement
  img.style.display = 'none'
  // 可以在这里设置一个默认的错误图标
}

// 监听器
watch(visible, (newVal) => {
  if (newVal) {
    loadCategories()
    loadMediaList()
  }
})

// 生命周期
onMounted(() => {
  if (visible.value) {
    loadCategories()
    loadMediaList()
  }
})
</script>

<style scoped>
.media-picker-dialog {
  .el-dialog__body {
    padding: 0;
  }
}

.media-picker {
  display: flex;
  height: 450px;
}

.category-sidebar {
  width: 200px;
  border-right: 1px solid #e4e7ed;
  background: #f8f9fa;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 500;
}

.category-list {
  padding: 8px 0;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.category-item:hover {
  background-color: #e6f7ff;
}

.category-item.active {
  background-color: #1890ff;
  color: white;
}

.category-name {
  margin-left: 8px;
  flex: 1;
}

.file-count {
  font-size: 12px;
  color: #999;
}

.category-item.active .file-count {
  color: rgba(255, 255, 255, 0.8);
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  background: white;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-count-info {
  font-size: 14px;
  color: #666;
}

.file-grid {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
  gap: 12px;
}

.file-item {
  border: 2px solid transparent;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  aspect-ratio: 1;
}

.file-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.file-item.selected {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.file-preview {
  width: 100%;
  height: 65%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  overflow: hidden;
}

.file-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
}

.file-ext {
  font-size: 12px;
  margin-top: 4px;
}

.file-info {
  padding: 8px;
  height: 35%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.file-name {
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.3;
}

.file-meta {
  font-size: 11px;
  color: #999;
  display: flex;
  justify-content: space-between;
  line-height: 1.2;
}

.file-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.file-item:hover .file-actions {
  opacity: 1;
}

.pagination-wrapper {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
  background: white;
  display: flex;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
