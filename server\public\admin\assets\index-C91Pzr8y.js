var e=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,s=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t,r=(e,a)=>{for(var l in a||(a={}))i.call(a,l)&&s(e,l,a[l]);if(t)for(var l of t(a))o.call(a,l)&&s(e,l,a[l]);return e},d=(e,t)=>a(e,l(t)),n=(e,a,l)=>new Promise(((t,i)=>{var o=e=>{try{r(l.next(e))}catch(a){i(a)}},s=e=>{try{r(l.throw(e))}catch(a){i(a)}},r=e=>e.done?t(e.value):Promise.resolve(e.value).then(o,s);r((l=l.apply(e,a)).next())}));import{B as u,b as c,c as p,E as m}from"./index-TSQrMSQp.js";/* empty css                   *//* empty css                *//* empty css                 *//* empty css                        *//* empty css                  */import"./el-form-item-l0sNRNKZ.js";import{k as _,c as g,r as f,M as v,w as y,d as h,C as x,D as w,G as b,R as V,x as k,a6 as C,W as z,P as j,F as U,$ as B,S as M,aa as S,u as O,aU as P,X as T,a5 as D,V as I,aV as L,aW as $,a3 as E,am as A,aX as W,ac as q,aY as F,aJ as G,aZ as J,ak as R,a8 as X,a2 as K,E as Y,aI as Z,a_ as H,a$ as N,aq as Q}from"./vendor-84Inc-Pt.js";/* empty css                  *//* empty css                      *//* empty css                  *//* empty css                     *//* empty css                    */import{_ as ee}from"./_plugin-vue_export-helper-BCo6x5W8.js";class ae{static getMediaList(e={}){return u.get({url:"/media",params:e})}static uploadMedia(e,a=0){const l=new FormData;return l.append("file",e),l.append("category_id",a.toString()),u.post({url:"/media/upload",data:l,headers:{"Content-Type":"multipart/form-data"}})}static deleteMedia(e){return u.del({url:`/media/${e}`})}static batchDeleteMedia(e){return u.post({url:"/media/batch-delete",data:{ids:e}})}static moveToCategory(e,a){return u.post({url:"/media/move-to-category",data:{ids:e,category_id:a}})}static getCategories(){return u.get({url:"/media/categories"})}static createCategory(e,a=0){return u.post({url:"/media/categories",data:{name:e,parent_id:a}})}static updateCategory(e,a){return u.put({url:`/media/categories/${e}`,data:{name:a}})}static deleteCategory(e){return u.del({url:`/media/categories/${e}`})}}const le={class:"media-picker"},te={class:"category-sidebar"},ie={class:"category-header"},oe={class:"category-list"},se=["onClick"],re={class:"category-name"},de={class:"file-count"},ne={class:"content-area"},ue={class:"toolbar"},ce={class:"toolbar-left"},pe={class:"toolbar-right"},me={class:"file-count-info"},_e={class:"file-grid"},ge=["onClick"],fe={class:"file-preview"},ve=["src","alt"],ye={key:1,class:"file-icon"},he={class:"file-ext"},xe={class:"file-info"},we=["title"],be={class:"file-meta"},Ve={class:"file-actions"},ke={class:"pagination-wrapper"},Ce={class:"dialog-footer"},ze=ee(_({__name:"index",props:{modelValue:{type:Boolean},multiple:{type:Boolean,default:!1},accept:{default:"image/*"},maxSize:{default:10},title:{default:"选择图片"},width:{default:"60%"},defaultCategory:{default:0}},emits:["update:modelValue","confirm"],setup(e,{emit:a}){const l=e,t=a,i=c(),o=g({get:()=>l.modelValue,set:e=>t("update:modelValue",e)}),s=f(!1),r=f([]),d=f([]),u=f([]),p=f(null),m=f(""),_=f(1),H=f(20),N=f(0),Q=f(!1),ee=v({name:""}),ze=f("http://fanshop.gg/adminapi/media/upload"),je=f({Authorization:`Bearer ${i.accessToken}`}),Ue=()=>n(this,null,(function*(){try{const e=yield ae.getCategories();r.value=Array.isArray(e)?e:[],r.value.length>0&&(p.value=null)}catch(e){Y.error("获取分类失败")}})),Be=()=>n(this,null,(function*(){s.value=!0;try{const e=yield ae.getMediaList({page:_.value,limit:H.value,category_id:p.value,keyword:m.value,file_type:l.accept.includes("image")?"image":""});d.value=e.list||[],N.value=e.total||0}catch(e){Y.error("获取文件列表失败")}finally{s.value=!1}})),Me=()=>{_.value=1,Be()},Se=()=>{Be()},Oe=()=>{_.value=1,Be()},Pe=()=>{u.value=[]},Te=()=>{o.value=!1,u.value=[]},De=()=>{t("confirm",u.value),Te()},Ie=e=>{if(l.accept&&!l.accept.includes("*")){const a=l.accept.split(",").map((e=>e.trim())),t=e.type;if(!a.some((e=>e.endsWith("/*")?t.startsWith(e.replace("/*","/")):t===e)))return Y.error("不支持的文件类型"),!1}return!!(e.size/1024/1024<l.maxSize)||(Y.error(`文件大小不能超过 ${l.maxSize}MB`),!1)},Le=e=>{200===e.code?(Y.success("上传成功"),Be()):Y.error(e.msg||e.message||"上传失败")},$e=e=>n(this,null,(function*(){try{yield Z.confirm("确定要删除这个文件吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield ae.deleteMedia(e),Y.success("删除成功"),Be(),u.value=u.value.filter((a=>a.id!==e))}catch(a){"cancel"!==a&&Y.error("删除失败")}})),Ee=()=>n(this,null,(function*(){try{yield Z.confirm(`确定要删除选中的 ${u.value.length} 个文件吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=u.value.map((e=>e.id));yield ae.batchDeleteMedia(e),Y.success("批量删除成功"),Be(),u.value=[]}catch(e){"cancel"!==e&&Y.error("批量删除失败")}})),Ae=()=>n(this,null,(function*(){if(ee.name.trim())try{yield ae.createCategory(ee.name),Y.success("创建成功"),Q.value=!1,ee.name="",Ue()}catch(e){Y.error("创建失败")}else Y.error("请输入分类名称")})),We=e=>{e.target.style.display="none"};return y(o,(e=>{e&&(Ue(),Be())})),h((()=>{o.value&&(Ue(),Be())})),(e,a)=>{const t=C,i=S,n=L,c=E,g=J,f=K,v=X,y=R,h=G;return w(),x(y,{modelValue:o.value,"onUpdate:modelValue":a[7]||(a[7]=e=>o.value=e),title:l.title,width:l.width,"before-close":Te,class:"media-picker-dialog"},{footer:b((()=>[V("div",Ce,[k(t,{onClick:Te},{default:b((()=>a[13]||(a[13]=[z("取消")]))),_:1,__:[13]}),k(t,{type:"primary",disabled:0===u.value.length,onClick:De},{default:b((()=>a[14]||(a[14]=[z(" 确定 ")]))),_:1,__:[14]},8,["disabled"])])])),default:b((()=>[V("div",le,[V("div",te,[V("div",ie,[a[9]||(a[9]=V("span",null,"分类",-1)),k(t,{type:"primary",size:"small",onClick:a[0]||(a[0]=e=>Q.value=!0)},{default:b((()=>a[8]||(a[8]=[z(" 新建 ")]))),_:1,__:[8]})]),V("div",oe,[(w(!0),j(U,null,B(r.value,(e=>(w(),j("div",{key:e.id,class:M(["category-item",{active:p.value===e.id}]),onClick:a=>{return l=e.id,p.value=l,_.value=1,void Be();var l}},[k(i,null,{default:b((()=>[k(O(P))])),_:1}),V("span",re,T(e.name),1),V("span",de,"("+T(e.file_count)+")",1)],10,se)))),128))])]),V("div",ne,[V("div",ue,[V("div",ce,[k(n,{action:ze.value,headers:je.value,data:{category_id:p.value},"show-file-list":!1,"on-success":Le,"before-upload":Ie,multiple:""},{default:b((()=>[k(t,{type:"primary"},{default:b((()=>[k(i,null,{default:b((()=>[k(O($))])),_:1}),a[10]||(a[10]=z(" 本地上传 "))])),_:1,__:[10]})])),_:1},8,["action","headers","data"]),u.value.length>0?(w(),x(t,{key:0,type:"danger",onClick:Ee},{default:b((()=>a[11]||(a[11]=[z(" 删除选中 ")]))),_:1,__:[11]})):I("",!0)]),V("div",pe,[k(c,{modelValue:m.value,"onUpdate:modelValue":a[1]||(a[1]=e=>m.value=e),placeholder:"搜索文件名",style:{width:"200px"},onInput:Me},{suffix:b((()=>[k(i,null,{default:b((()=>[k(O(A))])),_:1})])),_:1},8,["modelValue"]),V("span",me," 已选择 "+T(u.value.length)+" / "+T(e.multiple?"∞":1),1),k(t,{onClick:Pe},{default:b((()=>a[12]||(a[12]=[z("清空")]))),_:1,__:[12]})])]),D((w(),j("div",_e,[(w(!0),j(U,null,B(d.value,(e=>{return w(),j("div",{key:e.id,class:M(["file-item",{selected:(o=e.id,u.value.some((e=>e.id===o)))}]),onClick:a=>(e=>{const a=u.value.findIndex((a=>a.id===e.id));a>-1?u.value.splice(a,1):l.multiple?u.value.push(e):u.value=[e]})(e)},[V("div",fe,[e.is_image?(w(),j("img",{key:0,src:e.file_url,alt:e.original_name,onError:We},null,40,ve)):(w(),j("div",ye,[k(i,{size:"40"},{default:b((()=>[k(O(W))])),_:1}),V("span",he,T(e.file_type.toUpperCase()),1)]))]),V("div",xe,[V("div",{class:"file-name",title:e.original_name},T(e.original_name),9,we),V("div",be,[V("span",null,T(e.file_size_formatted),1),V("span",null,T((a=e.created_at,new Date(a).toLocaleDateString())),1)])]),V("div",Ve,[k(t,{type:"danger",size:"small",circle:"",onClick:q((a=>$e(e.id)),["stop"])},{default:b((()=>[k(i,null,{default:b((()=>[k(O(F))])),_:1})])),_:2},1032,["onClick"])])],10,ge);var a,o})),128))])),[[h,s.value]]),V("div",ke,[k(g,{"current-page":_.value,"onUpdate:currentPage":a[2]||(a[2]=e=>_.value=e),"page-size":H.value,"onUpdate:pageSize":a[3]||(a[3]=e=>H.value=e),total:N.value,"page-sizes":[20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Oe,onCurrentChange:Se},null,8,["current-page","page-size","total"])])])]),k(y,{modelValue:Q.value,"onUpdate:modelValue":a[6]||(a[6]=e=>Q.value=e),title:"创建分类",width:"400px"},{footer:b((()=>[k(t,{onClick:a[5]||(a[5]=e=>Q.value=!1)},{default:b((()=>a[15]||(a[15]=[z("取消")]))),_:1,__:[15]}),k(t,{type:"primary",onClick:Ae},{default:b((()=>a[16]||(a[16]=[z("确定")]))),_:1,__:[16]})])),default:b((()=>[k(v,{model:ee,"label-width":"80px"},{default:b((()=>[k(f,{label:"分类名称"},{default:b((()=>[k(c,{modelValue:ee.name,"onUpdate:modelValue":a[4]||(a[4]=e=>ee.name=e),placeholder:"请输入分类名称"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])),_:1},8,["modelValue","title","width"])}}}),[["__scopeId","data-v-6edf891e"]]),je={class:"page-content"},Ue={class:"upload-container"},Be={class:"image-preview-container"},Me=["src"],Se={key:1,class:"placeholder"},Oe={key:0,class:"image-actions"},Pe={class:"upload-container"},Te={class:"image-preview-container"},De=["src"],Ie={key:1,class:"placeholder"},Le={key:0,class:"image-actions"},$e=ee(_(d(r({},{name:"SystemSettings"}),{__name:"index",setup(e){const a=f(),l=f(!1),t=f(!1),i=p(),o=f(!1),s=f("logo"),u=v({site_name:"",site_logo:"",site_description:"",site_keywords:"",site_favicon:"",site_copyright:"",site_icp:"",upload_max_size:10,upload_allowed_ext:"jpg,jpeg,png,gif,webp,pdf,doc,docx,xls,xlsx",logo_size:48}),c=v({site_name:[{required:!0,message:"请输入网站名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],upload_max_size:[{required:!0,message:"请设置最大上传大小",trigger:"blur"}]}),_=()=>n(this,null,(function*(){l.value=!0;try{const e=yield m.getConfig();e&&(Object.keys(u).forEach((e=>{"upload_max_size"!==e&&"logo_size"!==e&&(u[e]="")})),Object.assign(u,e),e.upload_max_size&&(u.upload_max_size=Math.round(e.upload_max_size/1024/1024)),e.logo_size||(u.logo_size=48),i.updateSystemConfig(u))}catch(e){Y.error("获取配置失败")}finally{l.value=!1}})),g=e=>{s.value=e,o.value=!0},y=e=>{"logo"===e?u.site_logo="":u.site_favicon=""},U=e=>{if(e.length>0){const a=e[0];"logo"===s.value?(u.site_logo=a.file_url,Y.success("Logo设置成功")):(u.site_favicon=a.file_url,Y.success("网站图标设置成功"))}},B=()=>n(this,null,(function*(){if(a.value)try{yield a.value.validate(),t.value=!0;const e=d(r({},u),{upload_max_size:1024*(u.upload_max_size||10)*1024});yield m.setConfig(e),i.updateSystemConfig(u),Y.success("保存成功")}catch(e){!1!==e&&Y.error("保存失败")}finally{t.value=!1}})),M=()=>{Z.confirm("确定要重置表单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{var e;null==(e=a.value)||e.resetFields(),_()}))};return h((()=>{_()})),(e,i)=>{const r=H,d=E,n=K,p=S,m=C,_=Q,f=X,v=G;return w(),j("div",je,[D((w(),x(f,{ref_key:"formRef",ref:a,model:u,rules:c,"label-width":"120px"},{default:b((()=>[k(r,{"content-position":"left"},{default:b((()=>i[13]||(i[13]=[z("基础信息")]))),_:1,__:[13]}),k(n,{label:"网站名称",prop:"site_name"},{default:b((()=>[k(d,{modelValue:u.site_name,"onUpdate:modelValue":i[0]||(i[0]=e=>u.site_name=e),placeholder:"请输入网站名称",maxlength:"50","show-word-limit":"",style:{width:"400px"}},null,8,["modelValue"])])),_:1}),k(n,{label:"网站描述",prop:"site_description"},{default:b((()=>[k(d,{modelValue:u.site_description,"onUpdate:modelValue":i[1]||(i[1]=e=>u.site_description=e),type:"textarea",rows:3,placeholder:"请输入网站描述",maxlength:"200","show-word-limit":"",style:{width:"500px"}},null,8,["modelValue"])])),_:1}),k(n,{label:"网站关键词",prop:"site_keywords"},{default:b((()=>[k(d,{modelValue:u.site_keywords,"onUpdate:modelValue":i[2]||(i[2]=e=>u.site_keywords=e),placeholder:"请输入网站关键词，多个关键词用逗号分隔",maxlength:"100","show-word-limit":"",style:{width:"450px"}},null,8,["modelValue"])])),_:1}),k(n,{label:"网站Logo",prop:"site_logo"},{default:b((()=>[V("div",Ue,[V("div",Be,[V("div",{class:"image-preview",onClick:i[3]||(i[3]=e=>g("logo"))},[u.site_logo?(w(),j("img",{key:0,src:u.site_logo,class:"logo"},null,8,Me)):(w(),j("div",Se,[k(p,{class:"placeholder-icon"},{default:b((()=>[k(O(N))])),_:1})]))]),u.site_logo?(w(),j("div",Oe,[k(m,{size:"small",type:"danger",onClick:i[4]||(i[4]=e=>y("logo"))},{default:b((()=>i[14]||(i[14]=[z("清除")]))),_:1,__:[14]})])):I("",!0)]),i[15]||(i[15]=V("div",{class:"upload-tip"},[V("p",null,"建议尺寸：200x60px，支持jpg、png格式，大小不超过2MB")],-1))])])),_:1}),k(n,{label:"网站图标",prop:"site_favicon"},{default:b((()=>[V("div",Pe,[V("div",Te,[V("div",{class:"image-preview favicon-preview",onClick:i[5]||(i[5]=e=>g("favicon"))},[u.site_favicon?(w(),j("img",{key:0,src:u.site_favicon,class:"favicon"},null,8,De)):(w(),j("div",Ie,[k(p,{class:"placeholder-icon"},{default:b((()=>[k(O(N))])),_:1})]))]),u.site_favicon?(w(),j("div",Le,[k(m,{size:"small",type:"danger",onClick:i[6]||(i[6]=e=>y("favicon"))},{default:b((()=>i[16]||(i[16]=[z("清除")]))),_:1,__:[16]})])):I("",!0)]),i[17]||(i[17]=V("div",{class:"upload-tip"},[V("p",null,"建议尺寸：32x32px，支持ico、png格式，大小不超过1MB")],-1))])])),_:1}),k(r,{"content-position":"left"},{default:b((()=>i[18]||(i[18]=[z("其他设置")]))),_:1,__:[18]}),k(n,{label:"版权信息",prop:"site_copyright"},{default:b((()=>[k(d,{modelValue:u.site_copyright,"onUpdate:modelValue":i[7]||(i[7]=e=>u.site_copyright=e),placeholder:"请输入版权信息",maxlength:"100","show-word-limit":"",style:{width:"450px"}},null,8,["modelValue"])])),_:1}),k(n,{label:"ICP备案号",prop:"site_icp"},{default:b((()=>[k(d,{modelValue:u.site_icp,"onUpdate:modelValue":i[8]||(i[8]=e=>u.site_icp=e),placeholder:"请输入ICP备案号",maxlength:"50",style:{width:"300px"}},null,8,["modelValue"])])),_:1}),k(n,{label:"Logo尺寸",prop:"logo_size"},{default:b((()=>[k(_,{modelValue:u.logo_size,"onUpdate:modelValue":i[9]||(i[9]=e=>u.logo_size=e),min:24,max:100,step:4,"controls-position":"right",placeholder:"Logo显示尺寸（像素）",style:{width:"200px"}},null,8,["modelValue"]),i[19]||(i[19]=V("span",{style:{"margin-left":"10px",color:"#999"}},"像素（建议24-100px）",-1))])),_:1,__:[19]}),k(r,{"content-position":"left"},{default:b((()=>i[20]||(i[20]=[z("上传设置")]))),_:1,__:[20]}),k(n,{label:"最大上传大小",prop:"upload_max_size"},{default:b((()=>[k(_,{modelValue:u.upload_max_size,"onUpdate:modelValue":i[10]||(i[10]=e=>u.upload_max_size=e),min:1,max:100,step:1,"controls-position":"right",style:{width:"200px"}},null,8,["modelValue"]),i[21]||(i[21]=V("span",{class:"input-suffix"},"MB",-1))])),_:1,__:[21]}),k(n,{label:"允许上传类型",prop:"upload_allowed_ext"},{default:b((()=>[k(d,{modelValue:u.upload_allowed_ext,"onUpdate:modelValue":i[11]||(i[11]=e=>u.upload_allowed_ext=e),placeholder:"请输入允许上传的文件扩展名，用逗号分隔",maxlength:"200",style:{width:"500px"}},null,8,["modelValue"])])),_:1}),k(n,null,{default:b((()=>[k(m,{type:"primary",onClick:B,loading:t.value},{default:b((()=>i[22]||(i[22]=[z(" 保存设置 ")]))),_:1,__:[22]},8,["loading"]),k(m,{onClick:M},{default:b((()=>i[23]||(i[23]=[z("重置")]))),_:1,__:[23]})])),_:1})])),_:1},8,["model","rules"])),[[v,l.value]]),k(ze,{modelValue:o.value,"onUpdate:modelValue":i[12]||(i[12]=e=>o.value=e),title:"logo"===s.value?"选择网站Logo":"选择网站图标",multiple:!1,accept:"image/*","max-size":5,"default-category":4,width:"60%",onConfirm:U},null,8,["modelValue","title"])])}}})),[["__scopeId","data-v-5055d65c"]]);export{$e as default};
