var e=Object.defineProperty,t=Object.defineProperties,a=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,o=(t,a,s)=>a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[a]=s,i=(e,t)=>{for(var a in t||(t={}))l.call(t,a)&&o(e,a,t[a]);if(s)for(var a of s(t))r.call(t,a)&&o(e,a,t[a]);return e},n=(e,s)=>t(e,a(s));import{u,a as d,b as c,c as p,l as v,g,U as h,H as f,S as m,d as y}from"./index-DG9-1w7X.js";/* empty css                *//* empty css                  *//* empty css                    */import{k as x,M as w,N as b,r as k,d as _,n as T,O as M,c as B,P as j,D as L,Q as $,u as O,R as P,S as V,U as I,V as C,W as S,X as D,s as E,Y as X,Z as U,x as H,G as R,_ as z,F as A,$ as F,a0 as N,a1 as Y,a2 as q,a3 as G,a4 as K,a5 as W,C as Q,a6 as Z,a7 as J,a8 as ee,E as te,f as ae}from"./vendor-84Inc-Pt.js";import{_ as se}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./el-form-item-l0sNRNKZ.js";/* empty css                 */import{_ as le}from"./index-BHe8wxQ0.js";/* empty css                         *//* empty css                     */import{_ as re}from"./LoginLeftView-GL2VWqai.js";const oe=["innerHTML"],ie=se(x(n(i({},{name:"ArtDragVerify"}),{__name:"index",props:{value:{type:Boolean,default:!1},width:{default:"100%"},height:{default:40},text:{default:"按住滑块拖动"},successText:{default:"success"},background:{default:"#eee"},progressBarBg:{default:"#1385FF"},completedBg:{default:"#57D187"},circle:{type:Boolean,default:!1},radius:{default:"calc(var(--custom-radius) / 3 + 2px)"},handlerIcon:{default:"&#xea50;"},successIcon:{default:"&#xe621;"},handlerBg:{default:"#fff"},textSize:{default:"13px"},textColor:{default:"#333"}},emits:["handlerMove","update:value","passCallback"],setup(e,{expose:t,emit:a}){const s=a,l=e,r=w({isMoving:!1,x:0,isOk:!1}),{isOk:o}=b(r),i=k(),n=k(),u=k(),d=k();let c,p,v,g;const h=e=>{c=e.targetTouches[0].pageX,p=e.targetTouches[0].pageY},f=e=>{v=e.targetTouches[0].pageX,g=e.targetTouches[0].pageY,Math.abs(v-c)>Math.abs(g-p)&&e.preventDefault()};document.addEventListener("touchstart",h),document.addEventListener("touchmove",f,{passive:!1});const m=()=>{var e;return"string"==typeof l.width?(null==(e=i.value)?void 0:e.offsetWidth)||260:l.width};_((()=>{var e;null==(e=i.value)||e.style.setProperty("--textColor",l.textColor),T((()=>{var e,t;const a=m();null==(e=i.value)||e.style.setProperty("--width",Math.floor(a/2)+"px"),null==(t=i.value)||t.style.setProperty("--pwidth",-Math.floor(a/2)+"px")})),document.addEventListener("touchstart",h),document.addEventListener("touchmove",f,{passive:!1})})),M((()=>{document.removeEventListener("touchstart",h),document.removeEventListener("touchmove",f)}));const y={left:"0",width:l.height+"px",height:l.height+"px",background:l.handlerBg},x=B((()=>({width:"string"==typeof l.width?l.width:l.width+"px",height:l.height+"px",lineHeight:l.height+"px",background:l.background,borderRadius:l.circle?l.height/2+"px":l.radius}))),E={background:l.progressBarBg,height:l.height+"px",borderRadius:l.circle?l.height/2+"px 0 0 "+l.height/2+"px":l.radius},X=B((()=>({fontSize:l.textSize}))),U=B((()=>l.value?l.successText:l.text)),H=e=>{l.value||(r.isMoving=!0,u.value.style.transition="none",r.x=(e.pageX||e.touches[0].pageX)-parseInt(u.value.style.left.replace("px",""),10)),s("handlerMove")},R=e=>{if(r.isMoving&&!l.value){const t=m();let a=(e.pageX||e.touches[0].pageX)-r.x;a>0&&a<=t-l.height?(u.value.style.left=a+"px",d.value.style.width=a+l.height/2+"px"):a>t-l.height&&(u.value.style.left=t-l.height+"px",d.value.style.width=t-l.height/2+"px",A())}},z=e=>{if(r.isMoving&&!l.value){const t=m();(e.pageX||e.changedTouches[0].pageX)-r.x<t-l.height?(r.isOk=!0,u.value.style.left="0",u.value.style.transition="all 0.2s",d.value.style.width="0",r.isOk=!1):(u.value.style.transition="none",u.value.style.left=t-l.height+"px",d.value.style.width=t-l.height/2+"px",A()),r.isMoving=!1}},A=()=>{s("update:value",!0),r.isMoving=!1,d.value.style.background=l.completedBg,n.value.style["-webkit-text-fill-color"]="unset",n.value.style.animation="slidetounlock2 3s infinite",n.value.style.color="#fff",s("passCallback")};return t({reset:()=>{u.value.style.left="0",d.value.style.width="0",u.value.children[0].innerHTML=l.handlerIcon,n.value.style["-webkit-text-fill-color"]="transparent",n.value.style.animation="slidetounlock 3s infinite",n.value.style.color=l.background,s("update:value",!1),r.isOk=!1,r.isMoving=!1,r.x=0}}),(e,t)=>(L(),j("div",{ref_key:"dragVerify",ref:i,class:"drag_verify",style:$(O(x)),onMousemove:R,onMouseup:z,onMouseleave:z,onTouchmove:R,onTouchend:z},[P("div",{class:V(["dv_progress_bar",{goFirst2:O(o)}]),ref_key:"progressBar",ref:d,style:E},null,2),P("div",{class:"dv_text",style:$(O(X)),ref_key:"messageRef",ref:n},[e.$slots.textBefore?I(e.$slots,"textBefore",{key:0},void 0,!0):C("",!0),S(" "+D(O(U))+" ",1),e.$slots.textAfter?I(e.$slots,"textAfter",{key:1},void 0,!0):C("",!0)],4),P("div",{class:V(["dv_handler dv_handler_bg",{goFirst:O(o)}]),onMousedown:H,onTouchstart:H,ref_key:"handler",ref:u,style:y},[P("i",{class:"iconfont-sys",innerHTML:e.value?e.successIcon:e.handlerIcon},null,8,oe)],34)],36))}})),[["__scopeId","data-v-a83a974c"]]),ne={class:"login"},ue={class:"right-wrap"},de={class:"top-right-wrap"},ce={class:"iconfont-sys"},pe={class:"menu-txt"},ve={key:0,class:"iconfont-sys icon-check"},ge={class:"header"},he={class:"login-wrap"},fe={class:"form"},me={class:"title"},ye={class:"sub-title"},xe={class:"drag-verify"},we={class:"forget-password"},be={style:{"margin-top":"30px"}},ke=se(x(n(i({},{name:"Login"}),{__name:"index",setup(e){const{t:t}=u(),a=d(),{isDark:s,systemThemeType:l}=E(a),r=k(),o=c(),i=X(),n=k(!1),x=k(!1),b=p(),_=B((()=>b.getSiteName())),T=k(),M=w({username:"admin",password:"123456",rememberPassword:!0}),$=B((()=>({username:[{required:!0,message:t("login.placeholder[0]"),trigger:"blur"}],password:[{required:!0,message:t("login.placeholder[1]"),trigger:"blur"}]}))),I=k(!1),se=()=>{return e=this,t=null,a=function*(){if(T.value)try{if(!(yield T.value.validate()))return;if(!n.value)return void(x.value=!0);I.value=!0;const{username:e,password:t}=M,{token:a,refreshToken:s}=yield h.login({userName:e,password:t});if(!a)throw new Error("Login failed - no token received");o.setToken(a,s);const l=yield h.getUserInfo();o.setUserInfo(l),o.setLoginStatus(!0),yield b.loadSystemConfig(),setTimeout((()=>{ke()}),100),i.push("/")}catch(e){e instanceof f?te.error(e.message||"登录失败"):te.error("登录失败，请稍后重试")}finally{I.value=!1,oe()}},new Promise(((s,l)=>{var r=e=>{try{i(a.next(e))}catch(t){l(t)}},o=e=>{try{i(a.throw(e))}catch(t){l(t)}},i=e=>e.done?s(e.value):Promise.resolve(e.value).then(r,o);i((a=a.apply(e,t)).next())}));var e,t,a},oe=()=>{r.value.reset()},ke=()=>{setTimeout((()=>{const e=_.value;ae({title:t("login.success.title"),type:"success",duration:2500,zIndex:1e4,message:`${t("login.success.message")}, ${e}!`})}),150)},{locale:_e}=u(),Te=e=>{_e.value!==e&&(_e.value=e,o.setLanguage(e))},Me=()=>{let{LIGHT:e,DARK:t}=m;y().switchThemeStyles(l.value===e?t:e)};return(e,t)=>{const a=re,l=N,o=z,i=Y,u=le,d=G,c=q,p=ie,h=K,f=Z,m=ee,y=U("ripple");return L(),j("div",ne,[H(a),P("div",ue,[P("div",de,[P("div",{class:"btn theme-btn",onClick:Me},[P("i",ce,D(O(s)?"":""),1)]),H(i,{onCommand:Te,"popper-class":"langDropDownStyle"},{dropdown:R((()=>[H(o,null,{default:R((()=>[(L(!0),j(A,null,F(O(v),(e=>(L(),j("div",{key:e.value,class:"lang-btn-item"},[H(l,{command:e.value,class:V({"is-selected":O(_e)===e.value})},{default:R((()=>[P("span",pe,D(e.label),1),O(_e)===e.value?(L(),j("i",ve,"")):C("",!0)])),_:2},1032,["command","class"])])))),128))])),_:1})])),default:R((()=>[t[4]||(t[4]=P("div",{class:"btn language-btn"},[P("i",{class:"iconfont-sys icon-language"},"")],-1))])),_:1,__:[4]})]),P("div",ge,[H(u,{class:"icon"}),P("h1",null,D(_.value),1)]),P("div",he,[P("div",fe,[P("h3",me,D(e.$t("login.title")),1),P("p",ye,D(e.$t("login.subTitle")),1),H(m,{ref_key:"formRef",ref:T,model:M,rules:$.value,onKeyup:J(se,["enter"]),style:{"margin-top":"25px"}},{default:R((()=>[H(c,{prop:"username"},{default:R((()=>[H(d,{placeholder:e.$t("login.placeholder[0]"),modelValue:M.username,"onUpdate:modelValue":t[0]||(t[0]=e=>M.username=e),modelModifiers:{trim:!0}},null,8,["placeholder","modelValue"])])),_:1}),H(c,{prop:"password"},{default:R((()=>[H(d,{placeholder:e.$t("login.placeholder[1]"),modelValue:M.password,"onUpdate:modelValue":t[1]||(t[1]=e=>M.password=e),modelModifiers:{trim:!0},type:"password",radius:"8px",autocomplete:"off","show-password":""},null,8,["placeholder","modelValue"])])),_:1}),P("div",xe,[P("div",{class:V(["drag-verify-content",{error:!n.value&&x.value}])},[H(p,{ref_key:"dragVerify",ref:r,value:n.value,"onUpdate:value":t[2]||(t[2]=e=>n.value=e),text:e.$t("login.sliderText"),textColor:"var(--art-gray-800)",successText:e.$t("login.sliderSuccessText"),progressBarBg:O(g)("--el-color-primary"),background:"var(--art-gray-200)",handlerBg:"var(--art-main-bg-color)"},null,8,["value","text","successText","progressBarBg"])],2),P("p",{class:V(["error-text",{"show-error-text":!n.value&&x.value}])},D(e.$t("login.placeholder[2]")),3)]),P("div",we,[H(h,{modelValue:M.rememberPassword,"onUpdate:modelValue":t[3]||(t[3]=e=>M.rememberPassword=e)},{default:R((()=>[S(D(e.$t("login.rememberPwd")),1)])),_:1},8,["modelValue"])]),P("div",be,[W((L(),Q(f,{class:"login-btn",type:"primary",onClick:se,loading:I.value},{default:R((()=>[S(D(e.$t("login.btnText")),1)])),_:1},8,["loading"])),[[y]])])])),_:1},8,["model","rules"])])])])])}}})),[["__scopeId","data-v-d93e4eaa"]]);export{ke as default};
