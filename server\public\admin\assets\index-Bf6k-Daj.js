var e=Object.defineProperty,r=Object.defineProperties,a=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,l=(r,a,o)=>a in r?e(r,a,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[a]=o;import{u as i,A as d,R as n}from"./index-DG9-1w7X.js";/* empty css                *//* empty css                  *//* empty css                    */import"./el-form-item-l0sNRNKZ.js";/* empty css                 */import{_ as p}from"./index-BHe8wxQ0.js";import{_ as u}from"./LoginLeftView-GL2VWqai.js";import{k as m,r as c,M as g,P as f,x as v,R as w,X as h,u as y,G as b,a8 as _,Y as j,B as P,Z as x,D as V,a2 as $,a3 as O,a7 as E,a4 as k,W as L,a5 as T,C as M,a6 as R,E as U}from"./vendor-84Inc-Pt.js";import{_ as q}from"./_plugin-vue_export-helper-BCo6x5W8.js";const A={class:"login register"},B={class:"right-wrap"},I={class:"header"},C={class:"login-wrap"},D={class:"form"},z={class:"title"},F={class:"sub-title"},G={style:{"margin-top":"15px"}},K={class:"footer"},S=m((W=((e,r)=>{for(var a in r||(r={}))t.call(r,a)&&l(e,a,r[a]);if(o)for(var a of o(r))s.call(r,a)&&l(e,a,r[a]);return e})({},{name:"Register"}),r(W,a({__name:"index",setup(e){const{t:r}=i(),a=j(),o=c(),t=d.systemInfo.name,s=c(!1),l=g({username:"",password:"",confirmPassword:"",agreement:!1}),m=g({username:[{required:!0,message:r("register.placeholder[0]"),trigger:"blur"},{min:3,max:20,message:r("register.rule[2]"),trigger:"blur"}],password:[{required:!0,validator:(e,a,t)=>{var s;""===a?t(new Error(r("register.placeholder[1]"))):(""!==l.confirmPassword&&(null==(s=o.value)||s.validateField("confirmPassword")),t())},trigger:"blur"},{min:6,message:r("register.rule[3]"),trigger:"blur"}],confirmPassword:[{required:!0,validator:(e,a,o)=>{""===a?o(new Error(r("register.rule[0]"))):a!==l.password?o(new Error(r("register.rule[1]"))):o()},trigger:"blur"}],agreement:[{validator:(e,a,o)=>{a?o():o(new Error(r("register.rule[4]")))},trigger:"change"}]}),q=()=>{return e=this,r=null,a=function*(){if(o.value)try{yield o.value.validate(),s.value=!0,setTimeout((()=>{s.value=!1,U.success("注册成功"),S()}),1e3)}catch(e){}},new Promise(((o,t)=>{var s=e=>{try{i(a.next(e))}catch(r){t(r)}},l=e=>{try{i(a.throw(e))}catch(r){t(r)}},i=e=>e.done?o(e.value):Promise.resolve(e.value).then(s,l);i((a=a.apply(e,r)).next())}));var e,r,a},S=()=>{setTimeout((()=>{a.push(n.Login)}),1e3)};return(e,r)=>{const a=u,i=p,d=O,c=$,g=P("router-link"),j=k,U=R,S=_,W=x("ripple");return V(),f("div",A,[v(a),w("div",B,[w("div",I,[v(i,{class:"icon"}),w("h1",null,h(y(t)),1)]),w("div",C,[w("div",D,[w("h3",z,h(e.$t("register.title")),1),w("p",F,h(e.$t("register.subTitle")),1),v(S,{ref_key:"formRef",ref:o,model:y(l),rules:y(m),"label-position":"top"},{default:b((()=>[v(c,{prop:"username"},{default:b((()=>[v(d,{modelValue:y(l).username,"onUpdate:modelValue":r[0]||(r[0]=e=>y(l).username=e),modelModifiers:{trim:!0},placeholder:e.$t("register.placeholder[0]")},null,8,["modelValue","placeholder"])])),_:1}),v(c,{prop:"password"},{default:b((()=>[v(d,{modelValue:y(l).password,"onUpdate:modelValue":r[1]||(r[1]=e=>y(l).password=e),modelModifiers:{trim:!0},placeholder:e.$t("register.placeholder[1]"),type:"password",autocomplete:"off","show-password":""},null,8,["modelValue","placeholder"])])),_:1}),v(c,{prop:"confirmPassword"},{default:b((()=>[v(d,{modelValue:y(l).confirmPassword,"onUpdate:modelValue":r[2]||(r[2]=e=>y(l).confirmPassword=e),modelModifiers:{trim:!0},placeholder:e.$t("register.placeholder[2]"),type:"password",autocomplete:"off",onKeyup:E(q,["enter"]),"show-password":""},null,8,["modelValue","placeholder"])])),_:1}),v(c,{prop:"agreement"},{default:b((()=>[v(j,{modelValue:y(l).agreement,"onUpdate:modelValue":r[3]||(r[3]=e=>y(l).agreement=e)},{default:b((()=>[L(h(e.$t("register.agreeText"))+" ",1),v(g,{style:{color:"var(--main-color)","text-decoration":"none"},to:"/privacy-policy"},{default:b((()=>[L(h(e.$t("register.privacyPolicy")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1}),w("div",G,[T((V(),M(U,{class:"register-btn",type:"primary",onClick:q,loading:y(s)},{default:b((()=>[L(h(e.$t("register.submitBtnText")),1)])),_:1},8,["loading"])),[[W]])]),w("div",K,[w("p",null,[L(h(e.$t("register.hasAccount"))+" ",1),v(g,{to:y(n).Login},{default:b((()=>[L(h(e.$t("register.toLogin")),1)])),_:1},8,["to"])])])])),_:1},8,["model","rules"])])])])])}}}))));var W;const X=q(S,[["__scopeId","data-v-79bc0ed4"]]);export{X as default};
