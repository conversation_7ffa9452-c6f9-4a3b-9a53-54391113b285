var e=Object.defineProperty,a=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,s=(a,r,l)=>r in a?e(a,r,{enumerable:!0,configurable:!0,writable:!0,value:l}):a[r]=l,o=(e,a,r)=>new Promise(((l,s)=>{var o=e=>{try{d(r.next(e))}catch(a){s(a)}},t=e=>{try{d(r.throw(e))}catch(a){s(a)}},d=e=>e.done?l(e.value):Promise.resolve(e.value).then(o,t);d((r=r.apply(e,a)).next())}));import{U as t,A as d}from"./index-Bcx6y5fH.js";/* empty css                  *//* empty css                *//* empty css               *//* empty css               *//* empty css                 */import"./el-form-item-l0sNRNKZ.js";/* empty css                  */import{m as i,d as u,r as p,M as n,w as m,P as c,D as f,x as w,G as v,a8 as P,a2 as g,R as y,a6 as _,W as b,u as h,aa as V,b2 as j,a3 as k,aL as x,aK as U,ak as D,F as O,E as N,n as C}from"./vendor-8T3zXQLl.js";import{M as E}from"./index-7x-iptGQ.js";import{_ as R}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                   *//* empty css                      *//* empty css                  *//* empty css                     *//* empty css                    */const $={class:"media-upload-field"},q={key:0,class:"selected-media-box"},z=["src"],G={class:"media-overlay"},I={class:"dialog-footer"},K=R(i({__name:"user-edit-dialog",props:{visible:{type:Boolean},userData:{}},emits:["update:visible","submit"],setup(e,{emit:i}){const R=e,K=i,L=u({get:()=>R.visible,set:e=>K("update:visible",e)}),M=p(),A=p(!1),B=n({avatar:"",userPhone:"",nickName:"",password:"",confirmPassword:"",payPassword:"",confirmPayPassword:"",referrer:""}),F={userPhone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],nickName:[{required:!0,message:"请输入昵称",trigger:"blur"},{min:2,max:20,message:"昵称长度必须在2-20位之间",trigger:"blur"}],password:[{min:6,max:32,message:"密码长度必须在6-32位之间",trigger:"blur"}],confirmPassword:[{validator:(e,a,r)=>{B.password&&a!==B.password?r(new Error("两次输入的密码不一致")):r()},trigger:"blur"}],payPassword:[{pattern:/^\d{6}$/,message:"支付密码必须是6位数字",trigger:"blur"}],confirmPayPassword:[{validator:(e,a,r)=>{B.payPassword&&a!==B.payPassword?r(new Error("两次输入的支付密码不一致")):r()},trigger:"blur"}]};m((()=>[R.visible,R.userData]),(([e])=>{e&&(R.userData&&Object.assign(B,{avatar:R.userData.avatar||"",userPhone:R.userData.userPhone||"",nickName:R.userData.nickName||"",password:"",confirmPassword:"",payPassword:"",confirmPayPassword:"",referrer:R.userData.referrer||""}),C((()=>{var e;null==(e=M.value)||e.clearValidate()})))}),{immediate:!0});const H=()=>o(this,null,(function*(){M.value&&(yield M.value.validate((e=>o(this,null,(function*(){if(e)try{const e=((e,o)=>{for(var t in o||(o={}))r.call(o,t)&&s(e,t,o[t]);if(a)for(var t of a(o))l.call(o,t)&&s(e,t,o[t]);return e})({},B);e.password||(delete e.password,delete e.confirmPassword),e.payPassword||(delete e.payPassword,delete e.confirmPayPassword),yield t.updateUser(R.userData.id,e),N.success("更新用户成功"),L.value=!1,K("submit")}catch(o){N.error("更新用户失败："+(null==o?void 0:o.message)||"未知错误")}})))))})),J=()=>{A.value=!0},S=()=>{B.avatar=""},W=e=>{e.length>0&&(B.avatar=e[0].file_url||"")};return(e,a)=>{const r=_,l=g,s=k,o=U,t=x,i=P,u=D;return f(),c(O,null,[w(u,{modelValue:L.value,"onUpdate:modelValue":a[8]||(a[8]=e=>L.value=e),title:"编辑用户",width:"600px","align-center":""},{footer:v((()=>[y("div",I,[w(r,{onClick:a[7]||(a[7]=e=>L.value=!1)},{default:v((()=>a[13]||(a[13]=[b("取消")]))),_:1,__:[13]}),w(r,{type:"primary",onClick:H},{default:v((()=>a[14]||(a[14]=[b("确定")]))),_:1,__:[14]})])])),default:v((()=>[w(i,{ref_key:"formRef",ref:M,model:B,rules:F,"label-width":"100px"},{default:v((()=>[w(l,{label:"头像",prop:"avatar"},{default:v((()=>{return[y("div",$,[B.avatar?(f(),c("div",q,[y("img",{src:(e=B.avatar,d(e)),alt:"头像",class:"media-preview-image"},null,8,z),y("div",G,[w(r,{size:"small",onClick:J},{default:v((()=>a[10]||(a[10]=[b("重新选择")]))),_:1,__:[10]}),w(r,{size:"small",type:"danger",onClick:S},{default:v((()=>a[11]||(a[11]=[b("移除")]))),_:1,__:[11]})])])):(f(),c("div",{key:1,class:"upload-area",onClick:J},[w(h(V),{class:"upload-icon"},{default:v((()=>[w(h(j))])),_:1})])),a[12]||(a[12]=y("div",{class:"upload-tip"},"请选择头像（可选）",-1))])];var e})),_:1}),w(l,{label:"手机号",prop:"userPhone"},{default:v((()=>[w(s,{modelValue:B.userPhone,"onUpdate:modelValue":a[0]||(a[0]=e=>B.userPhone=e),placeholder:"请输入手机号"},null,8,["modelValue"])])),_:1}),w(l,{label:"昵称",prop:"nickName"},{default:v((()=>[w(s,{modelValue:B.nickName,"onUpdate:modelValue":a[1]||(a[1]=e=>B.nickName=e),placeholder:"请输入昵称"},null,8,["modelValue"])])),_:1}),w(t,{gutter:20},{default:v((()=>[w(o,{span:12},{default:v((()=>[w(l,{label:"密码",prop:"password"},{default:v((()=>[w(s,{modelValue:B.password,"onUpdate:modelValue":a[2]||(a[2]=e=>B.password=e),type:"password",placeholder:"不填则不修改","show-password":""},null,8,["modelValue"])])),_:1})])),_:1}),w(o,{span:12},{default:v((()=>[w(l,{label:"确认密码",prop:"confirmPassword"},{default:v((()=>[w(s,{modelValue:B.confirmPassword,"onUpdate:modelValue":a[3]||(a[3]=e=>B.confirmPassword=e),type:"password",placeholder:"不填则不修改","show-password":""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),w(t,{gutter:20},{default:v((()=>[w(o,{span:12},{default:v((()=>[w(l,{label:"支付密码",prop:"payPassword"},{default:v((()=>[w(s,{modelValue:B.payPassword,"onUpdate:modelValue":a[4]||(a[4]=e=>B.payPassword=e),type:"password",placeholder:"不填则不修改","show-password":""},null,8,["modelValue"])])),_:1})])),_:1}),w(o,{span:12},{default:v((()=>[w(l,{label:"确认密码",prop:"confirmPayPassword"},{default:v((()=>[w(s,{modelValue:B.confirmPayPassword,"onUpdate:modelValue":a[5]||(a[5]=e=>B.confirmPayPassword=e),type:"password",placeholder:"不填则不修改","show-password":""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),w(l,{label:"推荐人",prop:"referrer"},{default:v((()=>[w(s,{modelValue:B.referrer,"onUpdate:modelValue":a[6]||(a[6]=e=>B.referrer=e),placeholder:"请输入推荐人手机号或用户名（可选）"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),w(E,{modelValue:A.value,"onUpdate:modelValue":a[9]||(a[9]=e=>A.value=e),title:"选择用户头像",multiple:!1,accept:"image/*",onConfirm:W},null,8,["modelValue"])],64)}}}),[["__scopeId","data-v-faeae41a"]]);export{K as default};
