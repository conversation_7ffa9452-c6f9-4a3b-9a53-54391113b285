// 优化 Element Plus 组件库默认样式

:root {
  // 系统主色
  --main-color: var(--el-color-primary);
  --el-color-white: white !important;
  --el-color-black: white !important;
  // 输入框边框颜色
  // --el-border-color: #E4E4E7 !important; // DCDFE6
  // 按钮粗度
  --el-font-weight-primary: 400 !important;

  --el-component-custom-height: 36px !important;

  --el-component-size: var(--el-component-custom-height) !important;

  // 边框、按钮圆角...
  --el-border-radius-base: calc(var(--custom-radius) / 3 + 2px) !important;

  --el-border-radius-small: 10px !important;
  --el-messagebox-border-radius: 10px !important;

  .region .el-radio-button__original-radio:checked + .el-radio-button__inner {
    color: var(--main-color);
  }
}

// el-card 背景色跟系统背景色保持一致
html.dark .el-card {
  --el-card-bg-color: var(--art-main-bg-color) !important;
}

// 修改 el-pagination 大小
.el-pagination--default {
  --el-pagination-button-width: 32px !important;
  --el-pagination-button-height: var(--el-pagination-button-width) !important;

  .el-select--default .el-select__wrapper {
    min-height: var(--el-pagination-button-width) !important;
  }

  .el-pagination__jump .el-input {
    height: var(--el-pagination-button-width) !important;
  }
}

.el-pager li {
  padding: 0 10px !important;
  // border: 1px solid red !important;
}

// 优化菜单折叠展开动画（提升动画流畅度）
.el-menu.el-menu--inline {
  transition: max-height 0.26s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

// 优化菜单 item hover 动画（提升鼠标跟手感）
.el-sub-menu__title,
.el-menu-item {
  transition: background-color 0s !important;
}

// -------------------------------- 修改 el-size=default 组件默认高度 start --------------------------------
// 修改 el-button 高度
.el-button--default {
  height: var(--el-component-custom-height) !important;
}

// 修改 el-select 高度
.el-select--default {
  .el-select__wrapper {
    min-height: var(--el-component-custom-height) !important;
  }
}

// 修改 el-checkbox-button 高度
.el-checkbox-button--default .el-checkbox-button__inner,
// 修改 el-radio-button 高度
.el-radio-button--default .el-radio-button__inner {
  padding: 10px 15px !important;
}
// -------------------------------- 修改 el-size=default 组件默认高度 end --------------------------------

.el-pagination.is-background .btn-next,
.el-pagination.is-background .btn-prev,
.el-pagination.is-background .el-pager li {
  border-radius: 6px;
}

.el-popover {
  min-width: 80px;
}

.el-dialog {
  border-radius: 100px !important;
  border-radius: calc(var(--custom-radius) / 1.2 + 2px) !important;
  overflow: hidden;
}

.el-dialog__header {
  .el-dialog__title {
    font-size: 16px;
  }
}

.el-dialog__body {
  padding: 25px 0 !important;
  position: relative; // 为了兼容 el-pagination 样式，需要设置 relative，不然会影响 el-pagination 的样式，比如 el-pagination__jump--small 会被影响，导致 el-pagination__jump--small 按钮无法点击，详见 URL_ADDRESS.com/element-plus/element-plus/issues/5684#issuecomment-1176299275;
}

.el-dialog.el-dialog-border {
  .el-dialog__body {
    // 上边框
    &::before,
    // 下边框
    &::after {
      content: '';
      position: absolute;
      left: -16px;
      width: calc(100% + 32px);
      height: 1px;
      background-color: rgba(var(--art-gray-300-rgb), 0.56);
    }

    &::before {
      top: 0;
    }

    &::after {
      bottom: 0;
    }
  }
}

// ✅ el-message 样式优化
.el-message {
  background-color: var(--art-main-bg-color) !important;
  border: 0 !important;
  box-shadow:
    0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;

  p {
    color: #515a6e !important;
    font-size: 13px;
  }
}

// 修改 el-dropdown 样式
.el-dropdown-menu {
  padding: 6px !important;
  border-radius: 10px !important;
  border: none !important;

  .el-dropdown-menu__item {
    padding: 6px 16px !important;
    border-radius: 6px !important;

    &:hover:not(.is-disabled) {
      color: var(--art-gray-900) !important;
      background-color: var(--art-gray-200) !important;
    }
  }
}

// 隐藏 select、dropdown 的三角
.el-select__popper,
.el-dropdown__popper {
  margin-top: -6px !important;

  .el-popper__arrow {
    display: none;
  }
}

.el-dropdown-selfdefine:focus {
  outline: none !important;
}

// 处理移动端组件兼容性
@media screen and (max-width: $device-phone) {
  .el-message-box,
  .el-message,
  .el-dialog {
    width: calc(100% - 24px) !important;
  }

  .el-date-picker.has-sidebar.has-time {
    width: calc(100% - 24px);
    left: 12px !important;
  }

  .el-picker-panel *[slot='sidebar'],
  .el-picker-panel__sidebar {
    display: none;
  }

  .el-picker-panel *[slot='sidebar'] + .el-picker-panel__body,
  .el-picker-panel__sidebar + .el-picker-panel__body {
    margin-left: 0;
  }
}

// 修改el-button样式
.el-button {
  &.el-button--text {
    background-color: transparent !important;
    padding: 0 !important;

    span {
      margin-left: 0 !important;
    }
  }
}

// 修改el-tag样式
.el-tag {
  height: 26px !important;
  line-height: 26px !important;
  border: 0 !important;
  border-radius: 6px !important;
  font-weight: bold;
  transition: all 0s !important;
}

.el-checkbox-group {
  &.el-table-filter__checkbox-group label.el-checkbox {
    height: 17px !important;

    .el-checkbox__label {
      font-weight: 400 !important;
    }
  }
}

.el-checkbox {
  .el-checkbox__inner {
    width: 18px !important;
    height: 18px !important;
    border-radius: 4px !important;

    &::before {
      content: '';
      height: 3px !important;
      top: 6px !important;
      background-color: #fff !important;
      transform: scale(0.6) !important;
    }

    &::after {
      width: 4px;
      height: 8px;
      left: 0;
      right: 0;
      top: 0;
      bottom: 4px;
      margin: auto;
      border: 2px solid var(--el-checkbox-checked-icon-color);
      border-left: 0;
      border-top: 0;
    }
  }
}

.el-notification .el-notification__icon {
  font-size: 22px !important;
}

// 修改 el-message-box 样式
.el-message-box__headerbtn .el-message-box__close,
.el-dialog__headerbtn .el-dialog__close {
  color: var(--art-gray-500) !important;
  top: 7px !important;
  right: 7px !important;
  padding: 7px !important;
  border-radius: 5px !important;
  transition: all 0.3s !important;

  &:hover {
    background-color: var(--art-gray-200) !important;
    color: var(--art-gray-800) !important;
  }
}

.el-message-box {
  padding: 25px 20px !important;
}

.el-message-box__title {
  font-weight: 500 !important;
}

.el-table__column-filter-trigger i {
  color: var(--main-color) !important;
  margin: -3px 0 0 2px;
}

// 去除 el-dropdown 鼠标放上去出现的边框
.el-tooltip__trigger:focus-visible {
  outline: unset;
}

// ipad 表单右侧按钮优化
@media screen and (max-width: $device-ipad-pro) {
  .el-table-fixed-column--right {
    padding-right: 0 !important;

    .el-button {
      margin: 5px 10px 5px 0 !important;
    }
  }
}

.login-out-dialog {
  padding: 30px 20px !important;
  border-radius: 10px !important;
}

// 修改 dialog 动画
.dialog-fade-enter-active {
  .el-dialog:not(.is-draggable) {
    animation: dialog-open 0.3s cubic-bezier(0.32, 0.14, 0.15, 0.86);

    // 修复 el-dialog 动画后宽度不自适应问题
    .el-select__selected-item {
      display: inline-block;
    }
  }
}

.dialog-fade-leave-active {
  animation: fade-out 0.2s linear;

  .el-dialog:not(.is-draggable) {
    animation: dialog-close 0.5s;
  }
}

@keyframes dialog-open {
  0% {
    opacity: 0;
    transform: scale(0.2);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes dialog-close {
  0% {
    opacity: 1;
    transform: scale(1);
  }

  100% {
    opacity: 0;
    transform: scale(0.2);
  }
}

// 遮罩层动画
@keyframes fade-out {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

// 修改 el-select 样式
.el-select__popper:not(.el-tree-select__popper) {
  .el-select-dropdown__list {
    padding: 5px !important;

    .el-select-dropdown__item {
      height: 34px !important;
      line-height: 34px !important;
      border-radius: 6px !important;

      &.is-selected {
        color: var(--art-gray-900) !important;
        font-weight: 400 !important;
        background-color: var(--art-gray-200) !important;
        margin-bottom: 4px !important;
      }

      &:hover {
        background-color: var(--art-gray-200) !important;
      }
    }

    .el-select-dropdown__item:hover ~ .is-selected,
    .el-select-dropdown__item.is-selected:has(~ .el-select-dropdown__item:hover) {
      background-color: transparent !important;
    }
  }
}

// 修改 el-tree-select 样式
.el-tree-select__popper {
  .el-select-dropdown__list {
    padding: 5px !important;

    .el-tree-node {
      .el-tree-node__content {
        height: 36px !important;
        border-radius: 6px !important;

        &:hover {
          background-color: var(--art-gray-200) !important;
        }
      }
    }
  }
}

// 实现水波纹在文字下面效果
.el-button > span {
  position: relative;
  z-index: 10;
}
