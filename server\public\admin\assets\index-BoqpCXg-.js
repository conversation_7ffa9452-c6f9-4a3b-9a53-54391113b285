var e=Object.defineProperty,t=Object.defineProperties,r=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable,i=(t,r,o)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[r]=o;import{C as l}from"./index-CS_UjWPV.js";import{R as s}from"./roleApi-DTgIzzjr.js";import{m as n,r as d,C as m,D as c,G as b,x as j,W as u,u as y,a6 as g,E as x,p as f,aQ as h}from"./vendor-8T3zXQLl.js";import{_}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-Wowen7jt.js";import"./index-1JOyfOxu.js";import"./index-DdiMqMK0.js";import"./index-Zs-Thxwm.js";/* empty css                   *//* empty css                      *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./index-B5FHwYQA.js";/* empty css                *//* empty css               *//* empty css                  *//* empty css               */import"./el-form-item-l0sNRNKZ.js";/* empty css                       *//* empty css                 */import"./index-DS_-tevW.js";import"./index-MG-hoaSG.js";/* empty css                  *//* empty css                    */const w=n((v=((e,t)=>{for(var r in t||(t={}))a.call(t,r)&&i(e,r,t[r]);if(o)for(var r of o(t))p.call(t,r)&&i(e,r,t[r]);return e})({},{name:"Role"}),t(v,r({__name:"index",setup(e){const t=d([]),r={api:{list:s.getRoleList,create:e=>s.createRole(e),update:(e,t)=>s.updateRole(Number(e),t),delete:e=>s.deleteRole(Number(e))},columns:[{type:"selection"},{type:"index",label:"序号"},{prop:"roleName",label:"角色名称"},{prop:"roleCode",label:"角色编码"},{prop:"des",label:"描述"},{prop:"enable",label:"状态",formatter:e=>{const t=e.enable?{type:"success",text:"启用"}:{type:"danger",text:"禁用"};return f(h,{type:t.type},(()=>t.text))}},{prop:"date",label:"创建时间",sortable:!0,formatter:e=>w(e.date)}],search:{enabled:!1},actions:{enabled:!0,create:{enabled:!0,text:"新增角色",type:"primary"},edit:{enabled:!0},delete:{enabled:!0,confirmText:"确定要删除这个角色吗？"},custom:[{type:"view",text:"权限设置",handler:e=>_(e)}]},dialog:{enabled:!0,width:"600px",titles:{create:"新增角色",edit:"编辑角色"},formConfig:[{prop:"roleName",label:"角色名称",type:"input",required:!0,span:12},{prop:"roleCode",label:"角色编码",type:"input",required:!0,span:12},{prop:"des",label:"描述",type:"textarea",span:24},{prop:"enable",label:"状态",type:"switch",span:12}]},table:{rowKey:"id",stripe:!0,border:!1}},o=e=>{},a=(e,t)=>{},p=e=>{},i=e=>{t.value=e},n=()=>{x.success("同步权限功能开发中...")},_=e=>{x.success("权限设置功能开发中...")},w=e=>new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}).replace(/\//g,"-");return(e,t)=>(c(),m(l,{config:r,onCreate:o,onUpdate:a,onDelete:p,onSelectionChange:i},{"header-actions":b((({selectedRows:e})=>[j(y(g),{onClick:n},{default:b((()=>t[0]||(t[0]=[u(" 同步权限 ")]))),_:1,__:[0]})])),_:1}))}}))));var v;const O=_(w,[["__scopeId","data-v-05241a63"]]);export{O as default};
