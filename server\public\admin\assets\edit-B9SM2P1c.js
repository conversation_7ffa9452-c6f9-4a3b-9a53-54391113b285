var e=Object.defineProperty,l=Object.defineProperties,a=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,r=(l,a,t)=>a in l?e(l,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[a]=t,i=(e,l)=>{for(var a in l||(l={}))s.call(l,a)&&r(e,a,l[a]);if(t)for(var a of t(l))o.call(l,a)&&r(e,a,l[a]);return e},n=(e,l,a)=>new Promise(((t,s)=>{var o=e=>{try{i(a.next(e))}catch(l){s(l)}},r=e=>{try{i(a.throw(e))}catch(l){s(l)}},i=e=>e.done?t(e.value):Promise.resolve(e.value).then(o,r);i((a=a.apply(e,l)).next())}));import{A as u}from"./index-Bcx6y5fH.js";/* empty css                *//* empty css                 *//* empty css                        *//* empty css                       *//* empty css                 */import"./el-form-item-l0sNRNKZ.js";/* empty css                *//* empty css                  */import{m as d,r as c,d as m,M as p,o as v,P as g,D as _,x as f,G as h,R as y,X as b,a6 as j,W as w,aT as V,a8 as x,a2 as N,a3 as O,u as k,aN as P,aW as U,aq as C,E as D,Y as E,aI as I,at as R}from"./vendor-8T3zXQLl.js";import{R as q}from"./index-BgthDRX_.js";import{M as z}from"./index-7x-iptGQ.js";import{N as A}from"./noticeApi-DW6ppHlN.js";import{_ as G}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                   *//* empty css                  *//* empty css                      *//* empty css                  *//* empty css                     *//* empty css                    */const M={class:"notice-edit-page"},W={class:"header-content"},H={class:"title-section"},J={class:"action-section"},K={class:"edit-layout"},L={class:"left-panel"},S={class:"cover-upload-area"},T={key:1,class:"uploaded-image"},X=["src"],Y={class:"image-overlay"},Z={class:"right-panel"},B={class:"editor-container"};var F;const Q=G(d((F=i({},{name:"NoticeEdit"}),l(F,a({__name:"edit",setup(e){const l=E(),a=R(),t=c(),s=c(!1),o=c(!1),r=c(!1),d=m((()=>!!a.params.id)),G=m((()=>a.params.id)),F=p({title:"",content:"",cover_image:"",is_top:0,status:1,sort:0}),Q={title:[{required:!0,message:"请输入公告标题",trigger:"blur"},{max:200,message:"标题不能超过200个字符",trigger:"blur"},{min:1,message:"公告标题不能为空",trigger:"blur"}],content:[{required:!0,message:"请输入公告内容",trigger:"blur"},{min:1,message:"公告内容不能为空",trigger:"blur"}],is_top:[{type:"number",message:"置顶状态必须是数字",trigger:"change"}],status:[{type:"number",message:"状态必须是数字",trigger:"change"}],sort:[{type:"number",min:0,max:9999,message:"排序值必须在0-9999之间",trigger:"blur"}]},$=()=>n(this,null,(function*(){var e,a,t;if(d.value)try{o.value=!0;const l=Number(G.value);if(!l||isNaN(l))throw new Error("无效的公告ID");const s=yield A.getNoticeDetail(l);Object.assign(F,{title:s.title||"",content:s.content||"",cover_image:s.cover_image||"",is_top:null!=(e=s.is_top)?e:0,status:null!=(a=s.status)?a:1,sort:null!=(t=s.sort)?t:0})}catch(s){const e=(null==s?void 0:s.message)||"加载公告数据失败";D.error(e),yield l.push("/system/article/notice")}finally{o.value=!1}})),ee=()=>n(this,null,(function*(){if(t.value)try{yield t.value.validate(),s.value=!0;const e=i({},F);if(d.value){const l=Number(G.value);if(!l||isNaN(l))throw new Error("无效的公告ID");yield A.updateNotice(l,e),D.success("公告更新成功")}else yield A.createNotice(e),D.success("公告保存成功");yield l.push("/system/article/notice")}catch(e){if(!1!==e){const l=(null==e?void 0:e.message)||(d.value?"更新失败":"保存失败");D.error(l)}}finally{s.value=!1}})),le=e=>{e.length>0&&(F.cover_image=e[0].file_path)},ae=()=>n(this,null,(function*(){try{yield I.confirm("确定要取消吗？未保存的内容将丢失。","提示",{type:"warning"}),yield l.push("/system/article/notice")}catch(e){}}));return v((()=>{$()})),(e,l)=>{const a=j,o=V,i=O,n=N,c=U,m=P,p=C,v=x;return _(),g("div",M,[f(o,{class:"page-header",shadow:"never"},{default:h((()=>[y("div",W,[y("div",H,[y("h2",null,b(d.value?"编辑公告":"新增公告"),1)]),y("div",J,[f(a,{onClick:ae},{default:h((()=>l[9]||(l[9]=[w("取消")]))),_:1,__:[9]}),f(a,{type:"primary",onClick:ee,loading:s.value},{default:h((()=>l[10]||(l[10]=[w("保存")]))),_:1,__:[10]},8,["loading"])])])])),_:1}),f(o,{class:"form-content",shadow:"never"},{default:h((()=>[y("div",K,[y("div",L,[f(v,{ref_key:"formRef",ref:t,model:F,rules:Q,"label-width":"80px","label-position":"left"},{default:h((()=>[f(n,{label:"公告标题",prop:"title"},{default:h((()=>[f(i,{modelValue:F.title,"onUpdate:modelValue":l[0]||(l[0]=e=>F.title=e),placeholder:"请输入公告标题",maxlength:"200","show-word-limit":""},null,8,["modelValue"])])),_:1}),f(n,{label:"封面图片",prop:"cover_image"},{default:h((()=>[y("div",S,[F.cover_image?(_(),g("div",T,[y("img",{src:k(u)(F.cover_image),alt:"封面图片",class:"cover-preview"},null,8,X),y("div",Y,[f(a,{size:"small",onClick:l[2]||(l[2]=e=>r.value=!0)},{default:h((()=>l[12]||(l[12]=[w("更换")]))),_:1,__:[12]}),f(a,{size:"small",type:"danger",onClick:l[3]||(l[3]=e=>F.cover_image="")},{default:h((()=>l[13]||(l[13]=[w("删除")]))),_:1,__:[13]})])])):(_(),g("div",{key:0,class:"upload-placeholder",onClick:l[1]||(l[1]=e=>r.value=!0)},l[11]||(l[11]=[y("div",{class:"upload-icon"},"+",-1)]))),l[14]||(l[14]=y("div",{class:"upload-hint"},"建议尺寸：240*240像素",-1)),f(z,{modelValue:r.value,"onUpdate:modelValue":l[4]||(l[4]=e=>r.value=e),multiple:!1,accept:"image/*",title:"选择封面图片",onConfirm:le},null,8,["modelValue"])])])),_:1}),f(n,{label:"置顶状态",prop:"is_top"},{default:h((()=>[f(m,{modelValue:F.is_top,"onUpdate:modelValue":l[5]||(l[5]=e=>F.is_top=e)},{default:h((()=>[f(c,{value:0},{default:h((()=>l[15]||(l[15]=[w("不置顶")]))),_:1,__:[15]}),f(c,{value:1},{default:h((()=>l[16]||(l[16]=[w("置顶")]))),_:1,__:[16]})])),_:1},8,["modelValue"])])),_:1}),f(n,{label:"显示状态",prop:"status"},{default:h((()=>[f(m,{modelValue:F.status,"onUpdate:modelValue":l[6]||(l[6]=e=>F.status=e)},{default:h((()=>[f(c,{value:1},{default:h((()=>l[17]||(l[17]=[w("显示")]))),_:1,__:[17]}),f(c,{value:0},{default:h((()=>l[18]||(l[18]=[w("隐藏")]))),_:1,__:[18]})])),_:1},8,["modelValue"])])),_:1}),f(n,{label:"排序",prop:"sort"},{default:h((()=>[f(p,{modelValue:F.sort,"onUpdate:modelValue":l[7]||(l[7]=e=>F.sort=e),min:0,max:9999,placeholder:"数值越大越靠前",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])]),y("div",Z,[l[19]||(l[19]=y("div",{class:"panel-title"},"公告内容",-1)),f(v,{model:F,"label-width":"0"},{default:h((()=>[f(n,{prop:"content"},{default:h((()=>[y("div",B,[f(q,{modelValue:F.content,"onUpdate:modelValue":l[8]||(l[8]=e=>F.content=e),height:"600px",mode:"article",placeholder:"请输入公告内容..."},null,8,["modelValue"])])])),_:1})])),_:1},8,["model"])])])])),_:1})])}}})))),[["__scopeId","data-v-72936271"]]);export{Q as default};
