<!-- 智能图片组件 - 自动处理URL -->
<template>
  <ElImage
    :src="resolvedSrc"
    :alt="alt"
    :fit="fit"
    :loading="loading"
    :lazy="lazy"
    :preview-src-list="previewSrcList"
    :z-index="zIndex"
    :initial-index="initialIndex"
    :close-on-press-escape="closeOnPressEscape"
    :preview-teleported="previewTeleported"
    :hide-on-click-modal="hideOnClickModal"
    v-bind="$attrs"
    @load="handleLoad"
    @error="handleError"
    @switch="handleSwitch"
    @close="handleClose"
  >
    <template #placeholder>
      <slot name="placeholder">
        <div class="image-placeholder">
          <ElIcon><Picture /></ElIcon>
        </div>
      </slot>
    </template>
    
    <template #error>
      <slot name="error">
        <div class="image-error">
          <ElIcon><Picture /></ElIcon>
          <span>加载失败</span>
        </div>
      </slot>
    </template>
  </ElImage>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElImage, ElIcon } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'
import { resolveFileUrl, resolveImageUrl } from '@/utils/url'

interface Props {
  src?: string | null
  alt?: string
  fit?: 'fill' | 'contain' | 'cover' | 'none' | 'scale-down'
  loading?: 'eager' | 'lazy'
  lazy?: boolean
  previewSrcList?: string[]
  zIndex?: number
  initialIndex?: number
  closeOnPressEscape?: boolean
  previewTeleported?: boolean
  hideOnClickModal?: boolean
  fallback?: string // 回退图片
  autoResolve?: boolean // 是否自动解析URL，默认true
}

interface Emits {
  (e: 'load', event: Event): void
  (e: 'error', event: Event): void
  (e: 'switch', index: number): void
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  fit: 'cover',
  loading: 'lazy',
  lazy: true,
  closeOnPressEscape: true,
  previewTeleported: true,
  hideOnClickModal: false,
  autoResolve: true
})

const emit = defineEmits<Emits>()

// 解析后的图片URL
const resolvedSrc = computed(() => {
  if (!props.src) {
    return props.fallback || ''
  }

  // 如果禁用自动解析，直接返回原始URL
  if (!props.autoResolve) {
    return props.src
  }

  // 自动解析URL
  return resolveImageUrl(props.src, props.fallback)
})

// 事件处理
const handleLoad = (event: Event) => {
  emit('load', event)
}

const handleError = (event: Event) => {
  emit('error', event)
}

const handleSwitch = (index: number) => {
  emit('switch', index)
}

const handleClose = () => {
  emit('close')
}
</script>

<style scoped>
.image-placeholder,
.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--el-text-color-placeholder);
  background-color: var(--el-fill-color-light);
}

.image-placeholder .el-icon,
.image-error .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.image-error span {
  font-size: 12px;
}
</style>
