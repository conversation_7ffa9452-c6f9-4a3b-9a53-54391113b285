var e=Object.defineProperty,r=Object.defineProperties,a=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,l=(r,a,o)=>a in r?e(r,a,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[a]=o;import{u as i,e as d,R as n}from"./index-1JOyfOxu.js";/* empty css                *//* empty css                  *//* empty css                    */import"./el-form-item-l0sNRNKZ.js";/* empty css                 */import{_ as p}from"./index-gmCgevLJ.js";import{_ as u}from"./LoginLeftView-mva0jdOK.js";import{m,r as c,M as g,P as f,x as v,R as w,X as h,u as y,G as b,a8 as _,Y as P,B as j,Z as x,D as V,a2 as $,a3 as O,a7 as E,a4 as L,W as M,a5 as T,C as k,a6 as I,E as R}from"./vendor-8T3zXQLl.js";import{_ as U}from"./_plugin-vue_export-helper-BCo6x5W8.js";const q={class:"login register"},B={class:"right-wrap"},C={class:"header"},D={class:"login-wrap"},G={class:"form"},K={class:"title"},A={class:"sub-title"},F={style:{"margin-top":"15px"}},H={class:"footer"},J=m((S=((e,r)=>{for(var a in r||(r={}))t.call(r,a)&&l(e,a,r[a]);if(o)for(var a of o(r))s.call(r,a)&&l(e,a,r[a]);return e})({},{name:"Register"}),r(S,a({__name:"index",setup(e){const{t:r}=i(),a=P(),o=c(),t=d.systemInfo.name,s=c(!1),l=g({username:"",password:"",confirmPassword:"",agreement:!1}),m=g({username:[{required:!0,message:r("register.placeholder[0]"),trigger:"blur"},{min:3,max:20,message:r("register.rule[2]"),trigger:"blur"}],password:[{required:!0,validator:(e,a,t)=>{var s;""===a?t(new Error(r("register.placeholder[1]"))):(""!==l.confirmPassword&&(null==(s=o.value)||s.validateField("confirmPassword")),t())},trigger:"blur"},{min:6,message:r("register.rule[3]"),trigger:"blur"}],confirmPassword:[{required:!0,validator:(e,a,o)=>{""===a?o(new Error(r("register.rule[0]"))):a!==l.password?o(new Error(r("register.rule[1]"))):o()},trigger:"blur"}],agreement:[{validator:(e,a,o)=>{a?o():o(new Error(r("register.rule[4]")))},trigger:"change"}]}),U=()=>{return e=this,r=null,a=function*(){if(o.value)try{yield o.value.validate(),s.value=!0,setTimeout((()=>{s.value=!1,R.success("注册成功"),J()}),1e3)}catch(e){}},new Promise(((o,t)=>{var s=e=>{try{i(a.next(e))}catch(r){t(r)}},l=e=>{try{i(a.throw(e))}catch(r){t(r)}},i=e=>e.done?o(e.value):Promise.resolve(e.value).then(s,l);i((a=a.apply(e,r)).next())}));var e,r,a},J=()=>{setTimeout((()=>{a.push(n.Login)}),1e3)};return(e,r)=>{const a=u,i=p,d=O,c=$,g=j("router-link"),P=L,R=I,J=_,S=x("ripple");return V(),f("div",q,[v(a),w("div",B,[w("div",C,[v(i,{class:"icon"}),w("h1",null,h(y(t)),1)]),w("div",D,[w("div",G,[w("h3",K,h(e.$t("register.title")),1),w("p",A,h(e.$t("register.subTitle")),1),v(J,{ref_key:"formRef",ref:o,model:y(l),rules:y(m),"label-position":"top"},{default:b((()=>[v(c,{prop:"username"},{default:b((()=>[v(d,{modelValue:y(l).username,"onUpdate:modelValue":r[0]||(r[0]=e=>y(l).username=e),modelModifiers:{trim:!0},placeholder:e.$t("register.placeholder[0]")},null,8,["modelValue","placeholder"])])),_:1}),v(c,{prop:"password"},{default:b((()=>[v(d,{modelValue:y(l).password,"onUpdate:modelValue":r[1]||(r[1]=e=>y(l).password=e),modelModifiers:{trim:!0},placeholder:e.$t("register.placeholder[1]"),type:"password",autocomplete:"off","show-password":""},null,8,["modelValue","placeholder"])])),_:1}),v(c,{prop:"confirmPassword"},{default:b((()=>[v(d,{modelValue:y(l).confirmPassword,"onUpdate:modelValue":r[2]||(r[2]=e=>y(l).confirmPassword=e),modelModifiers:{trim:!0},placeholder:e.$t("register.placeholder[2]"),type:"password",autocomplete:"off",onKeyup:E(U,["enter"]),"show-password":""},null,8,["modelValue","placeholder"])])),_:1}),v(c,{prop:"agreement"},{default:b((()=>[v(P,{modelValue:y(l).agreement,"onUpdate:modelValue":r[3]||(r[3]=e=>y(l).agreement=e)},{default:b((()=>[M(h(e.$t("register.agreeText"))+" ",1),v(g,{style:{color:"var(--main-color)","text-decoration":"none"},to:"/privacy-policy"},{default:b((()=>[M(h(e.$t("register.privacyPolicy")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1}),w("div",F,[T((V(),k(R,{class:"register-btn",type:"primary",onClick:U,loading:y(s)},{default:b((()=>[M(h(e.$t("register.submitBtnText")),1)])),_:1},8,["loading"])),[[S]])]),w("div",H,[w("p",null,[M(h(e.$t("register.hasAccount"))+" ",1),v(g,{to:y(n).Login},{default:b((()=>[M(h(e.$t("register.toLogin")),1)])),_:1},8,["to"])])])])),_:1},8,["model","rules"])])])])])}}}))));var S;const W=U(J,[["__scopeId","data-v-79bc0ed4"]]);export{W as default};
