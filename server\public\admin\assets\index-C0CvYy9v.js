var e=Object.defineProperty,t=Object.defineProperties,a=Object.getOwnPropertyDescriptors,n=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,r=(t,a,n)=>a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[a]=n,s=(e,t,a)=>new Promise(((n,o)=>{var l=e=>{try{s(a.next(e))}catch(t){o(t)}},r=e=>{try{s(a.throw(e))}catch(t){o(t)}},s=e=>e.done?n(e.value):Promise.resolve(e.value).then(l,r);s((a=a.apply(e,t)).next())}));import{_ as i,A as p}from"./useTableColumns-Bny9oEJ9.js";import{_ as c}from"./index-DacxPkpy.js";import{u as m}from"./useTable-CBupmBcj.js";import{A as u,a as d}from"./admin-dialog-BHoUZFK3.js";import{k as g,r as h,d as f,P as y,D as b,x as v,G as j,u as x,i as w,a6 as P,W as C,aQ as T,l as _,aR as k,aB as O,aI as z,E as A}from"./vendor-84Inc-Pt.js";import{_ as B}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-TSQrMSQp.js";/* empty css                   *//* empty css                      *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";import"./index-XUvv1IdG.js";/* empty css                  *//* empty css                  *//* empty css                *//* empty css               *//* empty css               */import"./el-form-item-l0sNRNKZ.js";const S={class:"user-page art-full-height"},D=g(($=((e,t)=>{for(var a in t||(t={}))o.call(t,a)&&r(e,a,t[a]);if(n)for(var a of n(t))l.call(t,a)&&r(e,a,t[a]);return e})({},{name:"AdminManagement"}),t($,a({__name:"index",setup(e){const{getAdminList:t}=d,a=h(!1),n=h("add"),o=h(null),l=h([]),{columns:r,columnChecks:g,tableData:B,isLoading:D,paginationState:$,searchData:L,onPageSizeChange:R,onCurrentPageChange:F,refreshAll:I}=m({core:{apiFn:t,apiParams:{current:1,size:20,username:"",nickname:"",status:""},columnsFactory:()=>[{type:"selection"},{type:"index",width:60,label:"序号"},{prop:"username",label:"用户名",minWidth:120},{prop:"nickname",label:"昵称"},{prop:"email",label:"邮箱"},{prop:"phone",label:"手机号"},{prop:"roles",label:"角色",minWidth:150,formatter:e=>e.roles&&0!==e.roles.length?_("div",{class:"flex flex-wrap gap-1"},e.roles.map((e=>_(k,{key:e.id,type:"primary",size:"small",style:"margin-right: 4px; margin-bottom: 2px;"},(()=>e.name))))):_(k,{type:"info",size:"small"},(()=>"无角色"))},{prop:"status",label:"状态",width:80,formatter:e=>_(k,{type:1===e.status?"success":"danger",size:"small"},(()=>e.statusText))},{prop:"lastLoginTime",label:"最后登录",formatter:e=>e.lastLoginTime||"从未登录"},{prop:"createdAt",label:"创建时间"},{prop:"operation",label:"操作",width:240,fixed:"right",formatter:e=>{const t=[];return t.push(_(O,{content:"编辑",placement:"top"},(()=>_(p,{type:"edit",onClick:()=>E("edit",e)})))),t.push(_(O,{content:"重置密码",placement:"top"},(()=>_(p,{type:"view",onClick:()=>M(e)})))),t.push(_(O,{content:1===e.status?"禁用":"启用",placement:"top"},(()=>_(p,{type:"more",onClick:()=>Q(e)})))),t.push(_(O,{content:"删除",placement:"top"},(()=>_(p,{type:"delete",onClick:()=>U(e)})))),_("div",{class:"flex gap-2"},t)}}]}}),W=e=>{l.value=e},E=(e,t)=>{n.value=e,o.value=t||null,a.value=!0},G=()=>{a.value=!1,I()},M=e=>s(this,null,(function*(){try{const{value:t}=yield z.prompt(`请输入 ${e.username} 的新密码`,"重置密码",{confirmButtonText:"确定",cancelButtonText:"取消",inputType:"password",inputValidator:e=>e?!(e.length<6)||"密码长度不能少于6位":"密码不能为空"});yield d.resetPassword(e.id,t),A.success("密码重置成功")}catch(t){"cancel"!==t&&A.error("重置密码失败")}})),Q=e=>s(this,null,(function*(){try{const t=1===e.status?"禁用":"启用";yield z.confirm(`确定要${t}管理员 ${e.username} 吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield d.toggleStatus(e.id),A.success(`${t}成功`),I()}catch(t){"cancel"!==t&&A.error("状态切换失败")}})),U=e=>s(this,null,(function*(){try{yield z.confirm(`确定要删除管理员 ${e.username} 吗？此操作不可恢复！`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield d.deleteAdmin(e.id),A.success("删除成功"),I()}catch(t){"cancel"!==t&&A.error("删除失败")}}));return f((()=>{L()})),(e,t)=>(b(),y("div",S,[v(x(T),{class:"art-table-card",shadow:"never"},{default:j((()=>[v(i,{columns:x(g),"onUpdate:columns":t[1]||(t[1]=e=>w(g)?g.value=e:null),onRefresh:x(I)},{left:j((()=>[v(x(P),{onClick:t[0]||(t[0]=e=>E("add"))},{default:j((()=>t[3]||(t[3]=[C("新增管理员")]))),_:1,__:[3]})])),_:1},8,["columns","onRefresh"]),v(c,{loading:x(D),data:x(B),columns:x(r),pagination:x($),"table-config":{rowKey:"id"},layout:{marginTop:10},"onRow:selectionChange":W,"onPagination:sizeChange":x(R),"onPagination:currentChange":x(F)},null,8,["loading","data","columns","pagination","onPagination:sizeChange","onPagination:currentChange"]),v(u,{visible:a.value,"onUpdate:visible":t[2]||(t[2]=e=>a.value=e),type:n.value,"admin-data":o.value,onSubmit:G},null,8,["visible","type","admin-data"])])),_:1})]))}}))));var $;const L=B(D,[["__scopeId","data-v-015432e9"]]);export{L as default};
