<?php
declare(strict_types=1);

namespace app\common\service;

use app\common\model\Menu;
use app\common\model\Admin;

/**
 * 菜单服务类
 */
class MenuService
{
    /**
     * 获取菜单列表 - 后端控制模式
     * 返回符合前端 AppRouteRecord 结构的数据
     */
    public static function getMenuList($adminId)
    {
        $admin = Admin::with(['role.menu.permission'])->find($adminId);
        if (!$admin) {
            throw new \Exception('管理员不存在');
        }

        // 获取管理员有权限的菜单
        $menuIds = [];
        foreach ($admin->role as $role) {
            foreach ($role->menu as $menu) {
                $menuIds[] = $menu->id;
            }
        }
        $menuIds = array_unique($menuIds);

        // 获取菜单数据并构建树形结构
        $menus = Menu::with(['permission'])
            ->where('id', 'in', $menuIds)
            ->where('status', 1)
            ->order('sort asc, id asc')
            ->select()
            ->toArray();

        $menuTree = self::buildMenuTree($menus);

        return [
            'menuList' => self::formatMenuForFrontend($menuTree)
        ];
    }

    /**
     * 获取所有菜单 - 用于菜单管理页面
     * 不进行权限过滤，显示所有菜单供管理
     */
    public static function getAllMenusForManagement()
    {
        // 获取所有菜单数据并构建树形结构
        $menus = Menu::with(['permission'])
            ->order('sort asc, id asc')
            ->select()
            ->toArray();

        $menuTree = self::buildMenuTree($menus);

        return [
            'menuList' => self::formatMenuForFrontend($menuTree)
        ];
    }

    /**
     * 构建菜单树形结构
     */
    private static function buildMenuTree($menus, $parentId = 0)
    {
        $tree = [];
        foreach ($menus as $menu) {
            if ($menu['parent_id'] == $parentId) {
                $children = self::buildMenuTree($menus, $menu['id']);
                if ($children) {
                    $menu['children'] = $children;
                }
                $tree[] = $menu;
            }
        }
        return $tree;
    }

    /**
     * 格式化菜单数据为前端 AppRouteRecord 结构
     */
    private static function formatMenuForFrontend($menus)
    {
        $result = [];
        foreach ($menus as $menu) {
            $item = [
                'id' => $menu['id'],
                'parent_id' => $menu['parent_id'],
                'name' => $menu['name'],
                'path' => $menu['path'],
                'component' => $menu['component'] ?: '', // 空组件路径将由前端路由系统处理
                'meta' => [
                    'title' => $menu['title'],
                    'icon' => $menu['icon'],
                    'keepAlive' => (bool)$menu['keep_alive'],
                    'isHide' => !$menu['status'],
                    'isIframe' => (bool)$menu['is_iframe'],
                ]
            ];

            // 处理外部链接
            if ($menu['external_link']) {
                $item['meta']['link'] = $menu['external_link'];
            }

            // 处理按钮权限
            if (isset($menu['permission']) && !empty($menu['permission'])) {
                $authList = [];
                foreach ($menu['permission'] as $permission) {
                    $authList[] = [
                        'title' => $permission['name'],
                        'authMark' => $permission['code']
                    ];
                }
                if ($authList) {
                    $item['meta']['authList'] = $authList;
                }
            }

            // 递归处理子菜单
            if (isset($menu['children'])) {
                $item['children'] = self::formatMenuForFrontend($menu['children']);
            }

            $result[] = $item;
        }
        return $result;
    }

    /**
     * 创建菜单
     */
    public static function create($data)
    {
        // 验证必填字段
        if (empty($data['name'])) {
            throw new \Exception('菜单名称不能为空');
        }
        if (empty($data['title'])) {
            throw new \Exception('菜单标题不能为空');
        }

        // 准备菜单数据
        $menuData = [
            'parent_id' => $data['parent_id'] ?? 0,
            'name' => $data['name'],
            'path' => $data['path'] ?? '',
            'component' => $data['component'] ?? '',
            'title' => $data['title'],
            'icon' => $data['icon'] ?? '',
            'sort' => $data['sort'] ?? 0,
            'type' => $data['type'] ?? 1,
            'status' => $data['status'] ?? 1,
            'keep_alive' => $data['keep_alive'] ?? 0,
            'external_link' => $data['external_link'] ?? '',
            'is_iframe' => $data['is_iframe'] ?? 0,
        ];

        $menu = new Menu();
        $menu->save($menuData);
        return $menu;
    }

    /**
     * 更新菜单
     */
    public static function update($id, $data)
    {
        $menu = Menu::find($id);
        if (!$menu) {
            throw new \Exception('菜单不存在');
        }
        $menu->save($data);
        return $menu;
    }

    /**
     * 删除菜单
     */
    public static function delete($id)
    {
        $menu = Menu::find($id);
        if (!$menu) {
            throw new \Exception('菜单不存在');
        }
        
        // 检查是否有子菜单
        $children = Menu::where('parent_id', $id)->count();
        if ($children > 0) {
            throw new \Exception('请先删除子菜单');
        }
        
        $menu->delete();
        return true;
    }

    /**
     * 获取菜单树形结构（管理用）
     */
    public static function getMenuTree()
    {
        $menus = Menu::order('sort asc, id asc')->select()->toArray();
        return self::buildMenuTree($menus);
    }
}
