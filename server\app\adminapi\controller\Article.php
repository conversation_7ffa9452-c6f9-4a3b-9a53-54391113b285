<?php
declare(strict_types=1);

namespace app\adminapi\controller;

use app\common\controller\BaseController;
use app\adminapi\validate\ArticleValidate;
use app\common\service\ArticleService;
use app\common\model\Article as ArticleModel;
use think\response\Json;
use think\facade\Db;

/**
 * 文章管理控制器
 * 
 * 自动路由规则：
 * - GET /adminapi/article/list - 获取列表
 * - GET /adminapi/article/detail/{id} - 获取详情
 * - POST /adminapi/article/create - 创建
 * - PUT /adminapi/article/update/{id} - 更新
 * - DELETE /adminapi/article/delete/{id} - 删除
 * - POST /adminapi/article/batch-delete - 批量删除
 * - PUT /adminapi/article/status/{id} - 更新状态
 * - PUT /adminapi/article/publish/{id} - 发布/取消发布
 */
class Article extends BaseController
{
    /**
     * 获取列表
     */
    public function list(): Json
    {
        try {
            $params = $this->request->param();
            $result = ArticleService::getList($params);

            // 调试信息
            error_log('Article list result type: ' . gettype($result));
            error_log('Article list result: ' . json_encode($result));

            return $this->success($result, '获取成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取详情
     */
    public function detail(): Json
    {
        try {
            $id = $this->request->param('id');
            $model = ArticleService::getDetail((int)$id);

            return $this->success($model->toArray(), '获取成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 创建
     */
    public function create(): Json
    {
        try {
            $data = $this->request->post();
            
            // 数据验证
            $validate = new ArticleValidate();
            if (!$validate->scene('create')->check($data)) {
                return $this->error($validate->getError());
            }
            
            $model = ArticleService::create($data);

            return $this->success($model->toArray(), '创建成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 更新
     */
    public function update(): Json
    {
        try {
            $id = (int)$this->request->param('id');
            $data = $this->request->put();
            $data['id'] = $id; // 用于验证器检查唯一性

            // 数据验证
            $validate = new ArticleValidate();
            if (!$validate->scene('update')->check($data)) {
                return $this->error($validate->getError());
            }

            unset($data['id']); // 移除ID，避免更新主键
            $model = ArticleService::update($id, $data);

            return $this->success($model->toArray(), '更新成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除文章
     */
    public function delete()
    {
        $id = $this->request->param('id');

        try {
            $article = ArticleModel::find($id);
            if (!$article) {
                return $this->error('文章不存在，ID: ' . $id);
            }

            // 记录删除前的信息
            $title = $article->title;
            $articleId = $article->id;

            // 执行删除
            $result = $article->delete();

            if ($result) {
                return $this->success([
                    'deleted_id' => $articleId,
                    'deleted_title' => $title
                ], '删除成功');
            } else {
                return $this->error('删除失败');
            }
        } catch (\Exception $e) {
            return $this->error('删除异常：' . $e->getMessage());
        }
    }

    /**
     * 批量删除
     */
    public function batchDelete(): Json
    {
        try {
            $ids = $this->request->post('ids', []);
            
            if (empty($ids) || !is_array($ids)) {
                return $this->error('请选择要删除的数据');
            }
            
            ArticleService::batchDelete($ids);
            
            return $this->success([], '批量删除成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 更新状态
     */
    public function updateStatus(): Json
    {
        try {
            $id = (int)$this->request->param('id');
            $status = $this->request->put('status');

            // 数据验证
            $validate = new ArticleValidate();
            if (!$validate->scene('status')->check(['status' => $status])) {
                return $this->error($validate->getError());
            }

            ArticleService::updateStatus($id, (int)$status);

            return $this->success([], '状态更新成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 发布/取消发布文章
     */
    public function publish(): Json
    {
        try {
            $id = (int)$this->request->param('id');
            $isPublished = $this->request->put('is_published');

            // 数据验证
            $validate = new ArticleValidate();
            if (!$validate->scene('publish')->check(['is_published' => $isPublished])) {
                return $this->error($validate->getError());
            }

            ArticleService::updatePublishStatus($id, (int)$isPublished);

            $action = $isPublished ? '发布' : '取消发布';
            return $this->success([], $action . '成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取状态选项
     */
    public function statusOptions(): Json
    {
        try {
            $options = \app\common\model\Article::getStatusOptions();
            
            return $this->success($options, '获取成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取发布状态选项
     */
    public function publishedOptions(): Json
    {
        try {
            $options = \app\common\model\Article::getPublishedOptions();
            
            return $this->success($options, '获取成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
