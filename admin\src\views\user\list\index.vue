<!-- 用户管理 - 使用独立弹窗组件 -->
<template>
  <div>
    <CrudListPage
      ref="crudListPageRef"
      :config="userListConfig"
      @create="handleUserCreate"
      @update="handleUserUpdate"
      @delete="handleUserDelete"
      @selection-change="handleSelectionChange"
    >
      <!-- 自定义头部操作 -->
      <template #header-actions="{ selectedRows }">
        <ElButton
          v-if="selectedRows.length > 0"
          type="danger"
          @click="handleBatchDelete"
        >
          批量删除 ({{ selectedRows.length }})
        </ElButton>

        <ElButton @click="handleExport">
          导出用户
        </ElButton>
      </template>
    </CrudListPage>

    <!-- 添加用户弹窗 -->
    <UserAddDialog
      v-model:visible="addDialogVisible"
      @submit="handleAddSubmit"
    />

    <!-- 编辑用户弹窗 -->
    <UserEditDialog
      v-model:visible="editDialogVisible"
      :userData="currentUserData"
      @submit="handleEditSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, h } from 'vue'
import { ElButton, ElMessage, ElMessageBox, ElTag } from 'element-plus'
import CrudListPage from '@/components/common/crud-list-page/index.vue'
import UserAddDialog from './modules/user-add-dialog.vue'
import UserEditDialog from './modules/user-edit-dialog.vue'
import { UserService } from '@/api/usersApi'
import { resolveFileUrl } from '@/utils/url'
import type { CrudListConfig } from '@/components/common/crud-list-page/types.ts'

defineOptions({ name: 'User' })

// 组件引用
const crudListPageRef = ref()

// 选中的行
const selectedRows = ref<any[]>([])

// 弹窗相关状态
const addDialogVisible = ref(false)
const editDialogVisible = ref(false)
const currentUserData = ref<any>(null)

/**
 * 获取用户状态配置
 */
const getUserStatusConfig = (status: number) => {
  const statusMap: Record<number, { type: 'success' | 'danger' | 'info'; text: string }> = {
    1: { type: 'success', text: '正常' },
    2: { type: 'danger', text: '禁用' },
    3: { type: 'info', text: '注销' }
  }
  return statusMap[status] || { type: 'info', text: '未知' }
}

// 用户列表配置
const userListConfig: CrudListConfig = {
  // API配置
  api: {
    list: UserService.getUserList,
    create: UserService.createUser,
    update: UserService.updateUser,
    delete: UserService.deleteUser
  },

  // 表格列配置
  columns: [
    { type: 'selection' },
    { type: 'index', label: '序号' },
    {
      prop: 'avatar',
      label: '头像',
      width: 80,
      formatter: (row: any) => {
        return h('div', { class: 'user-avatar-cell' }, [
          row.avatar
            ? h('img', {
                src: resolveFileUrl(row.avatar),
                alt: '头像',
                class: 'avatar-image',
                style: {
                  width: '40px',
                  height: '40px',
                  objectFit: 'cover',
                  borderRadius: '50%',
                  border: '1px solid #ddd'
                }
              })
            : h('div', {
                class: 'no-avatar',
                style: {
                  width: '40px',
                  height: '40px',
                  borderRadius: '50%',
                  backgroundColor: '#f5f5f5',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#999',
                  fontSize: '12px',
                  border: '1px solid #ddd'
                }
              }, '无')
        ])
      }
    },
    { prop: 'nickName', label: '昵称' },
    { prop: 'userPhone', label: '电话' },
    { prop: 'referrer', label: '推荐人' },
    {
      prop: 'userStatus',
      label: '状态',
      formatter: (row: any) => {
        const config = getUserStatusConfig(row.userStatus)
        return h(ElTag, { type: config.type }, () => config.text)
      }
    },
    { prop: 'createTime', label: '创建时间', sortable: true }
  ],

  // 搜索配置
  search: {
    enabled: true,
    defaultParams: {
      name: '',
      phone: '',
      status: '',
      level: 'normal'
    },
    items: [
      {
        label: '用户名',
        prop: 'name',
        type: 'input',
        config: { clearable: true }
      },
      {
        label: '手机号',
        prop: 'phone',
        type: 'input',
        config: { clearable: true }
      },
      {
        label: '状态',
        prop: 'status',
        type: 'select',
        config: { clearable: true },
        options: [
          { label: '正常', value: 1 },
          { label: '禁用', value: 2 },
          { label: '注销', value: 3 }
        ]
      }
    ]
  },

  // 操作配置
  actions: {
    enabled: true,
    width: 120,
    create: {
      enabled: true,
      text: '新增用户',
      type: 'primary'
    },
    edit: {
      enabled: true
    },
    delete: {
      enabled: true,
      confirmText: '确定要删除这个用户吗？'
    }
  },

  // 弹窗配置 - 禁用内置弹窗，使用独立组件
  dialog: {
    enabled: false,
    width: '600px',
    titles: {
      create: '新增用户',
      edit: '编辑用户'
    },
    formConfig: [
      {
        prop: 'avatar',
        label: '头像',
        type: 'upload',
        placeholder: '请选择头像（可选）',
        span: 24,
        config: {
          pickerTitle: '选择用户头像',
          accept: 'image/*',
          uploadText: '选择头像',
          multiple: false
        }
      },
      {
        prop: 'userPhone',
        label: '手机号',
        type: 'input',
        required: true,
        placeholder: '请输入手机号',
        span: 24,
        rules: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ]
      },
      {
        prop: 'nickName',
        label: '昵称',
        type: 'input',
        required: true,
        placeholder: '请输入昵称',
        span: 24,
        rules: [
          { required: true, message: '请输入昵称', trigger: 'blur' },
          { min: 2, max: 20, message: '昵称长度必须在2-20位之间', trigger: 'blur' }
        ]
      },
      {
        prop: 'password',
        label: '密码',
        type: 'password',
        required: false,
        placeholder: '不填则不修改',
        span: 12,
        rules: [
          { min: 6, max: 32, message: '密码长度必须在6-32位之间', trigger: 'blur' }
        ]
      },
      {
        prop: 'confirmPassword',
        label: '确认密码',
        type: 'password',
        required: false,
        placeholder: '不填则不修改',
        span: 12,
        rules: [
          {
            validator: (_rule: any, _value: string, callback: Function) => {
              // 这里需要在组件中实现密码确认验证
              callback()
            },
            trigger: 'blur'
          }
        ]
      },
      {
        prop: 'payPassword',
        label: '支付密码',
        type: 'password',
        required: false,
        placeholder: '不填则不修改',
        span: 12,
        rules: [
          { pattern: /^\d{6}$/, message: '支付密码必须是6位数字', trigger: 'blur' }
        ]
      },
      {
        prop: 'confirmPayPassword',
        label: '确认支付密码',
        type: 'password',
        required: false,
        placeholder: '不填则不修改',
        span: 12,
        rules: [
          {
            validator: (_rule: any, _value: string, callback: Function) => {
              // 这里需要在组件中实现支付密码确认验证
              callback()
            },
            trigger: 'blur'
          }
        ]
      },
      {
        prop: 'referrer',
        label: '推荐人',
        type: 'input',
        placeholder: '请输入推荐人手机号或用户名（可选）',
        span: 24
      }
    ]
  },

  // 表格配置
  table: {
    rowKey: 'id',
    stripe: true,
    border: false
  }
}

// 事件处理 - 使用独立弹窗
const handleUserCreate = () => {
  addDialogVisible.value = true
}

const handleUserUpdate = (_id: string | number, userData: any) => {
  // 现在 CrudListPage 会传递完整的行数据
  currentUserData.value = userData
  editDialogVisible.value = true
}

const handleUserDelete = async (id: string | number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个用户吗？', '删除确认', { type: 'warning' })
    await UserService.deleteUser(Number(id))
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 处理添加弹窗提交
const handleAddSubmit = () => {
  addDialogVisible.value = false
  // TODO: 触发列表刷新
}

// 处理编辑弹窗提交
const handleEditSubmit = () => {
  editDialogVisible.value = false
  // TODO: 触发列表刷新
}

const handleSelectionChange = (rows: any[]) => {
  selectedRows.value = rows
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个用户吗？`,
      '批量删除',
      { type: 'warning' }
    )

    // 执行批量删除逻辑
    ElMessage.success('批量删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

const handleExport = () => {
  ElMessage.success('导出功能开发中...')
}
</script>

<style scoped lang="scss">
.user-avatar-cell {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 0;
}

.avatar-image {
  transition: transform 0.2s ease;
  cursor: pointer;
}

.avatar-image:hover {
  transform: scale(1.1);
}

.no-avatar {
  user-select: none;
}
</style>


