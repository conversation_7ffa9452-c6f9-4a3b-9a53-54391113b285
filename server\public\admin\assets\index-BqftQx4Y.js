var r=Object.defineProperty,o=Object.defineProperties,e=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,m=(o,e,t)=>e in o?r(o,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[e]=t;import{o as p}from"./index-DG9-1w7X.js";/* empty css               *//* empty css               */import a from"./CardList-GThznnsv.js";import l from"./ActiveUser-B1CQvcJ4.js";import j from"./SalesOverview-D_2LAq1D.js";import n from"./NewUser-Cyp4CrAy.js";import d from"./Dynamic-B_QrQ6oY.js";import c from"./TodoList-DYzogaXc.js";import f from"./AboutProject-BPhluDT_.js";import{k as u,P as b,D as g,x as _,G as v,aK as O,aL as y}from"./vendor-84Inc-Pt.js";import{_ as x}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-syD2yfUn.js";/* empty css                   */import"./useChart-CZIGBtkl.js";import"./index-CGmIUeDt.js";/* empty css                      *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                    *//* empty css                       *//* empty css                        */import"./avatar6-BkZJe8A3.js";const P={class:"console"},w=u((h=((r,o)=>{for(var e in o||(o={}))s.call(o,e)&&m(r,e,o[e]);if(t)for(var e of t(o))i.call(o,e)&&m(r,e,o[e]);return r})({},{name:"Console"}),o(h,e({__name:"index",setup:r=>(p().scrollToTop(),(r,o)=>{const e=O,t=y;return g(),b("div",P,[_(a),_(t,{gutter:20},{default:v((()=>[_(e,{sm:24,md:12,lg:10},{default:v((()=>[_(l)])),_:1}),_(e,{sm:24,md:12,lg:14},{default:v((()=>[_(j)])),_:1})])),_:1}),_(t,{gutter:20},{default:v((()=>[_(e,{sm:24,md:24,lg:12},{default:v((()=>[_(n)])),_:1}),_(e,{sm:24,md:12,lg:6},{default:v((()=>[_(d)])),_:1}),_(e,{sm:24,md:12,lg:6},{default:v((()=>[_(c)])),_:1})])),_:1}),_(f)])})}))));var h;const D=x(w,[["__scopeId","data-v-699cd4c1"]]);export{D as default};
