<template>
  <div class="rich-text-editor">
    <ArtWangEditor
      v-model="content"
      :height="height"
      :placeholder="placeholder"
      :toolbar-keys="toolbarKeys"
      :exclude-keys="excludeKeys"
      :upload-config="uploadConfig"
      @blur="handleBlur"
      @focus="handleFocus"
    />

    <!-- 集成媒体文件管理 -->
    <MediaPicker
      v-model="showMediaPicker"
      :multiple="true"
      accept="image/*"
      title="选择图片"
      @confirm="handleMediaConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, provide } from 'vue'
import ArtWangEditor from '@/components/core/forms/art-wang-editor/index.vue'
import MediaPicker from '@/components/MediaPicker/index.vue'
import type { MediaFile } from '@/api/mediaApi'
import { resolveFileUrl } from '@/utils/url'

defineOptions({ name: 'RichTextEditor' })

// Props 定义
interface Props {
  /** 编辑器内容 */
  modelValue?: string
  /** 编辑器高度 */
  height?: string
  /** 占位符文本 */
  placeholder?: string
  /** 编辑器模式：article(文章模式) | notice(公告模式) | simple(简单模式) */
  mode?: 'article' | 'notice' | 'simple'
  /** 是否禁用 */
  disabled?: boolean
  /** 上传配置 */
  uploadConfig?: {
    maxFileSize?: number
    maxNumberOfFiles?: number
    server?: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  height: '400px',
  placeholder: '请输入内容...',
  mode: 'article',
  disabled: false
})

// Emits 定义
const emit = defineEmits<{
  'update:modelValue': [value: string]
  blur: [value: string]
  focus: [value: string]
}>()

// 双向绑定
const content = computed({
  get: () => props.modelValue,
  set: (value: string) => emit('update:modelValue', value)
})

// 媒体选择器状态
const showMediaPicker = ref(false)

// 媒体选择器确认处理
const handleMediaConfirm = (files: MediaFile[]) => {
  if (files.length > 0 && window.tempInsertImageFn) {
    files.forEach(file => {
      // 使用resolveFileUrl生成正确的图片URL
      const imageUrl = resolveFileUrl(file.file_path)
      // 插入图片到编辑器
      window.tempInsertImageFn?.(imageUrl, file.original_name, imageUrl)
    })
    // 清理临时函数
    delete window.tempInsertImageFn
  }
}

// 提供媒体选择器控制函数给子组件
provide('openMediaPicker', () => {
  showMediaPicker.value = true
})

// 根据模式配置工具栏
const toolbarKeys = computed(() => {
  const baseTools = [
    'headerSelect',
    'bold',
    'italic',
    'underline',
    '|',
    'color',
    'bgColor',
    '|',
    'fontSize',
    'fontFamily',
    '|',
    'bulletedList',
    'numberedList',
    'todo',
    '|',
    'justifyLeft',
    'justifyCenter',
    'justifyRight',
    '|',
    'insertLink',
    'insertImage',
    '|',
    'insertTable',
    'codeBlock',
    'blockquote',
    'divider',
    '|',
    'undo',
    'redo',
    '|',
    'fullScreen'
  ]

  const articleTools = [
    'headerSelect',
    'bold',
    'italic',
    'underline',
    'through',
    '|',
    'color',
    'bgColor',
    '|',
    'fontSize',
    'fontFamily',
    '|',
    'indent',
    'delIndent',
    '|',
    'justifyLeft',
    'justifyCenter',
    'justifyRight',
    'justifyJustify',
    '|',
    'bulletedList',
    'numberedList',
    'todo',
    '|',
    'insertLink',
    'insertImage',
    'uploadImage',
    '|',
    'insertTable',
    'codeBlock',
    'blockquote',
    'divider',
    '|',
    'emotion',
    '|',
    'undo',
    'redo',
    '|',
    'fullScreen'
  ]

  const noticeTools = [
    'headerSelect',
    'bold',
    'italic',
    'underline',
    '|',
    'color',
    'bgColor',
    '|',
    'fontSize',
    '|',
    'justifyLeft',
    'justifyCenter',
    'justifyRight',
    '|',
    'bulletedList',
    'numberedList',
    '|',
    'insertLink',
    'insertImage',
    'uploadImage',
    '|',
    'blockquote',
    'divider',
    '|',
    'undo',
    'redo',
    '|',
    'fullScreen'
  ]

  const simpleTools = [
    'bold',
    'italic',
    'underline',
    '|',
    'color',
    '|',
    'bulletedList',
    'numberedList',
    '|',
    'insertLink',
    '|',
    'undo',
    'redo'
  ]

  switch (props.mode) {
    case 'article':
      return articleTools
    case 'notice':
      return noticeTools
    case 'simple':
      return simpleTools
    default:
      return baseTools
  }
})

// 排除的工具栏项
const excludeKeys = computed(() => {
  const baseExclude = ['fontFamily']
  
  if (props.mode === 'simple') {
    return [...baseExclude, 'fullScreen', 'insertTable', 'codeBlock']
  }
  
  return baseExclude
})

// 上传配置
const uploadConfig = computed(() => {
  const defaultConfig = {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    maxNumberOfFiles: 10,
    server: `${import.meta.env.VITE_API_URL}/api/common/upload/wangeditor`
  }

  return {
    ...defaultConfig,
    ...props.uploadConfig
  }
})

// 事件处理
const handleBlur = () => {
  emit('blur', content.value)
}

const handleFocus = () => {
  emit('focus', content.value)
}
</script>

<style scoped>
.rich-text-editor {
  width: 100%;
}

/* 确保编辑器在表单中的样式 */
.rich-text-editor :deep(.editor-wrapper) {
  border-radius: 6px;
  border: 1px solid var(--el-border-color);
  transition: border-color 0.2s;
}

.rich-text-editor :deep(.editor-wrapper:hover) {
  border-color: var(--el-border-color-hover);
}

.rich-text-editor :deep(.editor-wrapper:focus-within) {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
}

/* 工具栏样式优化 */
.rich-text-editor :deep(.w-e-toolbar) {
  border-bottom: 1px solid var(--el-border-color-light);
  background-color: var(--el-bg-color-page);
}

/* 编辑区域样式 */
.rich-text-editor :deep(.w-e-text-container) {
  background-color: var(--el-bg-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rich-text-editor :deep(.w-e-toolbar) {
    flex-wrap: wrap;
  }
}
</style>
