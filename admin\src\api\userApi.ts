/**
 * 用户管理API服务
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
import request from '@/utils/http'

/**
 * 用户信息接口
 */
export interface UserInfo {
  /** 用户ID */
  id: number
  /** 用户名 */
  userName: string
  /** 昵称 */
  nickName: string
  /** 性别 */
  userGender: string
  /** 手机号 */
  userPhone: string
  /** 邮箱 */
  userEmail: string
  /** 头像 */
  avatar: string
  /** 状态 */
  userStatus: number
  /** 状态文本 */
  userStatusText: string
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
}

/**
 * 用户表单数据接口
 */
export interface UserFormData {
  /** 头像 */
  avatar?: string
  /** 手机号 */
  userPhone: string
  /** 昵称 */
  nickName: string
  /** 密码 */
  password: string
  /** 支付密码 */
  payPassword?: string
  /** 推荐人 */
  referrer?: string
  /** 用户名（可选，系统自动生成） */
  userName?: string
  /** 性别 */
  userGender?: number
  /** 邮箱 */
  userEmail?: string
  /** 状态 */
  userStatus?: number
}

/**
 * 用户列表查询参数接口
 */
export interface UserListParams {
  /** 当前页码 */
  current?: number
  /** 每页数量 */
  size?: number
  /** 用户名/昵称 */
  name?: string
  /** 手机号 */
  phone?: string
  /** 状态 */
  status?: number
}

/**
 * 用户服务类
 */
export class UserService {
  /**
   * 获取用户列表
   * 
   * @param params 查询参数
   * @returns 用户列表数据
   */
  static async getUserList(params: UserListParams): Promise<{
    list: UserInfo[]
    total: number
    current: number
    size: number
  }> {
    return request.get({
      url: '/user/list',
      params
    })
  }

  /**
   * 创建用户
   * 
   * @param data 用户数据
   * @returns 创建的用户信息
   */
  static async createUser(data: UserFormData): Promise<UserInfo> {
    return request.post({
      url: '/user/create',
      data
    })
  }

  /**
   * 更新用户
   * 
   * @param id 用户ID
   * @param data 更新数据
   * @returns 更新后的用户信息
   */
  static async updateUser(id: number, data: Partial<UserFormData>): Promise<UserInfo> {
    if (!id || id <= 0) {
      throw new Error('无效的用户ID')
    }
    
    return request.put({
      url: `/user/update/${id}`,
      data
    })
  }

  /**
   * 删除用户
   * 
   * @param id 用户ID
   */
  static async deleteUser(id: number): Promise<void> {
    if (!id || id <= 0) {
      throw new Error('无效的用户ID')
    }
    
    return request.del({
      url: `/user/delete/${id}`
    })
  }
}
