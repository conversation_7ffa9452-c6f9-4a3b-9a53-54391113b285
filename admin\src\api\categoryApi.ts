/**
 * 分类管理API服务
 */
import request from '@/utils/http'

// 分类信息接口
export interface CategoryInfo {
  id: number
  name: string
  parent_id: number
  sort: number
  status: number
  status_text: string
  article_count: number
  created_at: string
  updated_at: string
}

// 分类表单数据接口
export interface CategoryFormData {
  name: string
  parent_id: number
  sort: number
  status: number
}

export class CategoryService {
  /**
   * 获取分类列表
   */
  static async getCategoryList(params: any): Promise<any> {
    return request.get({
      url: '/category/list',
      params
    })
  }

  /**
   * 创建分类
   */
  static async createCategory(data: CategoryFormData): Promise<CategoryInfo> {
    return request.post({
      url: '/category/create',
      data
    })
  }

  /**
   * 更新分类
   */
  static async updateCategory(id: number, data: Partial<CategoryFormData>): Promise<CategoryInfo> {
    return request.put({
      url: `/category/update/${id}`,
      data
    })
  }

  /**
   * 删除分类
   */
  static async deleteCategory(id: number): Promise<void> {
    return request.del({
      url: `/category/delete/${id}`
    })
  }
}
