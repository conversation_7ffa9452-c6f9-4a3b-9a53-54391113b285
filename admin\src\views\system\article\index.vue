<!-- 文章管理 - 使用通用组件 -->
<template>
  <div class="article-management">
    <h2>文章管理</h2>
    <p>请选择左侧菜单进行相应的操作</p>
  </div>
</template>

<script setup lang="ts">
// 文章管理主页面
import { ref, h } from 'vue'
import { ElButton, ElMessage, ElMessageBox, ElTag } from 'element-plus'
import CrudListPage from '@/components/common/crud-list-page/index.vue'
import { ArticleService, type ArticleInfo, type ArticleFormData } from '@/api/articleApi'
import type { CrudListConfig, CrudFormData } from '@/components/common/crud-list-page/types.ts'

defineOptions({ name: 'ArticleList' })

// 选中的行
const selectedRows = ref<any[]>([])

/**
 * 获取状态配置
 */
const getStatusConfig = (status: number) => {
  const statusMap: Record<number, { type: 'success' | 'danger' | 'info'; text: string }> = {
    1: { type: 'success', text: '启用' },
    2: { type: 'danger', text: '禁用' }
  }
  return statusMap[status] || { type: 'info', text: '未知' }
}

/**
 * 获取发布状态配置
 */
const getPublishedConfig = (isPublished: number) => {
  return isPublished 
    ? { type: 'success', text: '已发布' }
    : { type: 'warning', text: '草稿' }
}

/**
 * 获取推荐状态配置
 */
const getFeaturedConfig = (isFeatured: number) => {
  return isFeatured 
    ? { type: 'danger', text: '推荐' }
    : { type: 'info', text: '普通' }
}

// 文章管理列表配置
const articleListConfig: CrudListConfig = {
  // API配置
  api: {
    list: ArticleService.getArticleList,
    create: (data: CrudFormData) => ArticleService.createArticle(data as ArticleFormData),
    update: (id: string | number, data: CrudFormData) => ArticleService.updateArticle(Number(id), data as Partial<ArticleFormData>),
    delete: (id: string | number) => ArticleService.deleteArticle(Number(id))
  },

  // 表格列配置
  columns: [
    { type: 'selection' },
    { type: 'index', width: 60, label: '序号' },
    { prop: 'title', label: '文章标题', minWidth: 200 },
    { prop: 'slug', label: '文章别名', width: 120 },
    { prop: 'author_name', label: '作者', width: 100 },
    {
      prop: 'is_published',
      label: '发布状态',
      width: 100,
      formatter: (row: any) => {
        const config = getPublishedConfig(row.is_published)
        return h(ElTag, { type: config.type as any, size: 'small' }, () => config.text)
      }
    },
    {
      prop: 'is_featured',
      label: '推荐',
      width: 80,
      formatter: (row: any) => {
        const config = getFeaturedConfig(row.is_featured)
        return h(ElTag, { type: config.type as any, size: 'small' }, () => config.text)
      }
    },
    {
      prop: 'status',
      label: '状态',
      width: 80,
      formatter: (row: any) => {
        const config = getStatusConfig(row.status)
        return h(ElTag, { type: config.type, size: 'small' }, () => config.text)
      }
    },
    {
      prop: 'view_count',
      label: '浏览量',
      width: 80,
      formatter: (row: any) => row.view_count || 0
    },
    { prop: 'sort', label: '排序', width: 80 },
    { prop: 'created_at', label: '创建时间', width: 160, sortable: true }
  ],

  // 搜索配置（如果不需要搜索，设置 enabled: false）
  search: {
    enabled: false
  },

  // 操作配置
  actions: {
    enabled: true,
    width: 220,
    create: {
      enabled: true,
      text: '新增文章',
      type: 'primary'
    },
    edit: {
      enabled: true
    },
    delete: {
      enabled: true,
      confirmText: '确定要删除这篇文章吗？'
    },
    custom: [
      {
        type: 'view',
        text: '发布管理',
        handler: (row) => handlePublishToggle(row)
      },
      {
        type: 'edit',
        text: '状态切换',
        handler: (row) => handleStatusToggle(row)
      }
    ]
  },

  // 弹窗配置
  dialog: {
    enabled: true,
    width: '900px',
    titles: {
      create: '新增文章',
      edit: '编辑文章'
    },
    formConfig: [
      {
        prop: 'title',
        label: '文章标题',
        type: 'input',
        required: true,
        span: 12,
        config: { placeholder: '请输入文章标题' }
      },
      {
        prop: 'slug',
        label: '文章别名',
        type: 'input',
        required: true,
        span: 12,
        config: { placeholder: '请输入文章别名（英文）' }
      },
      {
        prop: 'author_name',
        label: '作者姓名',
        type: 'input',
        span: 12,
        config: { placeholder: '请输入作者姓名' }
      },
      {
        prop: 'sort',
        label: '排序',
        type: 'number',
        span: 12,
        config: { min: 0, step: 1, placeholder: '数字越大越靠前' }
      },
      {
        prop: 'is_published',
        label: '发布状态',
        type: 'radio',
        required: true,
        span: 12,
        options: [
          { label: '草稿', value: 0 },
          { label: '已发布', value: 1 }
        ]
      },
      {
        prop: 'is_featured',
        label: '推荐文章',
        type: 'switch',
        span: 12
      },
      {
        prop: 'status',
        label: '状态',
        type: 'radio',
        required: true,
        span: 12,
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 2 }
        ]
      },
      {
        prop: 'summary',
        label: '文章摘要',
        type: 'textarea',
        span: 24,
        config: { rows: 3, placeholder: '请输入文章摘要' }
      },
      {
        prop: 'content',
        label: '文章内容',
        type: 'textarea',
        span: 24,
        config: { rows: 8, placeholder: '请输入文章内容' }
      }
    ]
  },

  // 表格配置
  table: {
    rowKey: 'id',
    stripe: true,
    border: false
  }
}

// 事件处理
const handleArticleCreate = (data: CrudFormData) => {
  console.log('创建文章:', data)
}

const handleArticleUpdate = (id: string | number, data: CrudFormData) => {
  console.log('更新文章:', id, data)
}

const handleArticleDelete = (id: string | number) => {
  console.log('删除文章:', id)
}

const handleSelectionChange = (rows: any[]) => {
  selectedRows.value = rows
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 篇文章吗？`,
      '批量删除',
      { type: 'warning' }
    )

    const ids = selectedRows.value.map(row => row.id)
    await ArticleService.batchDeleteArticles(ids)
    ElMessage.success('批量删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 发布管理
const handlePublishToggle = async (article: any) => {
  try {
    const newStatus = article.is_published === 1 ? 0 : 1
    const action = newStatus === 1 ? '发布' : '取消发布'
    
    await ElMessageBox.confirm(
      `确定要${action}文章 "${article.title}" 吗？`,
      '发布管理',
      { type: 'warning' }
    )

    // TODO: 实现发布状态更新API
    // await ArticleService.updatePublishStatus(article.id, newStatus)
    ElMessage.success(`${action}成功`)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 状态切换
const handleStatusToggle = async (article: any) => {
  try {
    const newStatus = article.status === 1 ? 2 : 1
    const action = newStatus === 1 ? '启用' : '禁用'
    
    await ElMessageBox.confirm(
      `确定要${action}文章 "${article.title}" 吗？`,
      '状态切换',
      { type: 'warning' }
    )

    await ArticleService.updateArticleStatus(article.id, newStatus)
    ElMessage.success(`${action}成功`)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('状态切换失败')
    }
  }
}
</script>

<style scoped>
.article-management {
  padding: 20px;
  text-align: center;
}

/* 文章管理页面样式 */
</style>
