<?php
declare(strict_types=1);

namespace app\common\service;

use app\common\model\Upload;
use think\facade\Filesystem;

/**
 * 上传服务类
 */
class UploadService
{
    /**
     * 上传图片
     */
    public static function uploadImage($file, $user = null)
    {
        // 生成文件名
        $fileName = date('Ymd') . '/' . md5(uniqid()) . '.' . $file->extension();
        $savePath = 'images/' . $fileName;
        
        // 保存文件
        $path = Filesystem::disk('public')->putFileAs('images/' . date('Ymd'), $file, md5(uniqid()) . '.' . $file->extension());
        
        if (!$path) {
            throw new \Exception('文件上传失败');
        }

        // 记录到数据库，统一使用正斜杠
        $normalizedPath = str_replace('\\', '/', $path);
        $upload = new Upload();
        $upload->save([
            'original_name' => $file->getOriginalName(),
            'file_name' => basename($normalizedPath),
            'file_path' => $normalizedPath,
            'file_size' => $file->getSize(),
            'file_type' => $file->extension(),
            'mime_type' => $file->getMime(),
            'upload_user_id' => $user['id'] ?? 0,
            'upload_user_type' => 'admin'
        ]);

        return [
            'id' => $upload->id,
            'url' => '/storage/' . $normalizedPath,
            'filename' => $file->getOriginalName(),
            'size' => $file->getSize()
        ];
    }

    /**
     * 上传文件
     */
    public static function uploadFile($file, $user = null)
    {
        // 生成文件名
        $fileName = date('Ymd') . '/' . md5(uniqid()) . '.' . $file->extension();
        $savePath = 'files/' . $fileName;
        
        // 保存文件
        $path = Filesystem::disk('public')->putFileAs('files/' . date('Ymd'), $file, md5(uniqid()) . '.' . $file->extension());
        
        if (!$path) {
            throw new \Exception('文件上传失败');
        }

        // 记录到数据库，统一使用正斜杠
        $normalizedPath = str_replace('\\', '/', $path);
        $upload = new Upload();
        $upload->save([
            'original_name' => $file->getOriginalName(),
            'file_name' => basename($normalizedPath),
            'file_path' => $normalizedPath,
            'file_size' => $file->getSize(),
            'file_type' => $file->extension(),
            'mime_type' => $file->getMime(),
            'upload_user_id' => $user['id'] ?? 0,
            'upload_user_type' => 'admin'
        ]);

        return [
            'id' => $upload->id,
            'url' => '/storage/' . $normalizedPath,
            'filename' => $file->getOriginalName(),
            'size' => $file->getSize()
        ];
    }

    /**
     * 获取文件列表
     */
    public static function getFileList($current = 1, $size = 20, $type = '')
    {
        $where = [];
        if ($type) {
            $where[] = ['file_type', '=', $type];
        }

        $list = Upload::where($where)
            ->page($current, $size)
            ->order('created_at desc')
            ->select();

        $total = Upload::where($where)->count();

        $records = $list->map(function($item) {
            return [
                'id' => $item->id,
                'original_name' => $item->original_name,
                'file_name' => $item->file_name,
                'file_path' => $item->file_path,
                'file_size' => $item->file_size,
                'file_type' => $item->file_type,
                'url' => '/storage/' . $item->file_path,
                'created_at' => $item->created_at
            ];
        })->toArray();

        return [
            'list' => $records,
            'total' => $total
        ];
    }

    /**
     * 删除文件
     */
    public static function deleteFile($id)
    {
        $upload = Upload::find($id);
        if (!$upload) {
            throw new \Exception('文件不存在');
        }

        // 删除物理文件
        $filePath = public_path('storage/' . $upload->file_path);
        if (file_exists($filePath)) {
            unlink($filePath);
        }

        // 删除数据库记录
        $upload->delete();
        
        return true;
    }
}
