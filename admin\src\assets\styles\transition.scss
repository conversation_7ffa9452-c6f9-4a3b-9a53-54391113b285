@use 'sass:map';

// === 变量区域 ===
$transition: (
  duration: 0.3s,
  // 动画持续时间
  distance: 20px,
  // 滑动动画的移动距离
  easing: cubic-bezier(0.4, 0, 0.2, 1),
  // 默认缓动函数
  fade-easing: ease // 淡入淡出专用的缓动函数
);

// 抽取配置值函数，提高可复用性
@function transition-config($key) {
  @return map.get($transition, $key);
}

// 变量简写
$duration: transition-config('duration');
$distance: transition-config('distance');
$easing: transition-config('easing');
$fade-easing: transition-config('fade-easing');

// === 动画类 ===

// 淡入淡出动画
.fade {
  &-enter-active,
  &-leave-active {
    transition: opacity $duration $fade-easing;
    will-change: opacity;
  }

  &-enter-from,
  &-leave-to {
    opacity: 0;
  }

  &-enter-to,
  &-leave-from {
    opacity: 1;
  }
}

// 滑动动画通用样式
@mixin slide-transition($direction) {
  $distance-x: 0;
  $distance-y: 0;

  @if $direction == 'left' {
    $distance-x: -$distance;
  } @else if $direction == 'right' {
    $distance-x: $distance;
  } @else if $direction == 'top' {
    $distance-y: -$distance;
  } @else if $direction == 'bottom' {
    $distance-y: $distance;
  }

  &-enter-active,
  &-leave-active {
    transition:
      opacity $duration $easing,
      transform $duration $easing;
    will-change: opacity, transform;
  }

  &-enter-from {
    opacity: 0;
    transform: translate3d($distance-x, $distance-y, 0);
  }

  &-enter-to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }

  &-leave-to {
    opacity: 0;
    transform: translate3d(-$distance-x, -$distance-y, 0);
  }
}

// 滑动动画方向类
.slide-left {
  @include slide-transition('left');
}
.slide-right {
  @include slide-transition('right');
}
.slide-top {
  @include slide-transition('top');
}
.slide-bottom {
  @include slide-transition('bottom');
}
