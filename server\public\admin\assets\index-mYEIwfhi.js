var e=Object.defineProperty,t=Object.defineProperties,r=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,a=Object.prototype.propertyIsEnumerable,n=(t,r,o)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[r]=o;import{_ as l}from"./ArtResultPage-DLAOYpiN.js";import"./index-Bcx6y5fH.js";/* empty css                  */import{m as p,C as i,D as c,G as u,a5 as f,a6 as m,W as b,R as y,Z as _}from"./vendor-8T3zXQLl.js";import"./_plugin-vue_export-helper-BCo6x5W8.js";const j=p((d=((e,t)=>{for(var r in t||(t={}))s.call(t,r)&&n(e,r,t[r]);if(o)for(var r of o(t))a.call(t,r)&&n(e,r,t[r]);return e})({},{name:"ResultFail"}),t(d,r({__name:"index",setup:e=>(e,t)=>{const r=m,o=l,s=_("ripple");return c(),i(o,{type:"fail",title:"提交失败",message:"请核对并修改以下信息后，再重新提交。",iconCode:""},{content:u((()=>t[0]||(t[0]=[y("p",null,"您提交的内容有如下错误：",-1),y("p",null,[y("i",{class:"icon iconfont-sys"},""),b("您的账户已被冻结")],-1),y("p",null,[y("i",{class:"icon iconfont-sys"},""),b("您的账户还不具备申请资格")],-1)]))),buttons:u((()=>[f((c(),i(r,{type:"primary"},{default:u((()=>t[1]||(t[1]=[b("返回修改")]))),_:1,__:[1]})),[[s]]),f((c(),i(r,null,{default:u((()=>t[2]||(t[2]=[b("查看")]))),_:1,__:[2]})),[[s]])])),_:1})}}))));var d;export{j as default};
