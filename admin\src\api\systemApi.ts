import request from '@/utils/http'

// 系统配置相关接口类型定义
export interface SystemConfigItem {
  id: number
  config_key: string
  config_value: string
  config_desc: string
  config_type: 'string' | 'number' | 'boolean' | 'json'
  created_at: string
  updated_at: string
}

// 系统配置表单数据
export interface SystemConfigFormData {
  site_name?: string
  site_logo?: string
  site_description?: string
  site_keywords?: string
  site_favicon?: string
  site_copyright?: string
  site_icp?: string
  site_phone?: string
  site_email?: string
  site_address?: string
  upload_max_size?: number
  upload_allowed_ext?: string
  logo_size?: number
  [key: string]: any
}

// 系统信息
export interface SystemInfo {
  php_version: string
  thinkphp_version: string
  server_software: string
  mysql_version: string
  upload_max_filesize: string
  post_max_size: string
  memory_limit: string
  max_execution_time: string
}

// 系统配置服务类
export class SystemService {
  // 获取系统配置
  static getConfig() {
    return request.get<SystemConfigFormData>({
      url: '/system/config'
    })
  }

  // 设置系统配置
  static setConfig(data: SystemConfigFormData) {
    return request.post({
      url: '/system/config',
      data
    })
  }

  // 获取系统信息
  static getSystemInfo() {
    return request.get<SystemInfo>({
      url: '/system/info'
    })
  }

  // 获取配置项列表（管理用）
  static getConfigList() {
    return request.get<SystemConfigItem[]>({
      url: '/system/config-list'
    })
  }

  // 创建配置项
  static createConfigItem(data: Partial<SystemConfigItem>) {
    return request.post({
      url: '/system/config-item',
      data
    })
  }

  // 更新配置项
  static updateConfigItem(id: number, data: Partial<SystemConfigItem>) {
    return request.put({
      url: `/system/config-item/${id}`,
      data
    })
  }

  // 删除配置项
  static deleteConfigItem(id: number) {
    return request.del({
      url: `/system/config-item/${id}`
    })
  }
}

// 导出默认服务
export default SystemService
