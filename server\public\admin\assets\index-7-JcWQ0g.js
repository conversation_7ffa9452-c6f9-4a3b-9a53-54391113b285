var e=(e,t,r)=>new Promise(((l,a)=>{var o=e=>{try{s(r.next(e))}catch(t){a(t)}},p=e=>{try{s(r.throw(e))}catch(t){a(t)}},s=e=>e.done?l(e.value):Promise.resolve(e.value).then(o,p);s((r=r.apply(e,t)).next())}));import{C as t}from"./index-C8nK2eTm.js";import{E as r}from"./index-Bcx6y5fH.js";import{m as l,M as a,p as o,aQ as p,C as s,D as i}from"./vendor-8T3zXQLl.js";import"./index-DyBuW4xE.js";import"./index-D1l9fF2S.js";import"./index-Drb3fmHv.js";/* empty css                   *//* empty css                      *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-BPyeCy-r.js";/* empty css                *//* empty css               *//* empty css                  *//* empty css               */import"./el-form-item-l0sNRNKZ.js";/* empty css                       *//* empty css                 */import"./index-BgthDRX_.js";import"./index-7x-iptGQ.js";/* empty css                  *//* empty css                    */class n{static getCategoryList(t){return e(this,null,(function*(){return r.get({url:"/category/list",params:t})}))}static createCategory(t){return e(this,null,(function*(){return r.post({url:"/category/create",data:t})}))}static updateCategory(t,l){return e(this,null,(function*(){return r.put({url:`/category/update/${t}`,data:l})}))}static deleteCategory(t){return e(this,null,(function*(){return r.del({url:`/category/delete/${t}`})}))}}const u=l({__name:"index",setup(e){const r=a({api:{list:n.getCategoryList,create:e=>n.createCategory(e),update:(e,t)=>n.updateCategory(Number(e),t),delete:e=>n.deleteCategory(Number(e))},columns:[{type:"selection"},{type:"index",label:"序号"},{prop:"name",label:"分类名称"},{prop:"article_count",label:"文章数量"},{prop:"status",label:"状态",formatter:e=>{const t={0:{type:"danger",text:"禁用"},1:{type:"success",text:"启用"}}[e.status]||{type:"info",text:"未知"};return o(p,{type:t.type,size:"small"},(()=>t.text))}},{prop:"sort",label:"排序"},{prop:"created_at",label:"创建时间"}],search:{enabled:!0,items:[{prop:"name",label:"分类名称",type:"input",placeholder:"请输入分类名称"},{prop:"status",label:"状态",type:"select",options:[{label:"全部",value:""},{label:"禁用",value:0},{label:"启用",value:1}]}]},dialog:{enabled:!0,width:"600px",titles:{create:"新增分类",edit:"编辑分类"},formConfig:[{prop:"name",label:"分类名称",type:"input",required:!0,placeholder:"请输入分类名称",span:24,rules:[{required:!0,message:"请输入分类名称",trigger:"blur"},{max:50,message:"分类名称不能超过50个字符",trigger:"blur"}]},{prop:"parent_id",label:"上级分类",type:"select",placeholder:"请选择上级分类",span:12,options:[{label:"顶级分类",value:0},{label:"技术文章",value:1},{label:"产品介绍",value:2}]},{prop:"status",label:"状态",type:"radio",span:12,options:[{label:"禁用",value:0},{label:"启用",value:1}]},{prop:"sort",label:"排序",type:"number",span:24,placeholder:"请输入排序值，数字越大越靠前"}]},actions:{enabled:!0,create:{enabled:!0,text:"新增分类",type:"primary"},edit:{enabled:!0},delete:{enabled:!0,confirmText:"确定要删除这个分类吗？删除后该分类下的文章将变为未分类状态。"}},table:{rowKey:"id",stripe:!0,border:!1,size:"default"}}),l=e=>{},u=(e,t)=>{},m=e=>{};return(e,a)=>(i(),s(t,{config:r,onCreate:l,onUpdate:u,onDelete:m},null,8,["config"]))}});export{u as default};
