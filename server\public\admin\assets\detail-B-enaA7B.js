var a=(a,e,t)=>new Promise(((s,l)=>{var n=a=>{try{i(t.next(a))}catch(e){l(e)}},r=a=>{try{i(t.throw(a))}catch(e){l(e)}},i=a=>a.done?s(a.value):Promise.resolve(a.value).then(n,r);i((t=t.apply(a,e)).next())}));import"./index-1JOyfOxu.js";/* empty css                 *//* empty css                *//* empty css                  */import{m as e,r as t,at as s,o as l,E as n,Y as r,P as i,D as o,x as u,G as c,R as d,aR as p,aS as v,W as _,a6 as m,aT as h,aU as f,X as y,aV as j}from"./vendor-8T3zXQLl.js";import{_ as x}from"./_plugin-vue_export-helper-BCo6x5W8.js";const k={class:"article-detail-page"},w={class:"header-content"},g={class:"title-section"},T={class:"action-section"},H={key:0,class:"loading"},P={key:1,class:"article-content"},b={class:"meta-info"},C=["innerHTML"],D={key:2,class:"empty"},I=x(e({__name:"detail",setup(e){const x=s(),I=r(),L=t(!0),M=t(null),R=x.params.id;l((()=>a(this,null,(function*(){R?yield E():(n.error("缺少文章ID参数"),G())}))));const E=()=>a(this,null,(function*(){try{L.value=!0,setTimeout((()=>{M.value={id:R,title:"测试文章标题",author:"管理员",created_at:"2024-01-01 12:00:00",status:1,content:"<p>这是文章内容...</p>"},L.value=!1}),1e3)}catch(a){n.error("加载文章详情失败"),L.value=!1}})),G=()=>{I.push("/system/article/list")},J=()=>{I.push(`/system/article/list/edit/${R}`)};return(a,e)=>{const t=v,s=p,l=m,n=h,r=f,x=j;return o(),i("div",k,[u(n,{class:"page-header",shadow:"never"},{default:c((()=>[d("div",w,[d("div",g,[e[3]||(e[3]=d("h2",null,"文章详情",-1)),u(s,{separator:"/"},{default:c((()=>[u(t,null,{default:c((()=>e[0]||(e[0]=[_("系统管理")]))),_:1,__:[0]}),u(t,null,{default:c((()=>e[1]||(e[1]=[_("文章管理")]))),_:1,__:[1]}),u(t,null,{default:c((()=>e[2]||(e[2]=[_("文章详情")]))),_:1,__:[2]})])),_:1})]),d("div",T,[u(l,{onClick:G},{default:c((()=>e[4]||(e[4]=[_("返回")]))),_:1,__:[4]}),u(l,{type:"primary",onClick:J},{default:c((()=>e[5]||(e[5]=[_("编辑")]))),_:1,__:[5]})])])])),_:1}),u(n,{class:"content",shadow:"never"},{default:c((()=>[L.value?(o(),i("div",H,[u(r,{rows:5,animated:""})])):M.value?(o(),i("div",P,[d("h1",null,y(M.value.title),1),d("div",b,[d("span",null,"作者："+y(M.value.author),1),d("span",null,"发布时间："+y(M.value.created_at),1),d("span",null,"状态："+y(1===M.value.status?"已发布":"草稿"),1)]),d("div",{class:"content-body",innerHTML:M.value.content},null,8,C)])):(o(),i("div",D,[u(x,{description:"文章不存在"})]))])),_:1})])}}}),[["__scopeId","data-v-6c6d7f1d"]]);export{I as default};
