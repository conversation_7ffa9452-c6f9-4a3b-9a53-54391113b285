<?php
declare(strict_types=1);

namespace app\adminapi\controller;

use app\common\controller\BaseController;
use app\common\model\Admin as AdminModel;
use app\common\model\Role;
use app\adminapi\validate\AdminValidate;
use think\facade\Db;

/**
 * 管理员管理控制器
 */
class Admin extends BaseController
{
    /**
     * 获取管理员列表
     */
    public function list()
    {
        $params = $this->request->get();
        $current = (int)($params['current'] ?? 1);
        $size = (int)($params['size'] ?? 20);
        $username = $params['username'] ?? '';
        $nickname = $params['nickname'] ?? '';
        $status = $params['status'] ?? '';

        $where = [];
        if ($username) {
            $where[] = ['username', 'like', "%{$username}%"];
        }
        if ($nickname) {
            $where[] = ['nickname', 'like', "%{$nickname}%"];
        }
        if ($status !== '') {
            $where[] = ['status', '=', $status];
        }

        $list = AdminModel::with(['role'])
            ->where($where)
            ->page($current, $size)
            ->order('created_at desc')
            ->select();

        $total = AdminModel::where($where)->count();

        // 数据转换
        $records = $list->map(function($item) {
            return [
                'id' => $item->id,
                'username' => $item->username,
                'nickname' => $item->nickname ?? '',
                'email' => $item->email ?? '',
                'phone' => $item->phone ?? '',
                'avatar' => $item->avatar ?? '',
                'status' => $item->status,
                'statusText' => $item->status_text,
                'roles' => $item->role->map(function($role) {
                    return [
                        'id' => $role->id,
                        'name' => $role->role_name,
                        'code' => $role->role_code
                    ];
                })->toArray(),
                'lastLoginTime' => $item->last_login_time,
                'lastLoginIp' => $item->last_login_ip,
                'createdAt' => $item->created_at,
                'updatedAt' => $item->updated_at
            ];
        })->toArray();

        return $this->success([
            'records' => $records,
            'total' => $total,
            'current' => $current,
            'size' => $size
        ]);
    }

    /**
     * 创建管理员
     */
    public function create()
    {
        $data = $this->request->post();
        
        $validate = new AdminValidate();
        if (!$validate->scene('create')->check($data)) {
            return $this->error($validate->getError());
        }

        try {
            Db::startTrans();
            
            // 检查用户名是否已存在
            $exists = AdminModel::where('username', $data['username'])->find();
            if ($exists) {
                return $this->error('用户名已存在');
            }

            // 创建管理员
            $admin = new AdminModel();
            $admin->save([
                'username' => $data['username'],
                'password' => $data['password'],
                'nickname' => $data['nickname'] ?? '',
                'email' => $data['email'] ?? '',
                'phone' => $data['phone'] ?? '',
                'avatar' => $data['avatar'] ?? '',
                'status' => $data['status'] ?? 1
            ]);

            // 分配角色
            if (!empty($data['roleIds']) && is_array($data['roleIds'])) {
                $admin->role()->attach($data['roleIds']);
            }

            Db::commit();
            return $this->success($admin, '创建成功');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error($e->getMessage());
        }
    }

    /**
     * 更新管理员
     */
    public function update()
    {
        $id = $this->request->param('id');
        $data = $this->request->post();
        
        $validate = new AdminValidate();
        if (!$validate->scene('update')->check($data)) {
            return $this->error($validate->getError());
        }

        try {
            Db::startTrans();
            
            $admin = AdminModel::find($id);
            if (!$admin) {
                return $this->error('管理员不存在');
            }

            // 检查用户名是否已被其他用户使用
            if (isset($data['username'])) {
                $exists = AdminModel::where('username', $data['username'])
                    ->where('id', '<>', $id)
                    ->find();
                if ($exists) {
                    return $this->error('用户名已存在');
                }
            }

            // 更新基本信息
            $updateData = [];
            $allowFields = ['username', 'nickname', 'email', 'phone', 'avatar', 'status'];
            foreach ($allowFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }

            // 如果有新密码，则更新密码
            if (!empty($data['password'])) {
                $updateData['password'] = $data['password'];
            }

            $admin->save($updateData);

            // 更新角色关联
            if (isset($data['roleIds']) && is_array($data['roleIds'])) {
                $admin->role()->detach();
                if (!empty($data['roleIds'])) {
                    $admin->role()->attach($data['roleIds']);
                }
            }

            Db::commit();
            return $this->success($admin, '更新成功');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除管理员
     */
    public function delete()
    {
        $id = $this->request->param('id');
        
        try {
            $admin = AdminModel::find($id);
            if (!$admin) {
                return $this->error('管理员不存在');
            }

            // 不能删除自己
            $currentUser = $this->request->user;
            if ($currentUser['id'] == $id) {
                return $this->error('不能删除自己');
            }

            Db::startTrans();
            
            // 删除角色关联
            $admin->role()->detach();
            
            // 删除管理员
            $admin->delete();
            
            Db::commit();
            return $this->success(null, '删除成功');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取角色列表（用于分配角色）
     */
    public function getRoles()
    {
        try {
            $roles = Role::where('status', 1)
                ->order('id asc')
                ->select();
            
            $result = $roles->map(function($role) {
                return [
                    'id' => $role->id,
                    'name' => $role->role_name,
                    'code' => $role->role_code,
                    'description' => $role->description
                ];
            })->toArray();
            
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 重置密码
     */
    public function resetPassword()
    {
        $id = $this->request->param('id');
        $data = $this->request->post();
        
        if (empty($data['password'])) {
            return $this->error('新密码不能为空');
        }

        try {
            $admin = AdminModel::find($id);
            if (!$admin) {
                return $this->error('管理员不存在');
            }

            $admin->save(['password' => $data['password']]);
            
            return $this->success(null, '密码重置成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 切换状态
     */
    public function toggleStatus()
    {
        $id = $this->request->param('id');
        
        try {
            $admin = AdminModel::find($id);
            if (!$admin) {
                return $this->error('管理员不存在');
            }

            // 不能禁用自己
            $currentUser = $this->request->user;
            if ($currentUser['id'] == $id) {
                return $this->error('不能禁用自己');
            }

            $admin->save(['status' => $admin->status == 1 ? 2 : 1]);
            
            return $this->success(null, '状态切换成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
