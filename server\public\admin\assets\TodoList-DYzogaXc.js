import"./index-DG9-1w7X.js";/* empty css                    */import{k as a,M as e,P as s,D as t,ai as l,R as d,F as c,$ as o,u as m,x as r,X as p,a4 as n}from"./vendor-84Inc-Pt.js";import{_ as u}from"./_plugin-vue_export-helper-BCo6x5W8.js";const i={class:"card art-custom-card"},v={class:"list"},f={class:"title"},x={class:"date subtitle"},_=u(a({__name:"TodoList",setup(a){const u=e([{username:"查看今天工作内容",date:"上午 09:30",complate:!0},{username:"回复邮件",date:"上午 10:30",complate:!0},{username:"工作汇报整理",date:"上午 11:00",complate:!0},{username:"产品需求会议",date:"下午 02:00",complate:!1},{username:"整理会议内容",date:"下午 03:30",complate:!1},{username:"明天工作计划",date:"下午 06:30",complate:!1}]);return(a,e)=>{const _=n;return t(),s("div",i,[e[0]||(e[0]=l('<div class="card-header" data-v-ac7098f6><div class="title" data-v-ac7098f6><h4 class="box-title" data-v-ac7098f6>代办事项</h4><p class="subtitle" data-v-ac7098f6>待处理<span class="text-danger" data-v-ac7098f6>3</span></p></div></div>',1)),d("div",v,[(t(!0),s(c,null,o(m(u),((a,e)=>(t(),s("div",{key:e},[d("p",f,p(a.username),1),d("p",x,p(a.date),1),r(_,{modelValue:a.complate,"onUpdate:modelValue":e=>a.complate=e},null,8,["modelValue","onUpdate:modelValue"])])))),128))])])}}}),[["__scopeId","data-v-ac7098f6"]]);export{_ as default};
