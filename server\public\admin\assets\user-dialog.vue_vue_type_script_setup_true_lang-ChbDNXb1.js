import"./index-DG9-1w7X.js";/* empty css                  *//* empty css                  *//* empty css                *//* empty css                  *//* empty css                     */import"./el-form-item-l0sNRNKZ.js";/* empty css                 */import{k as e,r as l,c as a,M as r,w as s,n as o,C as t,D as u,G as d,x as i,a8 as m,u as n,a2 as p,a3 as g,ar as v,as as b,P as c,F as _,$ as f,R as V,a6 as y,W as h,i as j,ak as R,E as C}from"./vendor-84Inc-Pt.js";const x={class:"dialog-footer"},D=e({__name:"user-dialog",props:{visible:{type:Boolean},type:{},userData:{}},emits:["update:visible","submit"],setup(e,{emit:D}){const U=e,k=D,w=l([{roleName:"普通用户",roleCode:"R_USER"},{roleName:"管理员",roleCode:"R_ADMIN"},{roleName:"超级管理员",roleCode:"R_SUPER"}]),N=a({get:()=>U.visible,set:e=>k("update:visible",e)}),P=a((()=>U.type)),q=l(),A=r({username:"",phone:"",gender:"男",role:[]}),E={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:2,max:20,message:"长度在 2 到 20 个字符",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号格式",trigger:"blur"}],gender:[{required:!0,message:"请选择性别",trigger:"blur"}],role:[{required:!0,message:"请选择角色",trigger:"blur"}]};s((()=>[U.visible,U.type,U.userData]),(([e])=>{e&&((()=>{const e="edit"===U.type&&U.userData,l=U.userData;Object.assign(A,{username:e&&l.userName||"",phone:e&&l.userPhone||"",gender:e&&l.userGender||"男",role:e&&Array.isArray(l.userRoles)?l.userRoles:[]})})(),o((()=>{var e;null==(e=q.value)||e.clearValidate()})))}),{immediate:!0});const G=()=>{return e=this,l=null,a=function*(){q.value&&(yield q.value.validate((e=>{e&&(C.success("add"===P.value?"添加成功":"更新成功"),N.value=!1,k("submit"))})))},new Promise(((r,s)=>{var o=e=>{try{u(a.next(e))}catch(l){s(l)}},t=e=>{try{u(a.throw(e))}catch(l){s(l)}},u=e=>e.done?r(e.value):Promise.resolve(e.value).then(o,t);u((a=a.apply(e,l)).next())}));var e,l,a};return(e,l)=>{const a=g,r=p,s=b,o=v,C=m,D=y,U=R;return u(),t(U,{modelValue:n(N),"onUpdate:modelValue":l[5]||(l[5]=e=>j(N)?N.value=e:null),title:"add"===n(P)?"添加用户":"编辑用户",width:"30%","align-center":""},{footer:d((()=>[V("div",x,[i(D,{onClick:l[4]||(l[4]=e=>N.value=!1)},{default:d((()=>l[6]||(l[6]=[h("取消")]))),_:1,__:[6]}),i(D,{type:"primary",onClick:G},{default:d((()=>l[7]||(l[7]=[h("提交")]))),_:1,__:[7]})])])),default:d((()=>[i(C,{ref_key:"formRef",ref:q,model:n(A),rules:E,"label-width":"80px"},{default:d((()=>[i(r,{label:"用户名",prop:"username"},{default:d((()=>[i(a,{modelValue:n(A).username,"onUpdate:modelValue":l[0]||(l[0]=e=>n(A).username=e)},null,8,["modelValue"])])),_:1}),i(r,{label:"手机号",prop:"phone"},{default:d((()=>[i(a,{modelValue:n(A).phone,"onUpdate:modelValue":l[1]||(l[1]=e=>n(A).phone=e)},null,8,["modelValue"])])),_:1}),i(r,{label:"性别",prop:"gender"},{default:d((()=>[i(o,{modelValue:n(A).gender,"onUpdate:modelValue":l[2]||(l[2]=e=>n(A).gender=e)},{default:d((()=>[i(s,{label:"男",value:"男"}),i(s,{label:"女",value:"女"})])),_:1},8,["modelValue"])])),_:1}),i(r,{label:"角色",prop:"role"},{default:d((()=>[i(o,{modelValue:n(A).role,"onUpdate:modelValue":l[3]||(l[3]=e=>n(A).role=e),multiple:""},{default:d((()=>[(u(!0),c(_,null,f(n(w),(e=>(u(),t(s,{key:e.roleCode,value:e.roleCode,label:e.roleName},null,8,["value","label"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])}}});export{D as _};
