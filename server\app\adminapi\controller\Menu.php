<?php
declare(strict_types=1);

namespace app\adminapi\controller;

use app\common\controller\BaseController;
use app\common\service\MenuService;

/**
 * 菜单管理控制器
 */
class Menu extends BaseController
{
    /**
     * 获取菜单列表 - 对应前端 menuService.getMenuList()
     * 后端控制模式专用接口
     */
    public function list()
    {
        try {
            // 菜单管理页面显示所有菜单，不进行权限过滤
            $result = MenuService::getAllMenusForManagement();

            // 返回符合前端期望的数据结构
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取菜单树形结构（管理用）
     */
    public function tree()
    {
        try {
            $result = MenuService::getMenuTree();
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 创建菜单
     */
    public function create()
    {
        $data = $this->request->post();
        
        try {
            $menu = MenuService::create($data);
            return $this->success($menu, '创建成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 更新菜单
     */
    public function update()
    {
        $id = $this->request->param('id');
        $data = $this->request->post();
        
        try {
            $menu = MenuService::update($id, $data);
            return $this->success($menu, '更新成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除菜单
     */
    public function delete()
    {
        $id = $this->request->param('id');
        
        try {
            MenuService::delete($id);
            return $this->success([], '删除成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
