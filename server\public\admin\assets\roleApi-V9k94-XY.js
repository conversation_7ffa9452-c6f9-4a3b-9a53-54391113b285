import{E as e}from"./index-tBKMHRR1.js";class t{static getRoleList(){return e.get({url:"/role/list"})}static createRole(t){return e.post({url:"/role/create",data:t})}static updateRole(t,r){return e.put({url:`/role/update/${t}`,data:r})}static deleteRole(t){return e.del({url:`/role/delete/${t}`})}static setPermissions(t,r){return e.post({url:`/role/permissions/${t}`,data:r})}static getPermissions(t){return e.get({url:`/role/permissions/${t}`})}}export{t as R};
