# 开发指南

## 概述

本文档记录了在开发过程中遇到的关键问题和解决方案，基于文章管理功能的实际开发经验总结。

## 项目架构

### ThinkPHP多应用模式
```
server/app/
├── adminapi/     # 后台管理API应用
│   ├── controller/
│   ├── route/    # 应用级路由配置 ⭐
│   └── middleware/
├── api/          # 前台API应用
├── index/        # 前台页面应用
└── common/       # 公共模块
```

## 路由配置 ⭐ 重要

### 为什么使用应用级路由？
1. **模块化管理**：每个应用独立管理路由
2. **权限隔离**：不同应用有不同的中间件认证
3. **RESTful设计**：统一的API风格

### 添加新功能路由
在 `server/app/adminapi/route/app.php` 中添加：

```php
// 文章管理
Route::group('article', function () {
    Route::get('list', 'Article/list');
    Route::post('create', 'Article/create');
    Route::put('update/:id', 'Article/update');
    Route::delete('delete/:id', 'Article/delete');
    Route::post('batch-delete', 'Article/batchDelete');
})->middleware(['adminapi_auth']);
```

**注意**：如果不配置路由，会出现"参数获取不到"的问题！

## 模型配置

### 表名配置（关键）
```php
class Article extends Model
{
    // 方式1：直接指定完整表名（推荐，与Role模型一致）
    protected $table = 'fs_articles';
    
    // 方式2：让ThinkPHP自动添加前缀
    protected $name = 'articles';
    
    // 主键
    protected $pk = 'id';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
}
```

### 软删除问题
- **问题**：软删除会导致查询逻辑复杂，容易出现"记录不存在"错误
- **建议**：新功能直接使用物理删除，避免复杂性
- **如果必须使用软删除**：确保前后端逻辑一致

## 前端组件配置

### CrudListPage组件配置模板
```typescript
const articleListConfig: CrudListPageConfig<ArticleInfo> = {
  // API配置
  api: {
    list: ArticleService.getArticleList,
    create: (data: CrudFormData) => ArticleService.createArticle(data as ArticleFormData),
    update: (id: string | number, data: CrudFormData) => ArticleService.updateArticle(Number(id), data as Partial<ArticleFormData>),
    delete: (id: string | number) => ArticleService.deleteArticle(Number(id))
  },
  
  // 表格列配置
  columns: [
    { type: 'selection' },
    { type: 'index', width: 60, label: '序号' },
    { prop: 'title', label: '文章标题', minWidth: 200 },
    // ...其他列配置
  ],
  
  // 操作按钮配置
  actions: {
    create: { enabled: true },
    edit: { enabled: true },
    delete: { 
      enabled: true,
      confirmText: '确定要删除这篇文章吗？'
    }
  }
}
```

## 常见问题及解决方案

### 1. 删除功能报"文章不存在"
**原因**：路由配置缺失，导致参数获取不到
**解决**：在 `server/app/adminapi/route/app.php` 中添加路由配置

### 2. 前端传递ID正确，后端收到空值
**原因**：ThinkPHP无法解析 `/article/delete/6` 这种URL格式
**解决**：配置路由 `Route::delete('delete/:id', 'Article/delete')`

### 3. 模型查询不到数据
**原因**：表名配置错误或软删除影响
**解决**：
- 检查 `protected $table` 配置
- 如果使用软删除，用 `withTrashed()` 查询

### 4. 删除成功但数据还在
**可能原因**：
- 前端缓存问题（刷新页面确认）
- 数据库事务未提交
- 软删除逻辑问题

## 开发流程

### 添加新功能的标准步骤
1. **创建控制器**：`server/app/adminapi/controller/NewFeature.php`
2. **配置路由**：在 `server/app/adminapi/route/app.php` 中添加路由组
3. **创建模型**：`server/app/common/model/NewFeature.php`
4. **前端API**：`admin/src/api/newFeatureApi.ts`
5. **前端页面**：`admin/src/views/system/new-feature/index.vue`
6. **测试功能**：确保增删改查都正常工作

### 调试技巧
1. **后端调试**：在控制器中添加 `return $this->success($debugInfo, '调试信息');`
2. **前端调试**：在组件中添加 `console.log()` 查看数据传递
3. **路由调试**：检查浏览器网络面板，确认请求URL正确

## 最佳实践

1. **路由配置**：新功能必须配置路由，不要依赖自动路由
2. **模型配置**：使用 `protected $table` 直接指定表名
3. **删除功能**：优先使用物理删除，避免软删除复杂性
4. **错误处理**：统一使用 `try-catch` 处理异常
5. **代码复用**：参考现有功能（如角色管理）的实现方式

## 代码模板

### 1. 控制器模板
```php
<?php
declare(strict_types=1);

namespace app\adminapi\controller;

use app\common\controller\BaseController;
use app\adminapi\validate\ArticleValidate;
use app\common\service\ArticleService;
use app\common\model\Article as ArticleModel;
use think\response\Json;

class Article extends BaseController
{
    /**
     * 列表
     */
    public function list(): Json
    {
        $params = $this->request->param();
        $result = ArticleService::getList($params);
        return $this->success($result);
    }

    /**
     * 创建
     */
    public function create(): Json
    {
        $data = $this->request->param();

        try {
            validate(ArticleValidate::class)->scene('create')->check($data);
            $result = ArticleService::create($data);
            return $this->success($result, '创建成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 更新
     */
    public function update(): Json
    {
        $id = $this->request->param('id');
        $data = $this->request->param();

        try {
            validate(ArticleValidate::class)->scene('update')->check($data);
            $result = ArticleService::update($id, $data);
            return $this->success($result, '更新成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除
     */
    public function delete(): Json
    {
        $id = $this->request->param('id');

        try {
            $article = ArticleModel::find($id);
            if (!$article) {
                return $this->error('文章不存在');
            }

            $article->delete();
            return $this->success([], '删除成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
```

### 2. 模型模板
```php
<?php
declare(strict_types=1);

namespace app\common\model;

use think\Model;

/**
 * 文章模型
 */
class Article extends Model
{
    // 表名（重要：直接指定完整表名）
    protected $table = 'fs_articles';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 字段类型转换
    protected $type = [
        'is_published' => 'integer',
        'is_featured' => 'integer',
        'status' => 'integer',
        'sort' => 'integer',
    ];

    // 获取器 - 发布状态文本
    public function getPublishedTextAttr($value, $data)
    {
        $status = [0 => '未发布', 1 => '已发布'];
        return $status[$data['is_published']] ?? '未知';
    }

    // 获取器 - 推荐状态文本
    public function getFeaturedTextAttr($value, $data)
    {
        $status = [0 => '否', 1 => '是'];
        return $status[$data['is_featured']] ?? '否';
    }

    // 获取器 - 状态文本
    public function getStatusTextAttr($value, $data)
    {
        $status = [0 => '禁用', 1 => '启用'];
        return $status[$data['status']] ?? '未知';
    }
}
```

### 3. 服务层模板
```php
<?php
declare(strict_types=1);

namespace app\common\service;

use app\common\model\Article;

class ArticleService
{
    /**
     * 获取列表
     */
    public static function getList(array $params = []): array
    {
        $page = $params['current'] ?? 1;
        $limit = $params['size'] ?? 20;

        $query = Article::order('sort desc, id desc');

        // 搜索条件
        if (!empty($params['title'])) {
            $query->whereLike('title', '%' . $params['title'] . '%');
        }

        if (isset($params['is_published']) && $params['is_published'] !== '') {
            $query->where('is_published', $params['is_published']);
        }

        $result = $query->paginate([
            'list_rows' => $limit,
            'page' => $page,
        ]);

        return [
            'list' => $result->items(),
            'total' => $result->total(),
            'current' => $page,
            'size' => $limit,
        ];
    }

    /**
     * 创建
     */
    public static function create(array $data): Article
    {
        return Article::create($data);
    }

    /**
     * 更新
     */
    public static function update(int $id, array $data): bool
    {
        $article = Article::find($id);
        if (!$article) {
            throw new \Exception('文章不存在');
        }

        return $article->save($data);
    }
}
```

### 4. 验证器模板
```php
<?php
declare(strict_types=1);

namespace app\adminapi\validate;

use think\Validate;

class ArticleValidate extends Validate
{
    protected $rule = [
        'title' => 'require|max:200',
        'slug' => 'require|max:100',
        'content' => 'require',
        'is_published' => 'in:0,1',
        'is_featured' => 'in:0,1',
        'status' => 'in:0,1',
        'sort' => 'integer|egt:0',
    ];

    protected $message = [
        'title.require' => '文章标题不能为空',
        'title.max' => '文章标题不能超过200个字符',
        'slug.require' => '文章别名不能为空',
        'slug.max' => '文章别名不能超过100个字符',
        'content.require' => '文章内容不能为空',
        'is_published.in' => '发布状态值错误',
        'is_featured.in' => '推荐状态值错误',
        'status.in' => '状态值错误',
        'sort.integer' => '排序必须是整数',
        'sort.egt' => '排序不能小于0',
    ];

    protected $scene = [
        'create' => ['title', 'slug', 'content', 'is_published', 'is_featured', 'status', 'sort'],
        'update' => ['title', 'slug', 'content', 'is_published', 'is_featured', 'status', 'sort'],
    ];
}
```

### 5. 前端API模板
```typescript
/**
 * 文章管理API服务
 */
import request from '@/utils/http'

// 文章信息接口
export interface ArticleInfo {
  id: number
  title: string
  slug: string
  content?: string
  summary?: string
  is_published: number
  published_text: string
  is_featured: number
  featured_text: string
  status: number
  status_text: string
  sort: number
  created_at: string
  updated_at: string
}

// 文章表单数据接口
export interface ArticleFormData {
  title: string
  slug: string
  content: string
  summary?: string
  is_published: number
  is_featured: number
  status: number
  sort: number
}

export class ArticleService {
  /**
   * 获取文章列表
   */
  static async getArticleList(params: any): Promise<any> {
    return request.get({
      url: '/article/list',
      params
    })
  }

  /**
   * 创建文章
   */
  static async createArticle(data: ArticleFormData): Promise<ArticleInfo> {
    return request.post({
      url: '/article/create',
      data
    })
  }

  /**
   * 更新文章
   */
  static async updateArticle(id: number, data: Partial<ArticleFormData>): Promise<ArticleInfo> {
    return request.put({
      url: `/article/update/${id}`,
      data
    })
  }

  /**
   * 删除文章
   */
  static async deleteArticle(id: number): Promise<void> {
    return request.del({
      url: `/article/delete/${id}`
    })
  }
}
```

### 6. 前端视图模板
```vue
<template>
  <CrudListPage
    :config="articleListConfig"
    @create="handleArticleCreate"
    @update="handleArticleUpdate"
    @delete="handleArticleDelete"
  />
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import { ElTag } from 'element-plus'
import { h } from 'vue'
import CrudListPage from '@/components/common/crud-list-page/index.vue'
import { ArticleService, type ArticleInfo, type ArticleFormData } from '@/api/articleApi'
import type { CrudListPageConfig, CrudFormData } from '@/types/component'

// 配置对象
const articleListConfig: CrudListPageConfig<ArticleInfo> = reactive({
  // API配置
  api: {
    list: ArticleService.getArticleList,
    create: (data: CrudFormData) => ArticleService.createArticle(data as ArticleFormData),
    update: (id: string | number, data: CrudFormData) => ArticleService.updateArticle(Number(id), data as Partial<ArticleFormData>),
    delete: (id: string | number) => ArticleService.deleteArticle(Number(id))
  },

  // 表格列配置
  columns: [
    { type: 'selection' },
    { type: 'index', width: 60, label: '序号' },
    { prop: 'title', label: '文章标题', minWidth: 200 },
    { prop: 'slug', label: '文章别名', width: 120 },
    {
      prop: 'is_published',
      label: '发布状态',
      width: 100,
      formatter: (row: ArticleInfo) => {
        const type = row.is_published ? 'success' : 'info'
        return h(ElTag, { type, size: 'small' }, () => row.published_text)
      }
    },
    {
      prop: 'status',
      label: '状态',
      width: 80,
      formatter: (row: ArticleInfo) => {
        const type = row.status ? 'success' : 'danger'
        return h(ElTag, { type, size: 'small' }, () => row.status_text)
      }
    },
    { prop: 'sort', label: '排序', width: 80 },
    { prop: 'created_at', label: '创建时间', width: 160 }
  ],

  // 搜索配置
  search: {
    items: [
      {
        prop: 'title',
        label: '文章标题',
        type: 'input',
        placeholder: '请输入文章标题'
      },
      {
        prop: 'is_published',
        label: '发布状态',
        type: 'select',
        options: [
          { label: '全部', value: '' },
          { label: '未发布', value: 0 },
          { label: '已发布', value: 1 }
        ]
      }
    ]
  },

  // 表单配置
  form: {
    items: [
      {
        prop: 'title',
        label: '文章标题',
        type: 'input',
        required: true,
        placeholder: '请输入文章标题'
      },
      {
        prop: 'slug',
        label: '文章别名',
        type: 'input',
        required: true,
        placeholder: '请输入文章别名'
      },
      {
        prop: 'content',
        label: '文章内容',
        type: 'textarea',
        required: true,
        placeholder: '请输入文章内容'
      },
      {
        prop: 'is_published',
        label: '发布状态',
        type: 'radio',
        options: [
          { label: '未发布', value: 0 },
          { label: '已发布', value: 1 }
        ],
        defaultValue: 0
      },
      {
        prop: 'status',
        label: '状态',
        type: 'radio',
        options: [
          { label: '禁用', value: 0 },
          { label: '启用', value: 1 }
        ],
        defaultValue: 1
      },
      {
        prop: 'sort',
        label: '排序',
        type: 'number',
        defaultValue: 0,
        placeholder: '请输入排序值'
      }
    ]
  },

  // 操作按钮配置
  actions: {
    create: {
      enabled: true,
      text: '新增文章',
      type: 'primary'
    },
    edit: {
      enabled: true
    },
    delete: {
      enabled: true,
      confirmText: '确定要删除这篇文章吗？'
    }
  }
})

// 事件处理
const handleArticleCreate = (data: CrudFormData) => {
  console.log('创建文章:', data)
}

const handleArticleUpdate = (id: string | number, data: CrudFormData) => {
  console.log('更新文章:', id, data)
}

const handleArticleDelete = (id: string | number) => {
  console.log('删除文章:', id)
}
</script>
```

---

## 使用说明

1. **复制对应模板**：根据需要开发的功能，复制相应的代码模板
2. **替换关键词**：将 `Article/article` 替换为你的功能名称
3. **调整字段**：根据实际需求修改字段配置
4. **配置路由**：在 `server/app/adminapi/route/app.php` 中添加路由
5. **测试功能**：确保增删改查都正常工作

这样可以保证所有功能的代码风格和结构都保持一致！
