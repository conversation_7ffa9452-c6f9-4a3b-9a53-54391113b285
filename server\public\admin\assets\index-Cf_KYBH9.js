var e=Object.defineProperty,r=Object.defineProperties,a=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,o=(r,a,t)=>a in r?e(r,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[a]=t,i=(e,r,a)=>new Promise(((t,l)=>{var s=e=>{try{i(a.next(e))}catch(r){l(r)}},o=e=>{try{i(a.throw(e))}catch(r){l(r)}},i=e=>e.done?t(e.value):Promise.resolve(e.value).then(s,o);i((a=a.apply(e,r)).next())}));import{C as p}from"./index-DEvBMtTA.js";import n from"./user-add-dialog-D1liziKT.js";import d from"./user-edit-dialog-BJO9rfD9.js";import{U as u,A as c}from"./index-tBKMHRR1.js";import{m,r as b,P as g,D as y,x as v,G as f,C as h,V as j,W as x,X as w,u as P,a6 as _,E as U,aI as O,p as C,aQ as k}from"./vendor-8T3zXQLl.js";import{_ as q}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-CDOnAZ6j.js";import"./index-DWtE1Ov8.js";import"./index-ByC9HC3d.js";/* empty css                   *//* empty css                      *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./index-DYv3Gk6_.js";/* empty css                *//* empty css               *//* empty css                  *//* empty css               */import"./el-form-item-l0sNRNKZ.js";/* empty css                       *//* empty css                 */import"./index-CUnGyYEl.js";import"./index-CxM7mvjC.js";/* empty css                  *//* empty css                    */const S=m((D=((e,r)=>{for(var a in r||(r={}))l.call(r,a)&&o(e,a,r[a]);if(t)for(var a of t(r))s.call(r,a)&&o(e,a,r[a]);return e})({},{name:"User"}),r(D,a({__name:"index",setup(e){const r=b([]),a=b(!1),t=b(!1),l=b(null),s={api:{list:u.getUserList,create:u.createUser,update:u.updateUser,delete:u.deleteUser},columns:[{type:"selection"},{type:"index",label:"序号"},{prop:"avatar",label:"头像",width:80,formatter:e=>C("div",{class:"user-avatar-cell"},[e.avatar?C("img",{src:c(e.avatar),alt:"头像",class:"avatar-image",style:{width:"40px",height:"40px",objectFit:"cover",borderRadius:"50%",border:"1px solid #ddd"}}):C("div",{class:"no-avatar",style:{width:"40px",height:"40px",borderRadius:"50%",backgroundColor:"#f5f5f5",display:"flex",alignItems:"center",justifyContent:"center",color:"#999",fontSize:"12px",border:"1px solid #ddd"}},"无")])},{prop:"nickName",label:"昵称"},{prop:"userPhone",label:"电话"},{prop:"referrer",label:"推荐人"},{prop:"userStatus",label:"状态",formatter:e=>{const r={1:{type:"success",text:"正常"},2:{type:"danger",text:"禁用"},3:{type:"info",text:"注销"}}[e.userStatus]||{type:"info",text:"未知"};return C(k,{type:r.type},(()=>r.text))}},{prop:"createTime",label:"创建时间",sortable:!0}],search:{enabled:!0,defaultParams:{name:"",phone:"",status:"",level:"normal"},items:[{label:"用户名",prop:"name",type:"input",config:{clearable:!0}},{label:"手机号",prop:"phone",type:"input",config:{clearable:!0}},{label:"状态",prop:"status",type:"select",config:{clearable:!0},options:[{label:"正常",value:1},{label:"禁用",value:2},{label:"注销",value:3}]}]},actions:{enabled:!0,width:120,create:{enabled:!0,text:"新增用户",type:"primary"},edit:{enabled:!0},delete:{enabled:!0,confirmText:"确定要删除这个用户吗？"}},dialog:{enabled:!1,width:"600px",titles:{create:"新增用户",edit:"编辑用户"},formConfig:[{prop:"avatar",label:"头像",type:"upload",placeholder:"请选择头像（可选）",span:24,config:{pickerTitle:"选择用户头像",accept:"image/*",uploadText:"选择头像",multiple:!1}},{prop:"userPhone",label:"手机号",type:"input",required:!0,placeholder:"请输入手机号",span:24,rules:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}]},{prop:"nickName",label:"昵称",type:"input",required:!0,placeholder:"请输入昵称",span:24,rules:[{required:!0,message:"请输入昵称",trigger:"blur"},{min:2,max:20,message:"昵称长度必须在2-20位之间",trigger:"blur"}]},{prop:"password",label:"密码",type:"password",required:!1,placeholder:"不填则不修改",span:12,rules:[{min:6,max:32,message:"密码长度必须在6-32位之间",trigger:"blur"}]},{prop:"confirmPassword",label:"确认密码",type:"password",required:!1,placeholder:"不填则不修改",span:12,rules:[{validator:(e,r,a)=>{a()},trigger:"blur"}]},{prop:"payPassword",label:"支付密码",type:"password",required:!1,placeholder:"不填则不修改",span:12,rules:[{pattern:/^\d{6}$/,message:"支付密码必须是6位数字",trigger:"blur"}]},{prop:"confirmPayPassword",label:"确认支付密码",type:"password",required:!1,placeholder:"不填则不修改",span:12,rules:[{validator:(e,r,a)=>{a()},trigger:"blur"}]},{prop:"referrer",label:"推荐人",type:"input",placeholder:"请输入推荐人手机号或用户名（可选）",span:24}]},table:{rowKey:"id",stripe:!0,border:!1}},o=()=>{a.value=!0},m=(e,r)=>{l.value=r,t.value=!0},q=e=>i(this,null,(function*(){try{yield O.confirm("确定要删除这个用户吗？","删除确认",{type:"warning"}),yield u.deleteUser(Number(e)),U.success("删除成功")}catch(r){"cancel"!==r&&U.error("删除失败")}})),S=()=>{a.value=!1},D=()=>{t.value=!1},I=e=>{r.value=e},R=()=>i(this,null,(function*(){try{yield O.confirm(`确定要删除选中的 ${r.value.length} 个用户吗？`,"批量删除",{type:"warning"}),U.success("批量删除成功")}catch(e){"cancel"!==e&&U.error("批量删除失败")}})),T=()=>{U.success("导出功能开发中...")};return(e,r)=>(y(),g("div",null,[v(p,{config:s,onCreate:o,onUpdate:m,onDelete:q,onSelectionChange:I},{"header-actions":f((({selectedRows:e})=>[e.length>0?(y(),h(P(_),{key:0,type:"danger",onClick:R},{default:f((()=>[x(" 批量删除 ("+w(e.length)+") ",1)])),_:2},1024)):j("",!0),v(P(_),{onClick:T},{default:f((()=>r[2]||(r[2]=[x(" 导出用户 ")]))),_:1,__:[2]})])),_:1}),v(n,{visible:a.value,"onUpdate:visible":r[0]||(r[0]=e=>a.value=e),onSubmit:S},null,8,["visible"]),v(d,{visible:t.value,"onUpdate:visible":r[1]||(r[1]=e=>t.value=e),userData:l.value,onSubmit:D},null,8,["visible","userData"])]))}}))));var D;const I=q(S,[["__scopeId","data-v-24de10fb"]]);export{I as default};
