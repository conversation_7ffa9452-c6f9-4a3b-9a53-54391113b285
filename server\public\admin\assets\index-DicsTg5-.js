var e=Object.defineProperty,l=Object.defineProperties,a=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,u=(l,a,t)=>a in l?e(l,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[a]=t,i=(e,l)=>{for(var a in l||(l={}))n.call(l,a)&&u(e,a,l[a]);if(t)for(var a of t(l))o.call(l,a)&&u(e,a,l[a]);return e},s=(e,t)=>l(e,a(t)),r=(e,l,a)=>new Promise(((t,n)=>{var o=e=>{try{i(a.next(e))}catch(l){n(l)}},u=e=>{try{i(a.throw(e))}catch(l){n(l)}},i=e=>e.done?t(e.value):Promise.resolve(e.value).then(o,u);i((a=a.apply(e,l)).next())}));import{I as d,F as p,j as m}from"./index-1JOyfOxu.js";/* empty css                *//* empty css                  *//* empty css                *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                  *//* empty css                     *//* empty css                  */import{m as c,r as f,d as h,w as _,P as v,D as b,R as y,x as V,a5 as g,ao as k,u as x,S as j,X as w,ac as I,Q as C,ak as A,i as S,G as U,an as M,F as L,$ as E,a6 as N,W as T,M as O,o as P,E as H,C as R,a8 as $,V as q,a2 as D,aN as B,aM as W,aL as z,aK as F,a3 as K,ar as Q,as as X,aq as Z,ap as G,aT as J,Z as Y,aO as ee,aX as le,n as ae,p as te,aQ as ne,aB as oe,aI as ue}from"./vendor-8T3zXQLl.js";import{_ as ie}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css               *//* empty css               */import"./el-form-item-l0sNRNKZ.js";/* empty css                       *//* empty css                        */import{_ as se}from"./index-Zs-Thxwm.js";/* empty css                    */import{u as re,_ as de,A as pe}from"./index-DdiMqMK0.js";/* empty css                   *//* empty css                      */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./index-Wowen7jt.js";const me=new Set;function ce(){const e=[];try{Array.from(document.styleSheets).forEach((l=>{try{Array.from(l.cssRules||l.rules).forEach((l=>{const a=function(e){if(!(e instanceof CSSStyleRule))return null;const{selectorText:l,style:a}=e;if(!(null==l?void 0:l.startsWith(".iconsys-"))||!l.includes("::before"))return null;const t=l.substring(1).replace("::before",""),n=a.getPropertyValue("content");if(!n)return null;const o=n.replace(/['"\\]/g,"");return{className:t,unicode:o?`&#x${fe(o)};`:void 0}}(l);a&&e.push(a)}))}catch(a){const e={sheet:l,error:a};me.has(e)||me.add(e)}}))}catch(l){}return e}function fe(e){return e?e.charCodeAt(0).toString(16).padStart(4,"0"):""}const he={class:"icon-selector"},_e={class:"icon"},ve=["innerHTML"],be={class:"text"},ye={class:"arrow"},Ve={class:"icons-list"},ge=["onClick"],ke=["innerHTML"],xe={class:"dialog-footer"},je=ie(c(s(i({},{name:"ArtIconSelector"}),{__name:"index",props:{iconType:{default:d.CLASS_NAME},modelValue:{default:""},text:{default:"图标选择器"},width:{default:"200px"},size:{default:"default"},disabled:{type:Boolean,default:!1}},emits:["update:modelValue","getIcon"],setup(e,{emit:l}){const a=e,t=l,n=f(a.modelValue),o=f(!1),u=f("icons"),i=h((()=>ce()));_((()=>a.modelValue),(e=>{n.value=e}),{immediate:!0});const s=()=>{a.disabled||(o.value=!0)},r=()=>{n.value="",t("update:modelValue",""),t("getIcon","")},p=h((()=>a.iconType));return(e,l)=>{const m=M,c=N,f=A;return b(),v("div",he,[y("div",{class:j(["select",[e.size,{"is-disabled":e.disabled},{"has-icon":x(n)}]]),onClick:s,style:C({width:a.width})},[y("div",_e,[g(y("i",{class:j(`iconfont-sys ${x(n)}`)},null,2),[[k,a.iconType===x(d).CLASS_NAME]]),g(y("i",{class:"iconfont-sys",innerHTML:x(n)},null,8,ve),[[k,a.iconType===x(d).UNICODE]])]),y("div",be,w(a.text),1),y("div",ye,[l[3]||(l[3]=y("i",{class:"iconfont-sys arrow-icon"},"",-1)),y("i",{class:"iconfont-sys clear-icon",onClick:I(r,["stop"])},"")])],6),V(f,{title:"选择图标",width:"40%",modelValue:x(o),"onUpdate:modelValue":l[2]||(l[2]=e=>S(o)?o.value=e:null),"align-center":""},{footer:U((()=>[y("span",xe,[V(c,{onClick:l[0]||(l[0]=e=>o.value=!1)},{default:U((()=>l[4]||(l[4]=[T("取 消")]))),_:1,__:[4]}),V(c,{type:"primary",onClick:l[1]||(l[1]=e=>o.value=!1)},{default:U((()=>l[5]||(l[5]=[T("确 定")]))),_:1,__:[5]})])])),default:U((()=>[V(m,{height:"400px"},{default:U((()=>[g(y("ul",Ve,[(b(!0),v(L,null,E(x(i),(e=>(b(),v("li",{key:e.className,onClick:l=>(e=>{const l=a.iconType===d.CLASS_NAME?e.className:e.unicode||"";n.value=l,o.value=!1,t("update:modelValue",l),t("getIcon",l)})(e)},[g(y("i",{class:j(`iconfont-sys ${e.className}`)},null,2),[[k,x(p)===x(d).CLASS_NAME]]),g(y("i",{class:"iconfont-sys",innerHTML:e.unicode},null,8,ke),[[k,x(p)===x(d).UNICODE]])],8,ge)))),128))],512),[[k,"icons"===x(u)]])])),_:1})])),_:1},8,["modelValue"])])}}})),[["__scopeId","data-v-ed458c36"]]),we={class:"menu-page art-full-height"},Ie={class:"dialog-footer"},Ce=ie(c(s(i({},{name:"Menus"}),{__name:"index",setup(e){const l=f(!1),a=f([]),t=e=>{var l,a,t;return e.children&&e.children.length>0?"info":(null==(l=e.meta)?void 0:l.link)&&(null==(a=e.meta)?void 0:a.isIframe)?"success":e.path?"primary":(null==(t=e.meta)?void 0:t.link)?"warning":void 0},{columnChecks:n,columns:o}=re((()=>[{prop:"meta.title",label:"菜单名称",minWidth:120,formatter:e=>{var l;return m(null==(l=e.meta)?void 0:l.title)}},{prop:"type",label:"菜单类型",formatter:e=>te(ne,{type:t(e)},(()=>(e=>{var l,a,t;return e.children&&e.children.length>0?"目录":(null==(l=e.meta)?void 0:l.link)&&(null==(a=e.meta)?void 0:a.isIframe)?"内嵌":e.path?"菜单":(null==(t=e.meta)?void 0:t.link)?"外链":void 0})(e)))},{prop:"path",label:"路由",formatter:e=>{var l;return(null==(l=e.meta)?void 0:l.link)||e.path||""}},{prop:"component",label:"组件路径",formatter:e=>e.component||"/index/index"},{prop:"date",label:"编辑时间",formatter:()=>"2022-3-12 12:00:00"},{prop:"status",label:"隐藏菜单",formatter:e=>te(ne,{type:e.meta.isHide?"danger":"info"},(()=>e.meta.isHide?"是":"否"))},{prop:"operation",label:"操作",width:180,formatter:e=>{const l=[];return l.push(te(oe,{content:"添加子菜单",placement:"top"},(()=>te(pe,{type:"add",onClick:()=>ye("menu",null,!0)})))),l.push(te(oe,{content:"编辑",placement:"top"},(()=>te(pe,{type:"edit",onClick:()=>he("edit",e)})))),l.push(te(oe,{content:"删除",placement:"top"},(()=>te(pe,{type:"delete",onClick:()=>ge(e)})))),te("div",{class:"flex gap-2"},l)}}])),u=()=>{I()},i=f(!1),s=O({name:"",path:"",label:"",component:"",icon:"",isEnable:!0,sort:1,isMenu:!0,keepAlive:!0,isVisible:!0,link:"",isIframe:!1,parentId:0,authName:"",authLabel:"",authIcon:"",authSort:1}),c=f(d.UNICODE),_=f("menu"),k=O({name:[{required:!0,message:"请输入菜单名称",trigger:"blur"},{min:2,max:20,message:"长度在 2 到 20 个字符",trigger:"blur"}],path:[{required:!0,message:"请输入路由地址",trigger:"blur"}],label:[{required:!0,message:"输入权限标识",trigger:"blur"}],authName:[{required:!0,message:"请输入权限名称",trigger:"blur"}],authLabel:[{required:!0,message:"请输入权限权限标识",trigger:"blur"}]}),j=f([]);P((()=>{I()}));const I=()=>r(this,null,(function*(){l.value=!0;try{const e=yield p.getMenuList();j.value=e.menuList,C(e.menuList)}catch(e){H.error("获取菜单数据失败")}finally{l.value=!1}})),C=e=>{const l=[],t=e=>{e.forEach((e=>{l.push({id:e.id||0,title:e.meta.title}),e.children&&e.children.length>0&&t(e.children)}))};t(e),a.value=l},M=h((()=>j.value||[])),ie=f(!1),me=f(null),ce=f(),fe=h((()=>{const e="menu"===_.value?"菜单":"权限";return ie.value?`编辑${e}`:`新建${e}`})),he=(e,l)=>{ye("menu",l,!0)},_e=()=>{},ve=()=>{s.component="/index/index"},be=()=>r(this,null,(function*(){ce.value&&(yield ce.value.validate((e=>r(this,null,(function*(){if(e)try{const e={name:s.label,title:s.name,path:s.path,component:s.component||"/index/index",icon:s.icon,sort:s.sort||0,type:1,status:s.isVisible?1:0,keep_alive:s.keepAlive?1:0,external_link:s.link,is_iframe:s.isIframe?1:0,parent_id:s.parentId||0};ie.value&&me.value?(yield p.updateMenu(me.value,e),H.success("编辑成功")):(yield p.createMenu(e),H.success("新增成功")),i.value=!1,yield I()}catch(l){H.error((ie.value?"编辑":"新增")+"失败")}})))))})),ye=(e,l,a=!1)=>{i.value=!0,_.value=e,ie.value=!1,me.value=null,xe.value=a,Ve(),l&&(ie.value=!0,me.value=l.id||null,ae((()=>{s.name=m(l.meta.title),s.path=l.path,s.label=l.name,s.component=l.component||"/index/index",s.icon=l.meta.icon,s.sort=l.meta.sort||1,s.isMenu=l.meta.isMenu,s.keepAlive=l.meta.keepAlive,s.isVisible=!l.meta.isHide,s.isEnable=l.meta.isEnable||!0,s.link=l.meta.link,s.isIframe=l.meta.isIframe||!1,s.parentId=l.parent_id||0})))},Ve=()=>{var e;null==(e=ce.value)||e.resetFields(),Object.assign(s,{name:"",path:"",label:"",component:"",icon:"",sort:1,isMenu:!0,keepAlive:!0,isVisible:!0,link:"",isIframe:!1,parentId:0,authName:"",authLabel:"",authIcon:"",authSort:1})},ge=e=>r(this,null,(function*(){if(e&&e.id)try{yield ue.confirm("确定要删除该菜单吗？删除后无法恢复","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield p.deleteMenu(e.id),H.success("删除成功"),yield I()}catch(l){"cancel"!==l?H.error("删除失败"):H.info("已取消删除")}else H.error("无法获取菜单信息")})),ke=h((()=>!(!ie.value||"button"!==_.value)||(!(!ie.value||"menu"!==_.value)||!(ie.value||"menu"!==_.value||!xe.value)))),xe=f(!1),Ce=f(!1),Ae=f(),Se=()=>{Ce.value=!Ce.value,ae((()=>{Ae.value&&Ae.value[Ce.value?"expandAll":"collapseAll"]()}))};return(e,t)=>{const r=de,d=ee,p=se,m=W,f=B,h=D,j=K,I=F,C=z,O=je,P=X,H=Q,ae=Z,te=G,ne=$,oe=A,ue=J,ie=Y("ripple");return b(),v("div",we,[V(ue,{class:"art-table-card",shadow:"never"},{default:U((()=>[V(r,{showZebra:!1,columns:x(n),"onUpdate:columns":t[1]||(t[1]=e=>S(n)?n.value=e:null),onRefresh:u},{left:U((()=>[g((b(),R(x(N),{onClick:t[0]||(t[0]=e=>ye("menu",null,!0)),type:"primary"},{default:U((()=>t[21]||(t[21]=[T(" 添加菜单 ")]))),_:1,__:[21]})),[[ie]]),g((b(),R(x(N),{onClick:Se},{default:U((()=>[T(w(x(Ce)?"收起":"展开"),1)])),_:1})),[[ie]])])),_:1},8,["columns"]),V(p,{ref_key:"tableRef",ref:Ae,loading:x(l),data:x(M),tableConfig:{rowKey:"path",stripe:!1},layout:{marginTop:10}},{default:U((()=>[(b(!0),v(L,null,E(x(o),(e=>(b(),R(d,le({key:e.prop||e.type},{ref_for:!0},e),null,16)))),128))])),_:1},8,["loading","data"]),V(oe,{title:x(fe),modelValue:x(i),"onUpdate:modelValue":t[20]||(t[20]=e=>S(i)?i.value=e:null),width:"700px","align-center":""},{footer:U((()=>[y("span",Ie,[V(x(N),{onClick:t[18]||(t[18]=e=>i.value=!1)},{default:U((()=>t[25]||(t[25]=[T("取 消")]))),_:1,__:[25]}),V(x(N),{type:"primary",onClick:t[19]||(t[19]=e=>be())},{default:U((()=>t[26]||(t[26]=[T("确 定")]))),_:1,__:[26]})])])),default:U((()=>[V(ne,{ref_key:"formRef",ref:ce,model:x(s),rules:x(k),"label-width":"85px"},{default:U((()=>[V(h,{label:"菜单类型"},{default:U((()=>[V(f,{modelValue:x(_),"onUpdate:modelValue":t[2]||(t[2]=e=>S(_)?_.value=e:null),disabled:x(ke)},{default:U((()=>[V(m,{value:"menu"},{default:U((()=>t[22]||(t[22]=[T("菜单")]))),_:1,__:[22]}),V(m,{value:"button"},{default:U((()=>t[23]||(t[23]=[T("权限")]))),_:1,__:[23]})])),_:1},8,["modelValue","disabled"])])),_:1}),"menu"===x(_)?(b(),v(L,{key:0},[V(C,{gutter:20},{default:U((()=>[V(I,{span:12},{default:U((()=>[V(h,{label:"菜单名称",prop:"name"},{default:U((()=>[V(j,{modelValue:x(s).name,"onUpdate:modelValue":t[3]||(t[3]=e=>x(s).name=e),placeholder:"菜单名称"},null,8,["modelValue"])])),_:1})])),_:1}),V(I,{span:12},{default:U((()=>[V(h,{label:"路由地址",prop:"path"},{default:U((()=>[V(j,{modelValue:x(s).path,"onUpdate:modelValue":t[4]||(t[4]=e=>x(s).path=e),placeholder:"路由地址"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),V(C,{gutter:20},{default:U((()=>[V(I,{span:12},{default:U((()=>[V(h,{label:"权限标识",prop:"label"},{default:U((()=>[V(j,{modelValue:x(s).label,"onUpdate:modelValue":t[5]||(t[5]=e=>x(s).label=e),placeholder:"权限标识"},null,8,["modelValue"])])),_:1})])),_:1}),V(I,{span:12},{default:U((()=>[V(h,{label:"组件路径",prop:"component"},{default:U((()=>[V(j,{modelValue:x(s).component,"onUpdate:modelValue":t[6]||(t[6]=e=>x(s).component=e),placeholder:"组件路径，如：/index/index"},{prepend:U((()=>[V(x(N),{onClick:ve,size:"small",type:"primary"},{default:U((()=>t[24]||(t[24]=[T(" 默认 ")]))),_:1,__:[24]})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),V(C,{gutter:20},{default:U((()=>[V(I,{span:12},{default:U((()=>[V(h,{label:"图标",prop:"icon"},{default:U((()=>[V(O,{modelValue:x(s).icon,"onUpdate:modelValue":t[7]||(t[7]=e=>x(s).icon=e),iconType:x(c),width:"100%"},null,8,["modelValue","iconType"])])),_:1})])),_:1}),V(I,{span:12})])),_:1}),V(C,{gutter:20},{default:U((()=>[V(I,{span:12},{default:U((()=>[V(h,{label:"父级菜单",prop:"parentId"},{default:U((()=>[V(H,{modelValue:x(s).parentId,"onUpdate:modelValue":t[8]||(t[8]=e=>x(s).parentId=e),placeholder:"请选择父级菜单",clearable:"",style:{width:"100%"}},{default:U((()=>[V(P,{label:"顶级菜单",value:0}),(b(!0),v(L,null,E(x(a),(e=>(b(),R(P,{key:e.id,label:e.title,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1}),V(I,{span:12},{default:U((()=>[V(h,{label:"菜单排序",prop:"sort",style:{width:"100%"}},{default:U((()=>[V(ae,{modelValue:x(s).sort,"onUpdate:modelValue":t[9]||(t[9]=e=>x(s).sort=e),style:{width:"100%"},onChange:_e,min:1,"controls-position":"right"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),V(C,{gutter:20},{default:U((()=>[V(I,{span:24},{default:U((()=>[V(h,{label:"外部链接",prop:"link"},{default:U((()=>[V(j,{modelValue:x(s).link,"onUpdate:modelValue":t[10]||(t[10]=e=>x(s).link=e),placeholder:"外部链接/内嵌地址(https://www.baidu.com)"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),V(C,{gutter:20},{default:U((()=>[V(I,{span:5},{default:U((()=>[V(h,{label:"是否启用",prop:"isEnable"},{default:U((()=>[V(te,{modelValue:x(s).isEnable,"onUpdate:modelValue":t[11]||(t[11]=e=>x(s).isEnable=e)},null,8,["modelValue"])])),_:1})])),_:1}),V(I,{span:5},{default:U((()=>[V(h,{label:"页面缓存",prop:"keepAlive"},{default:U((()=>[V(te,{modelValue:x(s).keepAlive,"onUpdate:modelValue":t[12]||(t[12]=e=>x(s).keepAlive=e)},null,8,["modelValue"])])),_:1})])),_:1}),V(I,{span:5},{default:U((()=>[V(h,{label:"是否显示",prop:"isVisible"},{default:U((()=>[V(te,{modelValue:x(s).isVisible,"onUpdate:modelValue":t[13]||(t[13]=e=>x(s).isVisible=e)},null,8,["modelValue"])])),_:1})])),_:1}),V(I,{span:5},{default:U((()=>[V(h,{label:"是否内嵌",prop:"isMenu"},{default:U((()=>[V(te,{modelValue:x(s).isIframe,"onUpdate:modelValue":t[14]||(t[14]=e=>x(s).isIframe=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})],64)):q("",!0),"button"===x(_)?(b(),v(L,{key:1},[V(C,{gutter:20},{default:U((()=>[V(I,{span:12},{default:U((()=>[V(h,{label:"权限名称",prop:"authName"},{default:U((()=>[V(j,{modelValue:x(s).authName,"onUpdate:modelValue":t[15]||(t[15]=e=>x(s).authName=e),placeholder:"权限名称"},null,8,["modelValue"])])),_:1})])),_:1}),V(I,{span:12},{default:U((()=>[V(h,{label:"权限标识",prop:"authLabel"},{default:U((()=>[V(j,{modelValue:x(s).authLabel,"onUpdate:modelValue":t[16]||(t[16]=e=>x(s).authLabel=e),placeholder:"权限标识"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),V(C,{gutter:20},{default:U((()=>[V(I,{span:12},{default:U((()=>[V(h,{label:"权限排序",prop:"authSort",style:{width:"100%"}},{default:U((()=>[V(ae,{modelValue:x(s).authSort,"onUpdate:modelValue":t[17]||(t[17]=e=>x(s).authSort=e),style:{width:"100%"},onChange:_e,min:1,"controls-position":"right"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})],64)):q("",!0)])),_:1},8,["model","rules"])])),_:1},8,["title","modelValue"])])),_:1})])}}})),[["__scopeId","data-v-8a406627"]]);export{Ce as default};
