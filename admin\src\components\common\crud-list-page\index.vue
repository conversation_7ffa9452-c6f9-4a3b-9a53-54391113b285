<!-- 通用CRUD列表页面组件 -->
<template>
  <div class="crud-list-page art-full-height">
    <!-- 搜索栏 -->
    <CommonSearchBar
      v-if="config.search?.enabled"
      v-model:filter="searchParams"
      :items="config.search?.items || []"
      @reset="handleSearchReset"
      @search="handleSearch"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton
            v-if="config.actions?.create?.enabled"
            @click="handleCreate"
            :type="config.actions.create.type || 'primary'"
            v-ripple
          >
            {{ config.actions.create.text || '新增' }}
          </ElButton>
          
          <!-- 自定义头部按钮 -->
          <slot name="header-actions" :selected-rows="selectedRows"></slot>
        </template>
      </ArtTableHeader>

      <!-- 表格 -->
      <ArtTable
        :loading="loading"
        :data="tableData as T[]"
        :columns="computedColumns"
        :pagination="pagination"
        :table-config="config.table || { rowKey: 'id' }"
        :layout="{ marginTop: 10 }"
        @row:selection-change="handleSelectionChange"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
      />

      <!-- 通用弹窗 -->
      <CommonFormDialog
        v-if="config.dialog?.enabled"
        v-model:visible="dialogVisible"
        :type="dialogType"
        :title="dialogTitle"
        :form-config="config.dialog.formConfig"
        :form-data="currentFormData"
        :loading="dialogLoading"
        @submit="handleDialogSubmit"
      />
      
      <!-- 自定义弹窗 -->
      <slot
        name="custom-dialog"
        :visible="dialogVisible"
        :type="dialogType"
        :data="currentFormData"
        :close="() => dialogVisible = false"
        :submit="handleDialogSubmit"
      ></slot>
    </ElCard>
  </div>
</template>

<script setup lang="ts" generic="T extends Record<string, any>">
import { ref, computed, onMounted, h } from 'vue'
import { ElCard, ElButton, ElMessage, ElMessageBox, ElTooltip } from 'element-plus'
import { useTable } from '@/composables/useTable'
import ArtTable from '@/components/core/tables/art-table/index.vue'
import ArtTableHeader from '@/components/core/tables/art-table-header/index.vue'
import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
import CommonSearchBar from '../common-search-bar/index.vue'
import CommonFormDialog from '../common-form-dialog/index.vue'
import type { CrudListConfig, CrudFormData } from './types.ts'

interface Props {
  config: CrudListConfig<T>
}

interface Emits {
  (e: 'create', data: CrudFormData): void
  (e: 'update', id: string | number, data: CrudFormData): void
  (e: 'delete', id: string | number): void
  (e: 'selection-change', rows: T[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 弹窗状态
const dialogVisible = ref(false)
const dialogType = ref<'create' | 'edit'>('create')
const dialogLoading = ref(false)
const currentFormData = ref<CrudFormData>({})
const selectedRows = ref<T[]>([])

// 搜索参数
const searchParams = ref(props.config.search?.defaultParams || {})

// 计算列配置（添加操作列）
const computedColumns = computed(() => {
  const baseColumns = props.config.columns || []
  
  if (props.config.actions?.enabled) {
    const actionColumn = {
      prop: 'operation',
      label: '操作',
      width: props.config.actions.width || 120,
      fixed: 'right' as const,
      formatter: (row: T) => {
        return h('div', {
          class: 'flex items-center gap-1',
          style: { whiteSpace: 'nowrap' }
        }, [
          // 编辑按钮
          props.config.actions?.edit?.enabled && h(ArtButtonTable, {
            type: 'edit',
            onClick: () => handleEdit(row)
          }),
          // 自定义操作按钮
          ...(props.config.actions?.custom?.map((action: any) => {
            // 支持的 ArtButtonTable 类型
            const supportedTypes = ['add', 'edit', 'delete', 'view', 'more']

            // 如果是支持的类型，使用 ArtButtonTable
            if (supportedTypes.includes(action.type)) {
              return h(ElTooltip, {
                content: action.text || '操作',
                placement: 'top'
              }, () =>
                h(ArtButtonTable, {
                  type: action.type,
                  onClick: () => action.handler && action.handler(row)
                })
              )
            } else {
              // 不支持的类型使用 ElButton
              return h(ElTooltip, {
                content: action.text || '操作',
                placement: 'top'
              }, () =>
                h(ElButton, {
                  type: action.buttonType || 'primary',
                  size: 'small',
                  link: true,
                  onClick: () => action.handler && action.handler(row)
                }, () => action.text)
              )
            }
          }) || []),
          // 删除按钮（放在最后）
          props.config.actions?.delete?.enabled && h(ArtButtonTable, {
            type: 'delete',
            onClick: () => handleDelete(row)
          })
        ])
      }
    }
    
    return [...baseColumns, actionColumn]
  }
  
  return baseColumns
})

// 表格数据管理
const {
  columnChecks,
  tableData,
  isLoading: loading,
  paginationState: pagination,
  searchData: getDataByPage,
  resetSearch: resetSearchParams,
  onPageSizeChange: handleSizeChange,
  onCurrentPageChange: handleCurrentChange,
  refreshAll: refresh
} = useTable<T>({
  core: {
    apiFn: props.config.api.list,
    apiParams: {
      current: 1,
      size: 20,
      ...props.config.search?.defaultParams
    },
    columnsFactory: () => computedColumns.value,
    immediate: false // 禁用自动加载，由组件控制
  },
  hooks: {
    onError: (error) => ElMessage.error(error.message)
  }
})

// 弹窗标题
const dialogTitle = computed(() => {
  const titles = props.config.dialog?.titles || {}
  return dialogType.value === 'create' 
    ? titles.create || '新增'
    : titles.edit || '编辑'
})

// 事件处理
const handleSearch = () => {
  getDataByPage(searchParams.value)
}

const handleSearchReset = () => {
  searchParams.value = { ...props.config.search?.defaultParams || {} }
  resetSearchParams()
}

const handleRefresh = () => {
  refresh()
}

const handleSelectionChange = (rows: T[]) => {
  selectedRows.value = rows
  emit('selection-change', rows)
}

const handleCreate = () => {
  // 如果配置了弹窗，使用弹窗模式
  if (props.config.dialog?.enabled) {
    dialogType.value = 'create'
    currentFormData.value = {}
    dialogVisible.value = true
  } else {
    // 否则触发自定义创建事件
    emit('create', {})
  }
}

const handleEdit = (row: T) => {
  // 如果配置了弹窗，使用弹窗模式
  if (props.config.dialog?.enabled) {
    dialogType.value = 'edit'
    currentFormData.value = { ...row }
    dialogVisible.value = true
  } else {
    // 否则触发自定义编辑事件，传递完整的行数据
    emit('update', row.id, row)
  }
}

const handleDelete = async (row: T) => {
  try {
    await ElMessageBox.confirm(
      props.config.actions?.delete?.confirmText || '确定要删除这条记录吗？',
      '提示',
      { type: 'warning' }
    )

    if (props.config.api.delete) {
      // 调试信息已移除，保持代码整洁
      console.log('当前表格所有数据:', tableData.value)
      console.log('表格数据中每行的ID:', tableData.value.map((item: any, index: number) => ({
        index,
        id: item.id,
        title: item.title
      })))
      await props.config.api.delete(row.id)
      ElMessage.success('删除成功')
      refresh()
    }

    emit('delete', row.id)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      const errorMessage = (error as any)?.response?.data?.msg || (error as any)?.message || '删除失败'
      ElMessage.error(errorMessage)
    }
  }
}

const handleDialogSubmit = async (formData: CrudFormData) => {
  try {
    dialogLoading.value = true
    
    if (dialogType.value === 'create') {
      if (props.config.api.create) {
        await props.config.api.create(formData)
      }
      emit('create', formData)
      ElMessage.success('创建成功')
    } else {
      if (props.config.api.update) {
        await props.config.api.update(currentFormData.value.id, formData)
      }
      emit('update', currentFormData.value.id, formData)
      ElMessage.success('更新成功')
    }
    
    dialogVisible.value = false
    refresh()
  } catch (error) {
    ElMessage.error(dialogType.value === 'create' ? '创建失败' : '更新失败')
  } finally {
    dialogLoading.value = false
  }
}

onMounted(() => {
  if (props.config.autoLoad !== false) {
    getDataByPage()
  }
})

// 暴露方法给父组件
defineExpose({
  refresh,
  handleRefresh,
  getDataByPage,
  selectedRows
})
</script>

<style scoped>
.crud-list-page {
  display: flex;
  flex-direction: column;
  gap: 0;
}
</style>
