var e=Object.defineProperty,a=Object.defineProperties,t=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,s=(a,t,o)=>t in a?e(a,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):a[t]=o,i=(e,a)=>{for(var t in a||(a={}))l.call(a,t)&&s(e,t,a[t]);if(o)for(var t of o(a))r.call(a,t)&&s(e,t,a[t]);return e},n=(e,o)=>a(e,t(o));import{g as d,z as u}from"./index-DG9-1w7X.js";/* empty css                   */import{u as c,b as h,g as f,_ as y,L as v}from"./useChart-CZIGBtkl.js";import{k as p,r as m,c as g,w as b,d as x,O as w,a5 as A,aJ as L,P as S,D as _,Q as O,C as j,V as D,u as B,ai as C,x as P}from"./vendor-84Inc-Pt.js";import{_ as T}from"./_plugin-vue_export-helper-BCo6x5W8.js";const W=T(p(n(i({},{name:"ArtLineChart"}),{__name:"index",props:{data:{default:()=>[0,0,0,0,0,0,0]},xAxisData:{default:()=>[]},lineWidth:{default:2.5},showAreaColor:{type:Boolean,default:!1},smooth:{type:Boolean,default:!0},symbol:{default:"none"},symbolSize:{default:6},animationDelay:{default:200},height:{default:c().chartHeight},loading:{type:Boolean,default:!1},isEmpty:{type:Boolean,default:!1},colors:{default:()=>c().colors},showAxisLabel:{type:Boolean,default:!0},showAxisLine:{type:Boolean,default:!0},showSplitLine:{type:Boolean,default:!0},showTooltip:{type:Boolean,default:!0},showLegend:{type:Boolean,default:!1},legendPosition:{default:"bottom"}},setup(e){const a=e,{chartRef:t,isDark:o,initChart:l,getAxisLineStyle:r,getAxisLabelStyle:s,getAxisTickStyle:c,getSplitLineStyle:p,getTooltipStyle:C,getLegendStyle:P,getGridWithLegend:T}=h(),W=m(!1),k=m(),E=m([]),z=()=>{k.value&&(clearTimeout(k.value),k.value=void 0)},I=g((()=>{if(a.isEmpty)return!0;if(Array.isArray(a.data)&&"number"==typeof a.data[0]){const e=a.data;return!e.length||e.every((e=>0===e))}if(Array.isArray(a.data)&&"object"==typeof a.data[0]){const e=a.data;return!e.length||e.every((e=>{var a;return!(null==(a=e.data)?void 0:a.length)||e.data.every((e=>0===e))}))}return!0})),M=g((()=>Array.isArray(a.data)&&a.data.length>0&&"object"==typeof a.data[0]&&"name"in a.data[0])),R=g((()=>{if(M.value){return a.data.reduce(((e,a)=>{var t;if(null==(t=a.data)?void 0:t.length){const t=Math.max(...a.data);return Math.max(e,t)}return e}),0)}{const e=a.data;return(null==e?void 0:e.length)?Math.max(...e):0}})),V=()=>{if(M.value){return a.data.map((e=>n(i({},e),{data:new Array(e.data.length).fill(0)})))}{const e=a.data;return new Array(e.length).fill(0)}},G=()=>(M.value,[...a.data]),H=(e,t)=>e||(void 0!==t?a.colors[t%a.colors.length]:d("--el-color-primary")),J=e=>{var t,o,l,r,s;return{name:e.name,data:e.data,type:"line",color:e.color,smooth:null!=(t=e.smooth)?t:a.smooth,symbol:null!=(o=e.symbol)?o:a.symbol,symbolSize:null!=(l=e.symbolSize)?l:a.symbolSize,lineStyle:{width:null!=(r=e.lineWidth)?r:a.lineWidth,color:e.color},areaStyle:e.areaStyle,emphasis:{focus:"series",lineStyle:{width:(null!=(s=e.lineWidth)?s:a.lineWidth)+1}}}},Q=(e=!1)=>{const t={animation:!0,animationDuration:e?0:1300,animationDurationUpdate:e?0:1300,grid:T(a.showLegend&&M.value,a.legendPosition,{top:15,right:15,left:0}),tooltip:a.showTooltip?C():void 0,xAxis:{type:"category",boundaryGap:!1,data:a.xAxisData,axisTick:c(),axisLine:r(a.showAxisLine),axisLabel:s(a.showAxisLabel)},yAxis:{type:"value",min:0,max:R.value,axisLabel:s(a.showAxisLabel),axisLine:r(a.showAxisLine),splitLine:p(a.showSplitLine)}};if(a.showLegend&&M.value&&(t.legend=P(a.legendPosition)),M.value){const e=E.value;t.series=e.map(((e,t)=>{const o=H(a.colors[t],t),l=((e,t)=>{if(!e.areaStyle&&!e.showAreaColor&&!a.showAreaColor)return;const o=e.areaStyle||{};return o.custom?o.custom:{color:new v(0,0,0,1,[{offset:0,color:u(t,o.startOpacity||.2).rgba},{offset:1,color:u(t,o.endOpacity||.02).rgba}])}})(e,o);return J({name:e.name,data:e.data,color:o,smooth:e.smooth,symbol:e.symbol,lineWidth:e.lineWidth,areaStyle:l})}))}else{const e=E.value,o=H(a.colors[0]),l=(()=>{if(!a.showAreaColor)return;const e=H(a.colors[0]);return{color:new v(0,0,0,1,[{offset:0,color:u(e,.2).rgba},{offset:1,color:u(e,.02).rgba}])}})();t.series=[J({data:e,color:o,areaStyle:l})]}return t},U=e=>{I.value||l(e)},q=()=>{if(I.value)E.value=G(),U(Q(!1));else if(z(),W.value=!0,M.value){const e=a.data;E.value=V(),U(Q(!0)),e.forEach(((e,t)=>{setTimeout((()=>{const a=E.value;a[t]=i({},e),E.value=[...a],U(Q(!1))}),t*a.animationDelay+100)}));const t=(e.length-1)*a.animationDelay+1500;setTimeout((()=>{W.value=!1}),t)}else E.value=V(),U(Q(!0)),k.value=setTimeout((()=>{E.value=G(),U(Q(!1)),W.value=!1}),100)},F=()=>{q()},K=()=>{q()};return b([()=>a.data,()=>a.xAxisData,()=>a.colors],(()=>{W.value||F()}),{deep:!0}),b(o,(()=>{var e;const a=(null==(e=t.value)?void 0:e.__echart__)||f(t.value);if(a&&!I.value){const e=Q(!1);a.setOption(e)}})),x((()=>{F(),t.value&&t.value.addEventListener("chartVisible",K)})),w((()=>{z(),t.value&&t.value.removeEventListener("chartVisible",K)})),(e,o)=>{const l=y,r=L;return A((_(),S("div",{ref_key:"chartRef",ref:t,class:"art-line-chart",style:O({height:a.height})},[B(I)?(_(),j(l,{key:0})):D("",!0)],4)),[[r,a.loading]])}}})),[["__scopeId","data-v-79c89510"]]),k={class:"card art-custom-card"},E=T(p({__name:"SalesOverview",setup(e){const a=[50,25,40,20,70,35,65,30,35,20,40,44],t=["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"];return(e,o)=>{const l=W;return _(),S("div",k,[o[0]||(o[0]=C('<div class="card-header" data-v-ff2928a7><div class="title" data-v-ff2928a7><h4 class="box-title" data-v-ff2928a7>访问量</h4><p class="subtitle" data-v-ff2928a7>今年增长<span class="text-success" data-v-ff2928a7>+15%</span></p></div></div>',1)),P(l,{class:"chart",height:"calc(100% - 40px)",data:a,xAxisData:t,showAreaColor:!0,showAxisLine:!1})])}}}),[["__scopeId","data-v-ff2928a7"]]);export{E as default};
