<template>
  <ElDialog
    v-model="dialogVisible"
    title="编辑用户"
    width="600px"
    align-center
  >
    <ElForm ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <!-- 头像 -->
      <ElFormItem label="头像" prop="avatar">
        <div class="media-upload-field">
          <!-- 已选择的图片预览 -->
          <div v-if="formData.avatar" class="selected-media-box">
            <img
              :src="getImageUrl(formData.avatar)"
              alt="头像"
              class="media-preview-image"
            />
            <div class="media-overlay">
              <ElButton size="small" @click="openMediaPicker">重新选择</ElButton>
              <ElButton size="small" type="danger" @click="removeMedia">移除</ElButton>
            </div>
          </div>
          <!-- 上传区域 -->
          <div v-else class="upload-area" @click="openMediaPicker">
            <ElIcon class="upload-icon"><Plus /></ElIcon>
          </div>
          <div class="upload-tip">请选择头像（可选）</div>
        </div>
      </ElFormItem>

      <!-- 手机号 -->
      <ElFormItem label="手机号" prop="userPhone">
        <ElInput v-model="formData.userPhone" placeholder="请输入手机号" />
      </ElFormItem>

      <!-- 昵称 -->
      <ElFormItem label="昵称" prop="nickName">
        <ElInput v-model="formData.nickName" placeholder="请输入昵称" />
      </ElFormItem>

      <!-- 密码行 - 编辑时可选 -->
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="密码" prop="password">
            <ElInput v-model="formData.password" type="password" placeholder="不填则不修改" show-password />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="确认密码" prop="confirmPassword">
            <ElInput v-model="formData.confirmPassword" type="password" placeholder="不填则不修改" show-password />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <!-- 支付密码行 - 编辑时可选 -->
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="支付密码" prop="payPassword">
            <ElInput v-model="formData.payPassword" type="password" placeholder="不填则不修改" show-password />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="确认密码" prop="confirmPayPassword">
            <ElInput v-model="formData.confirmPayPassword" type="password" placeholder="不填则不修改" show-password />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <!-- 推荐人 -->
      <ElFormItem label="推荐人" prop="referrer">
        <ElInput v-model="formData.referrer" placeholder="请输入推荐人手机号或用户名（可选）" />
      </ElFormItem>
    </ElForm>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="dialogVisible = false">取消</ElButton>
        <ElButton type="primary" @click="handleSubmit">确定</ElButton>
      </div>
    </template>
  </ElDialog>

  <!-- 媒体文件选择器 -->
  <MediaPicker
    v-model="showMediaPicker"
    title="选择用户头像"
    :multiple="false"
    accept="image/*"
    @confirm="handleMediaConfirm"
  />
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage, ElIcon } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import MediaPicker from '@/components/MediaPicker/index.vue'
import { UserService } from '@/api/usersApi'
import { resolveFileUrl } from '@/utils/url'
import type { MediaFile } from '@/api/mediaApi'

interface Props {
  visible: boolean
  userData?: any
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'submit'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 对话框显示控制
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 表单实例
const formRef = ref<FormInstance>()

// 媒体选择器
const showMediaPicker = ref(false)

// 表单数据
const formData = reactive({
  avatar: '',
  userPhone: '',
  nickName: '',
  password: '',
  confirmPassword: '',
  payPassword: '',
  confirmPayPassword: '',
  referrer: ''
})

// 表单验证规则 - 编辑时密码不是必填
const rules: FormRules = {
  userPhone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  nickName: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { min: 2, max: 20, message: '昵称长度必须在2-20位之间', trigger: 'blur' }
  ],
  password: [
    { min: 6, max: 32, message: '密码长度必须在6-32位之间', trigger: 'blur' }
  ],
  confirmPassword: [
    {
      validator: (_rule: any, value: string, callback: Function) => {
        if (formData.password && value !== formData.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  payPassword: [
    { pattern: /^\d{6}$/, message: '支付密码必须是6位数字', trigger: 'blur' }
  ],
  confirmPayPassword: [
    {
      validator: (_rule: any, value: string, callback: Function) => {
        if (formData.payPassword && value !== formData.payPassword) {
          callback(new Error('两次输入的支付密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 初始化表单数据
const initFormData = () => {
  if (props.userData) {
    Object.assign(formData, {
      avatar: props.userData.avatar || '',
      userPhone: props.userData.userPhone || '',
      nickName: props.userData.nickName || '',
      password: '',
      confirmPassword: '',
      payPassword: '',
      confirmPayPassword: '',
      referrer: props.userData.referrer || ''
    })
  }
}

// 监听对话框状态变化
watch(
  () => [props.visible, props.userData],
  ([visible]) => {
    if (visible) {
      initFormData()
      nextTick(() => {
        formRef.value?.clearValidate()
      })
    }
  },
  { immediate: true }
)

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 准备更新数据 - 只传递非空的密码字段
        const updateData: any = { ...formData }
        if (!updateData.password) {
          delete updateData.password
          delete updateData.confirmPassword
        }
        if (!updateData.payPassword) {
          delete updateData.payPassword
          delete updateData.confirmPayPassword
        }

        await UserService.updateUser(props.userData.id, updateData)
        ElMessage.success('更新用户成功')
        dialogVisible.value = false
        emit('submit')
      } catch (error) {
        ElMessage.error('更新用户失败：' + (error as any)?.message || '未知错误')
      }
    }
  })
}

// 媒体处理方法
const getImageUrl = (url: string) => {
  return resolveFileUrl(url)
}

const openMediaPicker = () => {
  showMediaPicker.value = true
}

const removeMedia = () => {
  formData.avatar = ''
}

const handleMediaConfirm = (files: MediaFile[]) => {
  if (files.length > 0) {
    formData.avatar = files[0].file_url || ''
  }
}
</script>

<style scoped>
.media-upload-field {
  width: 100%;
}

.selected-media-box {
  position: relative;
  display: inline-block;
  border: 1px solid #dcdfe6;
  border-radius: 50%;
  overflow: hidden;
}

.media-preview-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  display: block;
}

.media-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.selected-media-box:hover .media-overlay {
  opacity: 1;
}

.upload-area {
  width: 80px;
  height: 80px;
  border: 2px dashed #dcdfe6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #409eff;
}

.upload-icon {
  font-size: 24px;
  color: #8c939d;
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}
</style>
