<template>
  <div class="page-content">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      
      <!-- 基础信息 -->
      <el-divider content-position="left">基础信息</el-divider>

      <el-form-item label="网站名称" prop="site_name">
        <el-input
          v-model="formData.site_name"
          placeholder="请输入网站名称"
          maxlength="50"
          show-word-limit
          style="width: 400px;"
        />
      </el-form-item>

      <el-form-item label="网站描述" prop="site_description">
        <el-input
          v-model="formData.site_description"
          type="textarea"
          :rows="3"
          placeholder="请输入网站描述"
          maxlength="200"
          show-word-limit
          style="width: 500px;"
        />
      </el-form-item>

      <el-form-item label="网站关键词" prop="site_keywords">
        <el-input
          v-model="formData.site_keywords"
          placeholder="请输入网站关键词，多个关键词用逗号分隔"
          maxlength="100"
          show-word-limit
          style="width: 450px;"
        />
      </el-form-item>
      
      <el-form-item label="网站Logo" prop="site_logo">
        <div class="upload-container">
          <div class="image-preview-container">
            <div class="image-preview" @click="openMediaPicker('logo')">
              <img v-if="formData.site_logo" :src="resolveFileUrl(formData.site_logo)" class="logo" />
              <div v-else class="placeholder">
                <el-icon class="placeholder-icon"><Plus /></el-icon>
              </div>
            </div>
            <div v-if="formData.site_logo" class="image-actions">
              <el-button size="small" type="danger" @click="clearImage('logo')">清除</el-button>
            </div>
          </div>
          <div class="upload-tip">
            <p>建议尺寸：200x60px，支持jpg、png格式，大小不超过2MB</p>
          </div>
        </div>
      </el-form-item>
      
      <el-form-item label="网站图标" prop="site_favicon">
        <div class="upload-container">
          <div class="image-preview-container">
            <div class="image-preview favicon-preview" @click="openMediaPicker('favicon')">
              <img v-if="formData.site_favicon" :src="resolveFileUrl(formData.site_favicon)" class="favicon" />
              <div v-else class="placeholder">
                <el-icon class="placeholder-icon"><Plus /></el-icon>
              </div>
            </div>
            <div v-if="formData.site_favicon" class="image-actions">
              <el-button size="small" type="danger" @click="clearImage('favicon')">清除</el-button>
            </div>
          </div>
          <div class="upload-tip">
            <p>建议尺寸：32x32px，支持ico、png格式，大小不超过1MB</p>
          </div>
        </div>
      </el-form-item>
      

      
      <!-- 其他设置 -->
      <el-divider content-position="left">其他设置</el-divider>

      <el-form-item label="版权信息" prop="site_copyright">
        <el-input
          v-model="formData.site_copyright"
          placeholder="请输入版权信息"
          maxlength="100"
          show-word-limit
          style="width: 450px;"
        />
      </el-form-item>

      <el-form-item label="ICP备案号" prop="site_icp">
        <el-input
          v-model="formData.site_icp"
          placeholder="请输入ICP备案号"
          maxlength="50"
          style="width: 300px;"
        />
      </el-form-item>

      <el-form-item label="Logo尺寸" prop="logo_size">
        <el-input-number
          v-model="formData.logo_size"
          :min="24"
          :max="100"
          :step="4"
          controls-position="right"
          placeholder="Logo显示尺寸（像素）"
          style="width: 200px;"
        />
        <span style="margin-left: 10px; color: #999;">像素（建议24-100px）</span>
      </el-form-item>

      <!-- 上传设置 -->
      <el-divider content-position="left">上传设置</el-divider>

      <el-form-item label="最大上传大小" prop="upload_max_size">
        <el-input-number
          v-model="formData.upload_max_size"
          :min="1"
          :max="100"
          :step="1"
          controls-position="right"
          style="width: 200px;"
        />
        <span class="input-suffix">MB</span>
      </el-form-item>

      <el-form-item label="允许上传类型" prop="upload_allowed_ext">
        <el-input
          v-model="formData.upload_allowed_ext"
          placeholder="请输入允许上传的文件扩展名，用逗号分隔"
          maxlength="200"
          style="width: 500px;"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          保存设置
        </el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 图片选择器 -->
    <MediaPicker
      v-model="showMediaPicker"
      :title="currentImageType === 'logo' ? '选择网站Logo' : '选择网站图标'"
      :multiple="false"
      accept="image/*"
      :max-size="5"
      :default-category="4"
      width="60%"
      @confirm="handleMediaSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { SystemService, type SystemConfigFormData } from '@/api/systemApi'
import MediaPicker from '@/components/MediaPicker/index.vue'
import type { MediaFile } from '@/api/mediaApi'
import { useSystemStore } from '@/store/modules/system'
import { resolveFileUrl } from '@/utils/url'

defineOptions({ name: 'SystemSettings' })

// 表单引用
const formRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)
const submitLoading = ref(false)
const systemStore = useSystemStore()

// 媒体选择器
const showMediaPicker = ref(false)
const currentImageType = ref<'logo' | 'favicon'>('logo')

// 表单数据
const formData = reactive<SystemConfigFormData>({
  site_name: '',
  site_logo: '',
  site_description: '',
  site_keywords: '',
  site_favicon: '',
  site_copyright: '',
  site_icp: '',
  upload_max_size: 10,
  upload_allowed_ext: 'jpg,jpeg,png,gif,webp,pdf,doc,docx,xls,xlsx',
  logo_size: 48
})

// 表单验证规则
const rules = reactive<FormRules>({
  site_name: [
    { required: true, message: '请输入网站名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  upload_max_size: [
    { required: true, message: '请设置最大上传大小', trigger: 'blur' }
  ]
})

// 获取配置数据
const getConfigData = async () => {
  loading.value = true
  try {
    const data = await SystemService.getConfig()
    console.log('获取到的配置数据:', data) // 添加调试日志
    if (data) {
      // 清空现有数据
      Object.keys(formData).forEach(key => {
        if (key !== 'upload_max_size' && key !== 'logo_size') {
          formData[key] = ''
        }
      })

      // 赋值新数据
      Object.assign(formData, data)

      // 转换上传大小从字节到MB
      if (data.upload_max_size) {
        formData.upload_max_size = Math.round(data.upload_max_size / 1024 / 1024)
      }

      // 设置默认Logo尺寸
      if (!data.logo_size) {
        formData.logo_size = 48
      }

      console.log('处理后的表单数据:', formData) // 添加调试日志

      // 立即更新全局配置
      systemStore.updateSystemConfig(formData)
    }
  } catch (error) {
    ElMessage.error('获取配置失败')
    console.error('获取配置错误:', error)
  } finally {
    loading.value = false
  }
}

// 打开媒体选择器
const openMediaPicker = (type: 'logo' | 'favicon') => {
  currentImageType.value = type
  showMediaPicker.value = true
}

// 清除图片
const clearImage = (type: 'logo' | 'favicon') => {
  if (type === 'logo') {
    formData.site_logo = ''
  } else {
    formData.site_favicon = ''
  }
}

// 媒体选择回调
const handleMediaSelect = (files: MediaFile[]) => {
  if (files.length > 0) {
    const file = files[0]
    if (currentImageType.value === 'logo') {
      // 存储文件路径，而不是完整URL
      formData.site_logo = file.file_path
      ElMessage.success('Logo设置成功')
    } else {
      formData.site_favicon = file.file_path
      ElMessage.success('网站图标设置成功')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    submitLoading.value = true

    // 转换上传大小为字节
    const submitData = {
      ...formData,
      upload_max_size: (formData.upload_max_size || 10) * 1024 * 1024
    }

    await SystemService.setConfig(submitData)

    // 更新全局系统配置
    systemStore.updateSystemConfig(formData)

    ElMessage.success('保存成功')
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error('保存失败')
      console.error(error)
    }
  } finally {
    submitLoading.value = false
  }
}

// 重置表单
const handleReset = () => {
  ElMessageBox.confirm('确定要重置表单吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    formRef.value?.resetFields()
    getConfigData()
  })
}

// 组件挂载时获取数据
onMounted(() => {
  getConfigData()
})
</script>

<style scoped>
/* 分割线标题颜色 */
:deep(.el-divider__text) {
  color: #303133 !important;
  font-weight: 500;
}

.upload-container {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.image-preview-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.image-preview {
  width: 200px;
  height: 60px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: border-color 0.3s;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-preview:hover {
  border-color: #409eff;
}

.image-preview.favicon-preview {
  width: 60px;
  height: 60px;
}

.image-preview .logo,
.image-preview .favicon {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8c939d;
  font-size: 12px;
  text-align: center;
  padding: 8px;
}

.placeholder-icon {
  font-size: 32px;
  color: #c0c4cc;
  transition: color 0.3s;
}

.image-preview:hover .placeholder-icon {
  color: #409eff;
}

.image-actions {
  display: flex;
  gap: 8px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
  line-height: 1.4;
}

.input-suffix {
  margin-left: 8px;
  color: #999;
  font-size: 14px;
}


:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-divider__text) {
  font-weight: bold;
  color: #409eff;
}
</style>
