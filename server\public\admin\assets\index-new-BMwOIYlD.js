var e=Object.defineProperty,l=Object.defineProperties,a=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,o=(l,a,r)=>a in l?e(l,a,{enumerable:!0,configurable:!0,writable:!0,value:r}):l[a]=r;import{C as p}from"./index-CS_UjWPV.js";import{R as s}from"./roleApi-DTgIzzjr.js";import{m as n,r as d,P as m,D as c,x as b,G as u,u as f,a6 as y,W as g,R as j,aY as h,ak as v,F as x,E as w,p as _,aQ as k}from"./vendor-8T3zXQLl.js";import{_ as C}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-Wowen7jt.js";import"./index-1JOyfOxu.js";import"./index-DdiMqMK0.js";import"./index-Zs-Thxwm.js";/* empty css                   *//* empty css                      *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import"./index-B5FHwYQA.js";/* empty css                *//* empty css               *//* empty css                  *//* empty css               */import"./el-form-item-l0sNRNKZ.js";/* empty css                       *//* empty css                 */import"./index-DS_-tevW.js";import"./index-MG-hoaSG.js";/* empty css                  *//* empty css                    */const O={class:"permission-tree"},R={class:"dialog-footer"},N=n((P=((e,l)=>{for(var a in l||(l={}))t.call(l,a)&&o(e,a,l[a]);if(r)for(var a of r(l))i.call(l,a)&&o(e,a,l[a]);return e})({},{name:"RoleListNew"}),l(P,a({__name:"index-new",setup(e){const l=d(!1),a=d(),r=d(null),t=d([]),i=d([{id:"1",label:"系统管理",children:[{id:"1-1",label:"用户管理"},{id:"1-2",label:"角色管理"},{id:"1-3",label:"菜单管理"}]},{id:"2",label:"内容管理",children:[{id:"2-1",label:"文章管理"},{id:"2-2",label:"分类管理"}]}]),o={children:"children",label:"label"},n={api:{list:s.getRoleList,create:e=>s.createRole(e),update:(e,l)=>s.updateRole(Number(e),l),delete:e=>s.deleteRole(Number(e))},columns:[{type:"selection",width:50},{type:"index",label:"序号",width:80},{prop:"roleName",label:"角色名称",width:150},{prop:"roleCode",label:"角色编码",width:150},{prop:"des",label:"描述",minWidth:200},{prop:"enable",label:"状态",width:100,formatter:e=>_(k,{type:e.enable?"primary":"info"},(()=>e.enable?"启用":"禁用"))},{prop:"date",label:"创建时间",width:180,formatter:e=>{return l=e.date,new Date(l).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}).replace(/\//g,"-");var l}},{prop:"operation",label:"操作",width:200,fixed:"right",formatter:e=>_("div",{class:"flex gap-2"},[_(y,{type:"primary",size:"small",link:!0,onClick:()=>z(e)},(()=>"权限设置")),_(y,{type:"primary",size:"small",link:!0,onClick:()=>D(e)},(()=>"编辑")),_(y,{type:"danger",size:"small",link:!0,onClick:()=>L(e)},(()=>"删除"))])}],search:{enabled:!0,defaultParams:{roleName:"",roleCode:"",enable:""},items:[{label:"角色名称",prop:"roleName",type:"input",config:{clearable:!0}},{label:"角色编码",prop:"roleCode",type:"input",config:{clearable:!0}},{label:"状态",prop:"enable",type:"select",config:{clearable:!0},options:()=>[{label:"启用",value:1},{label:"禁用",value:0}]}]},actions:{enabled:!1,create:{enabled:!0,text:"新增角色",type:"primary"}},dialog:{enabled:!0,width:"600px",titles:{create:"新增角色",edit:"编辑角色"},formConfig:[{prop:"roleName",label:"角色名称",type:"input",required:!0,span:12,rules:[{min:2,max:20,message:"长度在 2 到 20 个字符",trigger:"blur"}]},{prop:"roleCode",label:"角色编码",type:"input",required:!0,span:12,rules:[{pattern:/^[A-Z_]+$/,message:"只能包含大写字母和下划线",trigger:"blur"}]},{prop:"des",label:"描述",type:"textarea",span:24,config:{rows:3}},{prop:"enable",label:"状态",type:"switch",span:12}]},table:{rowKey:"id",stripe:!1,border:!1}},C=e=>{},N=(e,l)=>{},P=e=>{},z=e=>{r.value=e,t.value=e.permissions||[],l.value=!0},D=e=>{},L=e=>{},I=()=>{w.success("权限同步成功")},K=()=>{a.value.getCheckedKeys(),w.success("权限设置成功"),l.value=!1};return(e,r)=>(c(),m(x,null,[b(p,{config:n,onCreate:C,onUpdate:N,onDelete:P},{"header-actions":u((()=>[b(f(y),{onClick:I},{default:u((()=>r[2]||(r[2]=[g(" 同步权限 ")]))),_:1,__:[2]})])),_:1}),b(f(v),{modelValue:l.value,"onUpdate:modelValue":r[1]||(r[1]=e=>l.value=e),title:"权限设置",width:"800px","close-on-click-modal":!1},{footer:u((()=>[j("div",R,[b(f(y),{onClick:r[0]||(r[0]=e=>l.value=!1)},{default:u((()=>r[3]||(r[3]=[g("取消")]))),_:1,__:[3]}),b(f(y),{type:"primary",onClick:K},{default:u((()=>r[4]||(r[4]=[g("保存")]))),_:1,__:[4]})])])),default:u((()=>[j("div",O,[b(f(h),{ref_key:"permissionTreeRef",ref:a,data:i.value,props:o,"show-checkbox":"","node-key":"id","default-checked-keys":t.value,"default-expand-all":!0},null,8,["data","default-checked-keys"])])])),_:1},8,["modelValue"])],64))}}))));var P;const z=C(N,[["__scopeId","data-v-f3163b69"]]);export{z as default};
